Conditional function definition
-----
<?php

if (true) {
    function A() {}
}
-----
array(
    0: Stmt_If(
        cond: Expr_ConstFetch(
            name: Name(
                name: true
            )
        )
        stmts: array(
            0: Stmt_Function(
                attrGroups: array(
                )
                byRef: false
                name: Identifier(
                    name: A
                )
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
        )
        elseifs: array(
        )
        else: null
    )
)
