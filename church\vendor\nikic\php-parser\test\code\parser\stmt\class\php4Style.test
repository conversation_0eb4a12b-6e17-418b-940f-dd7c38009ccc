PHP 4 style declarations
-----
<?php

class A {
    var $foo;
    function bar() {}
    static abstract function baz() {}
}
-----
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_Property(
                attrGroups: array(
                )
                flags: 0
                type: null
                props: array(
                    0: PropertyItem(
                        name: VarLikeIdentifier(
                            name: foo
                        )
                        default: null
                    )
                )
                hooks: array(
                )
            )
            1: Stmt_ClassMethod(
                attrGroups: array(
                )
                flags: 0
                byRef: false
                name: Identifier(
                    name: bar
                )
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
            2: Stmt_ClassMethod(
                attrGroups: array(
                )
                flags: ABSTRACT | STATIC (24)
                byRef: false
                name: Identifier(
                    name: baz
                )
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
        )
    )
)
