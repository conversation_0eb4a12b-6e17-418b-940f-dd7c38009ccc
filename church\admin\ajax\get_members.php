<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Include the configuration file
require_once '../../config.php';

// Get parameters
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'full_name';
$order = isset($_GET['order']) ? $_GET['order'] : 'ASC';

// Validate sort field
$allowed_sort_fields = ['full_name', 'email', 'created_at'];
if (!in_array($sort, $allowed_sort_fields)) {
    $sort = 'full_name';
}

// Validate order
$order = strtoupper($order) === 'DESC' ? 'DESC' : 'ASC';

try {
    // Calculate offset
    $offset = ($page - 1) * $limit;
    
    // Get total count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM members");
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Get paginated results
    $stmt = $pdo->prepare("SELECT id, full_name, email, created_at FROM members ORDER BY $sort $order LIMIT :offset, :limit");
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get all members for bulk operations if requested
    $all_members = [];
    if (isset($_GET['get_all']) && $_GET['get_all'] === 'true') {
        $stmt = $pdo->query("SELECT id, full_name, email FROM members ORDER BY full_name");
        $all_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Calculate pagination info
    $total_pages = ceil($total / $limit);
    $pagination = [
        'current' => $page,
        'pages' => $total_pages,
        'start' => $offset + 1,
        'end' => min($offset + $limit, $total),
        'total' => $total
    ];
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'members' => $members,
        'all_members' => $all_members,
        'pagination' => $pagination
    ]);
    
} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} 