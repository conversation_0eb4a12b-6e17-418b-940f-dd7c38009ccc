<?php
/**
 * Enhanced Donation Notification System
 * 
 * Handles automatic email notifications for donations and gifts with sender and organization information
 */

require_once 'email_functions.php';

class EnhancedDonationNotifier {
    private $pdo;
    private $organization_name;
    private $organization_type;
    private $system_sender_name;
    private $system_sender_email;
    private $payment_settings;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->loadSettings();
    }
    
    private function loadSettings() {
        // Load organization settings
        $stmt = $this->pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('organization_name', 'organization_type', 'sender_name', 'sender_email')");
        $stmt->execute();
        $org_settings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $org_settings[$row['setting_key']] = $row['setting_value'];
        }
        
        $this->organization_name = $org_settings['organization_name'] ?? get_organization_name();
        $this->organization_type = $org_settings['organization_type'] ?? 'church';
        $this->system_sender_name = $org_settings['sender_name'] ?? $this->organization_name;
        $this->system_sender_email = $org_settings['sender_email'] ?? '<EMAIL>';
        
        // Load payment settings
        $stmt = $this->pdo->prepare("SELECT setting_key, setting_value FROM payment_settings");
        $stmt->execute();
        $this->payment_settings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->payment_settings[$row['setting_key']] = $row['setting_value'];
        }
    }
    
    /**
     * Send all notifications for a completed donation
     */
    public function sendDonationNotifications($donation_id) {
        try {
            // Get complete donation details
            $donation = $this->getDonationDetails($donation_id);
            if (!$donation) {
                throw new Exception("Donation not found");
            }
            
            $notifications_sent = [];
            
            // Send recipient notification for birthday gifts
            if ($donation['donation_type'] === 'birthday_gift' && $donation['recipient_email']) {
                $result = $this->sendRecipientNotification($donation);
                $notifications_sent['recipient'] = $result;
            }
            
            // Send donor confirmation
            $result = $this->sendDonorConfirmation($donation);
            $notifications_sent['donor'] = $result;
            
            // Send admin notification if configured
            if ($this->payment_settings['payment_notification_email']) {
                $result = $this->sendAdminNotification($donation);
                $notifications_sent['admin'] = $result;
            }
            
            // Update notification status
            $this->updateNotificationStatus($donation_id, true);
            
            return [
                'success' => true,
                'notifications_sent' => $notifications_sent
            ];
            
        } catch (Exception $e) {
            error_log("Error sending donation notifications: " . $e->getMessage());
            $this->updateNotificationStatus($donation_id, false);
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get complete donation details with recipient and template information
     */
    private function getDonationDetails($donation_id) {
        $stmt = $this->pdo->prepare("
            SELECT d.*, 
                   m.full_name as recipient_name, 
                   m.email as recipient_email, 
                   m.birth_date,
                   gt.template_name, 
                   gt.template_content,
                   gt.template_style
            FROM donations d 
            LEFT JOIN members m ON d.recipient_id = m.id 
            LEFT JOIN gift_templates gt ON d.gift_template_id = gt.id
            WHERE d.id = ?
        ");
        $stmt->execute([$donation_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Send birthday gift notification to recipient
     */
    private function sendRecipientNotification($donation) {
        $sender_display_name = $donation['anonymous_gift'] ? 'Anonymous' : $donation['donor_name'];
        $sender_org_info = $this->buildSenderOrgInfo($donation);
        
        $subject = "🎉 You've received a birthday gift" . $sender_org_info . "!";
        
        if ($donation['gift_type'] === 'digital_card' && $donation['template_content']) {
            $email_body = $this->processDigitalCardTemplate($donation, $sender_display_name);
        } else {
            $email_body = $this->buildDefaultBirthdayEmail($donation, $sender_display_name, $sender_org_info);
        }
        
        $result = sendEmailWithPHPMailer(
            $donation['recipient_email'],
            $subject,
            $email_body,
            $this->system_sender_name,
            $this->system_sender_email,
            true
        );
        
        // Log the notification
        $this->logNotification($donation['id'], 'recipient_notification', $donation['recipient_email'], $result['success']);
        
        return $result;
    }
    
    /**
     * Process digital card template with placeholders
     */
    private function processDigitalCardTemplate($donation, $sender_display_name) {
        $email_body = $donation['template_content'];
        
        // Replace placeholders
        $placeholders = [
            '{celebrant_name}' => $donation['recipient_name'],
            '{sender_name}' => $sender_display_name,
            '{sender_message}' => $donation['sender_message'] ?: 'Wishing you a wonderful birthday!',
            '{organization_name}' => $this->organization_name,
            '{gift_amount}' => $donation['currency'] . ' ' . number_format($donation['amount'], 2),
            '{current_date}' => date('F j, Y'),
            '{current_year}' => date('Y')
        ];
        
        foreach ($placeholders as $placeholder => $value) {
            $email_body = str_replace($placeholder, htmlspecialchars($value), $email_body);
        }
        
        // Wrap in email container if not already wrapped
        if (strpos($email_body, '<html>') === false) {
            $email_body = $this->wrapInEmailContainer($email_body, $donation['template_style'] ?? '');
        }
        
        return $email_body;
    }
    
    /**
     * Build default birthday email
     */
    private function buildDefaultBirthdayEmail($donation, $sender_display_name, $sender_org_info) {
        $email_body = "
        <div style='max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; border-radius: 10px;'>
            <div style='text-align: center; margin-bottom: 30px;'>
                <h1 style='color: #007bff; margin-bottom: 10px;'>🎉 Happy Birthday!</h1>
                <h2 style='color: #333; margin-bottom: 20px;'>Dear " . htmlspecialchars($donation['recipient_name']) . ",</h2>
            </div>
            
            <div style='background: white; padding: 25px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #007bff;'>
                <p style='font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 15px;'>
                    You've received a special birthday gift" . $sender_org_info . "!
                </p>";
        
        if ($donation['gift_type'] === 'monetary') {
            $email_body .= "
                <div style='background: #e7f3ff; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;'>
                    <h3 style='color: #007bff; margin: 0;'>Gift Amount: " . $donation['currency'] . " " . number_format($donation['amount'], 2) . "</h3>
                </div>";
        }
        
        if ($donation['sender_message']) {
            $email_body .= "
                <div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 3px solid #28a745;'>
                    <p style='font-style: italic; color: #555; margin: 0;'>\"" . htmlspecialchars($donation['sender_message']) . "\"</p>
                </div>";
        }
        
        $email_body .= "
                <p style='color: #666; margin-top: 20px;'>
                    With warm birthday wishes" . ($donation['anonymous_gift'] ? "" : " from <strong>" . htmlspecialchars($sender_display_name) . "</strong>") . "
                </p>
            </div>
            
            <div style='text-align: center; color: #888; font-size: 14px;'>
                <p>This gift was sent through " . htmlspecialchars($this->organization_name) . "</p>
            </div>
        </div>";
        
        return $email_body;
    }
    
    /**
     * Send confirmation email to donor
     */
    private function sendDonorConfirmation($donation) {
        $subject = $donation['donation_type'] === 'birthday_gift' ? 
            "Birthday Gift Confirmation - " . $this->organization_name : 
            "Donation Confirmation - " . $this->organization_name;
        
        $email_body = $this->buildDonorConfirmationEmail($donation);
        
        $result = sendEmailWithPHPMailer(
            $donation['donor_email'],
            $subject,
            $email_body,
            $this->system_sender_name,
            $this->system_sender_email,
            true
        );
        
        $this->logNotification($donation['id'], 'donor_confirmation', $donation['donor_email'], $result['success']);
        
        return $result;
    }
    
    /**
     * Build donor confirmation email
     */
    private function buildDonorConfirmationEmail($donation) {
        $email_body = "
        <div style='max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; padding: 20px;'>
            <div style='text-align: center; margin-bottom: 30px;'>
                <h1 style='color: #28a745;'>Thank You!</h1>
                <p style='font-size: 18px; color: #666;'>Your " . ($donation['donation_type'] === 'birthday_gift' ? 'birthday gift' : 'donation') . " has been processed successfully.</p>
            </div>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                <h3 style='color: #333; margin-top: 0;'>Transaction Details</h3>
                <p><strong>Amount:</strong> " . $donation['currency'] . " " . number_format($donation['amount'], 2) . "</p>
                <p><strong>Transaction ID:</strong> " . $donation['id'] . "</p>
                <p><strong>Date:</strong> " . date('F j, Y g:i A') . "</p>";
        
        if ($donation['donation_type'] === 'birthday_gift') {
            $email_body .= "
                <p><strong>Recipient:</strong> " . htmlspecialchars($donation['recipient_name']) . "</p>
                <p><strong>Gift Type:</strong> " . ucfirst(str_replace('_', ' ', $donation['gift_type'])) . "</p>";
            
            if ($donation['delivery_method'] === 'scheduled') {
                $email_body .= "<p><strong>Delivery:</strong> Scheduled for " . ($donation['scheduled_delivery_date'] ?: 'birthday') . "</p>";
            } else {
                $email_body .= "<p><strong>Delivery:</strong> Immediate</p>";
            }
        }
        
        $email_body .= "
            </div>
            
            <div style='text-align: center; margin-top: 30px;'>
                <p style='color: #666;'>Thank you for your generous support of " . htmlspecialchars($this->organization_name) . "!</p>
            </div>
        </div>";
        
        return $email_body;
    }
    
    /**
     * Send admin notification
     */
    private function sendAdminNotification($donation) {
        $subject = "New " . ($donation['donation_type'] === 'birthday_gift' ? 'Birthday Gift' : 'Donation') . " - " . $this->organization_name;
        
        $email_body = "
        <div style='max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; padding: 20px;'>
            <h2 style='color: #007bff;'>New " . ($donation['donation_type'] === 'birthday_gift' ? 'Birthday Gift' : 'Donation') . " Received</h2>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 8px;'>
                <p><strong>Donor:</strong> " . htmlspecialchars($donation['donor_name']) . " (" . $donation['donor_email'] . ")</p>
                <p><strong>Amount:</strong> " . $donation['currency'] . " " . number_format($donation['amount'], 2) . "</p>";
        
        if ($donation['sender_organization']) {
            $email_body .= "<p><strong>Organization:</strong> " . htmlspecialchars($donation['sender_organization']);
            if ($donation['sender_department']) {
                $email_body .= " (" . htmlspecialchars($donation['sender_department']) . ")";
            }
            $email_body .= "</p>";
        }
        
        if ($donation['donation_type'] === 'birthday_gift') {
            $email_body .= "<p><strong>Recipient:</strong> " . htmlspecialchars($donation['recipient_name']) . "</p>";
            $email_body .= "<p><strong>Gift Type:</strong> " . ucfirst(str_replace('_', ' ', $donation['gift_type'])) . "</p>";
        }
        
        $email_body .= "
                <p><strong>Transaction ID:</strong> " . $donation['id'] . "</p>
                <p><strong>Date:</strong> " . date('F j, Y g:i A') . "</p>
            </div>
        </div>";
        
        $result = sendEmailWithPHPMailer(
            $this->payment_settings['payment_notification_email'],
            $subject,
            $email_body,
            $this->system_sender_name,
            $this->system_sender_email,
            true
        );
        
        $this->logNotification($donation['id'], 'admin_notification', $this->payment_settings['payment_notification_email'], $result['success']);
        
        return $result;
    }
    
    /**
     * Build sender organization information string
     */
    private function buildSenderOrgInfo($donation) {
        if ($donation['anonymous_gift']) {
            return '';
        }
        
        $org_info = '';
        if ($donation['sender_organization']) {
            $org_info = " from " . $donation['sender_organization'];
            if ($donation['sender_department']) {
                $org_info .= " (" . $donation['sender_department'] . ")";
            }
        }
        
        return $org_info;
    }
    
    /**
     * Wrap content in email container
     */
    private function wrapInEmailContainer($content, $style = '') {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Gift Notification</title>
            <style>
                body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background-color: #f4f4f4; }
                .email-container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; }
                " . $style . "
            </style>
        </head>
        <body>
            <div class='email-container'>
                " . $content . "
            </div>
        </body>
        </html>";
    }
    
    /**
     * Log notification attempt
     */
    private function logNotification($donation_id, $type, $recipient_email, $success) {
        try {
            $stmt = $this->pdo->prepare("INSERT INTO donation_notifications (
                donation_id, notification_type, recipient_email, sent_at, status
            ) VALUES (?, ?, ?, NOW(), ?)");
            $stmt->execute([$donation_id, $type, $recipient_email, $success ? 'sent' : 'failed']);
        } catch (Exception $e) {
            error_log("Error logging notification: " . $e->getMessage());
        }
    }
    
    /**
     * Update overall notification status for donation
     */
    private function updateNotificationStatus($donation_id, $success) {
        try {
            $stmt = $this->pdo->prepare("UPDATE donations SET notification_sent = ?, notification_sent_at = NOW() WHERE id = ?");
            $stmt->execute([$success ? 1 : 0, $donation_id]);
        } catch (Exception $e) {
            error_log("Error updating notification status: " . $e->getMessage());
        }
    }
}

?>
