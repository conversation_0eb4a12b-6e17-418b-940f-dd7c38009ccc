Assigning new by reference (PHP 5 only)
-----
<?php
$a =& new B;
-----
!!version=5.6
array(
    0: Stmt_Expression(
        expr: Expr_AssignRef(
            var: Expr_Variable(
                name: a
            )
            expr: Expr_New(
                class: Name(
                    name: B
                )
                args: array(
                )
            )
        )
    )
)
-----
<?php
$a =& new B;
-----
!!version=7.0
Cannot assign new by reference from 2:1 to 2:11
array(
    0: Stmt_Expression(
        expr: Expr_AssignRef(
            var: Expr_Variable(
                name: a
            )
            expr: Expr_New(
                class: Name(
                    name: B
                )
                args: array(
                )
            )
        )
    )
)
