<?php
// Include config to access organization functions
if (!function_exists('get_organization_name')) {
    require_once 'config.php';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Error - <?php echo htmlspecialchars(get_organization_name()); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Arial', sans-serif;
        }
        .error-container {
            max-width: 600px;
            margin: 5rem auto;
            padding: 2rem;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .error-code {
            font-size: 120px;
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 0;
            line-height: 1;
        }
        .error-title {
            font-size: 24px;
            color: #343a40;
            margin-bottom: 20px;
        }
        .error-message {
            color: #6c757d;
            margin-bottom: 30px;
        }
        .back-button {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <h1 class="error-code">500</h1>
            <h2 class="error-title">Server Error</h2>
            <p class="error-message">
                We're sorry, but something went wrong on our end. Our team has been notified and is working to fix the issue.
            </p>
            <div class="d-flex justify-content-center gap-3">
                <a href="javascript:history.back()" class="btn btn-outline-secondary">Go Back</a>
                <a href="<?php echo isset($base_url) ? $base_url : '/'; ?>" class="btn btn-primary">Return to Homepage</a>
            </div>
            <div class="mt-4 text-muted small">
                <p>If you continue to experience problems, please contact the church office.</p>
            </div>
        </div>
    </div>
</body>
</html> 