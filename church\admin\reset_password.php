<?php
// Include the configuration file
require_once '../config.php';

// Include session manager which will handle starting the session
require_once 'includes/session-manager.php';

// If already logged in, redirect to admin dashboard
if (isset($_SESSION['admin_id'])) {
    header("Location: dashboard.php");
    exit();
}

// Include the SecurityManager class
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize SecurityManager
$security = new SecurityManager($conn);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';
$token = '';
$validToken = false;
$admin = null;

// Check if token is provided
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = $security->sanitizeInput($_GET['token'], 'text');
    
    // For debugging
    error_log("Received token: " . $token);
    
    // First check if the token exists at all, regardless of expiration
    $stmt = $conn->prepare("
        SELECT id, username, full_name, password_reset_expires
        FROM admins 
        WHERE password_reset_token = ?
    ");
    $stmt->execute([$token]);
    $admin = $stmt->fetch();
    
    if ($admin) {
        // Token exists, now check if it's expired
        $expiryTime = strtotime($admin['password_reset_expires']);
        $currentTime = time();
        
        error_log("Token found for user: " . $admin['username'] . 
                 ", Expires at: " . $admin['password_reset_expires'] . 
                 ", Current server time: " . date('Y-m-d H:i:s', $currentTime));
        
        // Give a 30-minute grace period beyond the official expiry time
        if ($currentTime <= ($expiryTime + 1800)) {
            $validToken = true;
            error_log("Valid token for user: " . $admin['username']);
        } else {
            error_log("Token expired for user: " . $admin['username'] . 
                     ", Expired at: " . $admin['password_reset_expires'] . 
                     ", Current time: " . date('Y-m-d H:i:s', $currentTime));
            $error = "Your password reset link has expired. Please request a new password reset.";
        }
    } else {
        error_log("Invalid token, not found in database");
        $error = "Invalid password reset token. Please request a new password reset.";
    }
} else {
    $error = "No password reset token provided. Please request a password reset.";
}

// Process password reset
if ($_SERVER["REQUEST_METHOD"] == "POST" && $validToken) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $password = $_POST['password'];
        $confirmPassword = $_POST['confirm_password'];
        
        // Validate password
        if ($password !== $confirmPassword) {
            $error = "Passwords do not match.";
        } elseif (!$security->validateInput($password, 'password')) {
            $error = "Password does not meet the requirements. Please use a stronger password.";
        } else {
            // Hash password
            $hashedPassword = $security->hashPassword($password);
            
            // Update password
            $stmt = $conn->prepare("
                UPDATE admins 
                SET password = ?, 
                    password_reset_token = NULL, 
                    password_reset_expires = NULL,
                    password_changed_at = NOW()
                WHERE id = ?
            ");
            
            if ($stmt->execute([$hashedPassword, $admin['id']])) {
                $success = "Your password has been reset successfully. You can now login with your new password.";
                
                // Log password reset
                $security->logSecurityEvent('Password reset completed', [
                    'admin_id' => $admin['id'],
                    'username' => $admin['username']
                ]);
                
                // Clear token from URL to prevent reuse
                $token = '';
                $validToken = false;
            } else {
                $error = "Failed to reset password. Please try again or contact an administrator.";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - <?php echo get_organization_name(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4f46e5;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --dark-color: #1f2937;
            --border-color: #e5e7eb;
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .reset-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 48px 40px;
            width: 100%;
            max-width: 440px;
            box-shadow: var(--shadow-xl);
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .reset-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .reset-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--success-color), #34d399);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-xl);
        }

        .reset-icon i {
            font-size: 32px;
            color: white;
        }

        .reset-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 8px;
        }

        .reset-subtitle {
            color: #6b7280;
            font-size: 16px;
        }

        .alert {
            border: none;
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 24px;
            font-weight: 500;
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .form-floating {
            margin-bottom: 24px;
        }

        .form-floating input {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-floating input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            background: white;
        }

        .form-floating label {
            color: #6b7280;
            font-weight: 500;
        }

        .btn-reset {
            background: linear-gradient(135deg, var(--success-color), #34d399);
            border: none;
            border-radius: 12px;
            padding: 16px 24px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .password-requirements {
            background: rgba(79, 70, 229, 0.05);
            border: 1px solid rgba(79, 70, 229, 0.1);
            border-radius: 8px;
            padding: 12px;
            margin-top: 16px;
            font-size: 14px;
            color: #6b7280;
        }

        .password-strength {
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            margin-top: 8px;
            overflow: hidden;
        }

        .password-strength-bar {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 3px;
        }

        .info-box {
            background: rgba(79, 70, 229, 0.05);
            border: 1px solid rgba(79, 70, 229, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <div class="reset-icon">
                <i class="fas fa-shield-check"></i>
            </div>
            <h1 class="reset-title">Reset Password</h1>
            <p class="reset-subtitle"><?php echo htmlspecialchars(get_organization_name()); ?></p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
            </div>
            <div class="text-center">
                <a href="forgot_password.php" class="btn btn-reset">
                    <i class="fas fa-redo me-2"></i>
                    Request New Reset
                </a>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success; ?>
            </div>
            <div class="text-center">
                <a href="login.php" class="btn btn-reset">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Go to Login
                </a>
            </div>
        <?php elseif ($validToken): ?>
            <div class="info-box">
                <p>
                    <i class="fas fa-info-circle me-2"></i>
                    Please enter your new password below. Make sure it's strong and secure.
                </p>
            </div>

            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?token=' . urlencode($token)); ?>" id="resetForm">
                <?php echo $security->generateCSRFInput(); ?>

                <div class="form-floating">
                    <input type="password" class="form-control" id="password" name="password" placeholder="New Password" required>
                    <label for="password">
                        <i class="fas fa-lock me-2"></i>New Password
                    </label>
                    <div class="password-strength">
                        <div class="password-strength-bar" id="strengthBar"></div>
                    </div>
                    <div class="password-requirements">
                        <strong>Password Requirements:</strong>
                        <ul>
                            <li>At least 8 characters long</li>
                            <li>Include uppercase and lowercase letters</li>
                            <li>Include numbers and special characters</li>
                        </ul>
                    </div>
                </div>

                <div class="form-floating">
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm Password" required>
                    <label for="confirm_password">
                        <i class="fas fa-lock me-2"></i>Confirm New Password
                    </label>
                </div>

                <button type="submit" class="btn-reset" id="resetBtn">
                    <i class="fas fa-shield-check me-2"></i>
                    Reset Password
                </button>
            </form>
        <?php endif; ?>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            const confirmInput = document.getElementById('confirm_password');
            const strengthBar = document.getElementById('strengthBar');
            const resetForm = document.getElementById('resetForm');
            const resetBtn = document.getElementById('resetBtn');

            // Password strength meter
            if (passwordInput && strengthBar) {
                passwordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;

                    // Length check
                    if (password.length >= 8) strength += 25;

                    // Character type checks
                    if (/[A-Z]/.test(password)) strength += 25;
                    if (/[a-z]/.test(password)) strength += 25;
                    if (/[0-9]/.test(password)) strength += 12.5;
                    if (/[^A-Za-z0-9]/.test(password)) strength += 12.5;

                    // Update strength meter
                    strengthBar.style.width = strength + '%';

                    // Set color based on strength
                    if (strength < 50) {
                        strengthBar.style.backgroundColor = '#ef4444'; // red
                        strengthBar.style.boxShadow = '0 0 10px rgba(239, 68, 68, 0.3)';
                    } else if (strength < 75) {
                        strengthBar.style.backgroundColor = '#f59e0b'; // yellow
                        strengthBar.style.boxShadow = '0 0 10px rgba(245, 158, 11, 0.3)';
                    } else {
                        strengthBar.style.backgroundColor = '#10b981'; // green
                        strengthBar.style.boxShadow = '0 0 10px rgba(16, 185, 129, 0.3)';
                    }
                });
            }

            // Password confirmation validation
            if (confirmInput) {
                confirmInput.addEventListener('input', function() {
                    const password = passwordInput.value;
                    const confirm = this.value;

                    if (confirm && password !== confirm) {
                        this.style.borderColor = '#ef4444';
                        this.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
                    } else if (confirm && password === confirm) {
                        this.style.borderColor = '#10b981';
                        this.style.boxShadow = '0 0 0 3px rgba(16, 185, 129, 0.1)';
                    } else {
                        this.style.borderColor = '#e5e7eb';
                        this.style.boxShadow = 'none';
                    }
                });
            }

            // Form submission with loading state
            if (resetForm && resetBtn) {
                resetForm.addEventListener('submit', function(e) {
                    const password = passwordInput.value;
                    const confirm = confirmInput.value;

                    if (password !== confirm) {
                        e.preventDefault();
                        alert('Passwords do not match. Please try again.');
                        return;
                    }

                    resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Resetting...';
                    resetBtn.disabled = true;

                    // Re-enable button after 10 seconds as fallback
                    setTimeout(() => {
                        resetBtn.innerHTML = '<i class="fas fa-shield-check me-2"></i>Reset Password';
                        resetBtn.disabled = false;
                    }, 10000);
                });
            }

            // Enhanced input focus effects
            const inputs = document.querySelectorAll('.form-floating input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                    this.parentElement.style.transition = 'transform 0.3s ease';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });

            // Auto-focus password field
            if (passwordInput) {
                passwordInput.focus();
            }
        });
    </script>
</body>
</html> 