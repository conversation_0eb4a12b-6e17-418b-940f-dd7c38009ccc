<?php
/**
 * Database Migration for Promotional Materials Enhancement
 * 
 * This script updates the event_files table to support promotional materials
 * and header banners for events.
 */

session_start();

// Include configuration
require_once '../config.php';
require_once 'includes/auth_check.php';

// Set page variables
$page_title = "Promotional Materials Migration";
$page_header = "Database Migration";
$page_description = "Updating database schema for promotional materials support";

// Include header
include 'includes/header.php';

$migration_results = [];
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    try {
        $pdo->beginTransaction();
        
        // Check if columns already exist
        $stmt = $pdo->query("DESCRIBE event_files");
        $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Add new columns if they don't exist
        if (!in_array('file_category', $existing_columns)) {
            $pdo->exec("ALTER TABLE event_files ADD COLUMN file_category ENUM('promotional', 'document', 'other') DEFAULT 'document' AFTER upload_date");
            $migration_results[] = "✓ Added file_category column";
        } else {
            $migration_results[] = "• file_category column already exists";
        }
        
        if (!in_array('is_header_banner', $existing_columns)) {
            $pdo->exec("ALTER TABLE event_files ADD COLUMN is_header_banner TINYINT(1) DEFAULT 0 AFTER file_category");
            $migration_results[] = "✓ Added is_header_banner column";
        } else {
            $migration_results[] = "• is_header_banner column already exists";
        }
        
        if (!in_array('display_order', $existing_columns)) {
            $pdo->exec("ALTER TABLE event_files ADD COLUMN display_order INT(11) DEFAULT 0 AFTER is_header_banner");
            $migration_results[] = "✓ Added display_order column";
        } else {
            $migration_results[] = "• display_order column already exists";
        }
        
        if (!in_array('alt_text', $existing_columns)) {
            $pdo->exec("ALTER TABLE event_files ADD COLUMN alt_text VARCHAR(255) DEFAULT NULL AFTER display_order");
            $migration_results[] = "✓ Added alt_text column";
        } else {
            $migration_results[] = "• alt_text column already exists";
        }
        
        // Add indexes if they don't exist
        try {
            $pdo->exec("ALTER TABLE event_files ADD INDEX idx_file_category (file_category)");
            $migration_results[] = "✓ Added file_category index";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                throw $e;
            }
            $migration_results[] = "• file_category index already exists";
        }
        
        try {
            $pdo->exec("ALTER TABLE event_files ADD INDEX idx_is_header_banner (is_header_banner)");
            $migration_results[] = "✓ Added is_header_banner index";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                throw $e;
            }
            $migration_results[] = "• is_header_banner index already exists";
        }
        
        // Create uploads directory structure
        $upload_dirs = [
            '../uploads/events/promotional/',
            '../uploads/events/banners/',
            '../uploads/events/thumbnails/'
        ];
        
        foreach ($upload_dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
                $migration_results[] = "✓ Created directory: " . $dir;
            } else {
                $migration_results[] = "• Directory already exists: " . $dir;
            }
        }
        
        $pdo->commit();
        $migration_results[] = "✅ Migration completed successfully!";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $errors[] = "Migration failed: " . $e->getMessage();
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-database-gear"></i> Promotional Materials Migration
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h6>Migration Errors:</h6>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($migration_results)): ?>
                        <div class="alert alert-info">
                            <h6>Migration Results:</h6>
                            <ul class="mb-0">
                                <?php foreach ($migration_results as $result): ?>
                                    <li><?php echo htmlspecialchars($result); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <h6>What this migration does:</h6>
                            <ul>
                                <li><strong>Enhances event_files table</strong> to support promotional materials</li>
                                <li><strong>Adds file_category field</strong> to categorize files (promotional, document, other)</li>
                                <li><strong>Adds is_header_banner field</strong> to designate header banners</li>
                                <li><strong>Adds display_order field</strong> for file ordering</li>
                                <li><strong>Adds alt_text field</strong> for accessibility</li>
                                <li><strong>Creates directory structure</strong> for organized file storage</li>
                                <li><strong>Adds database indexes</strong> for improved performance</li>
                            </ul>
                            
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>Important:</strong> This migration is safe and will not affect existing data. 
                                It only adds new columns and directories.
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6>Ready to migrate?</h6>
                                    <p class="small text-muted">Click the button below to run the migration</p>
                                    
                                    <form method="POST">
                                        <button type="submit" name="run_migration" class="btn btn-primary">
                                            <i class="bi bi-play-circle"></i> Run Migration
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
