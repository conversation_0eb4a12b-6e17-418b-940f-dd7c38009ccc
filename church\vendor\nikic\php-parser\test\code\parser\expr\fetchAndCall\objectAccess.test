Object access
-----
<?php

// property fetch variations
$a->b;
$a->b['c'];

// method call variations
$a->b();
$a->{'b'}();
$a->$b();
$a->$b['c']();

// array dereferencing
$a->b()['c'];
-----
array(
    0: Stmt_Expression(
        expr: Expr_PropertyFetch(
            var: Expr_Variable(
                name: a
            )
            name: Identifier(
                name: b
            )
        )
        comments: array(
            0: // property fetch variations
        )
    )
    1: Stmt_Expression(
        expr: Expr_ArrayDimFetch(
            var: Expr_PropertyFetch(
                var: Expr_Variable(
                    name: a
                )
                name: Identifier(
                    name: b
                )
            )
            dim: Scalar_String(
                value: c
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Expr_Variable(
                name: a
            )
            name: Identifier(
                name: b
            )
            args: array(
            )
        )
        comments: array(
            0: // method call variations
        )
    )
    3: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Expr_Variable(
                name: a
            )
            name: Scalar_String(
                value: b
            )
            args: array(
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Expr_Variable(
                name: a
            )
            name: Expr_Variable(
                name: b
            )
            args: array(
            )
        )
    )
    5: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_ArrayDimFetch(
                var: Expr_PropertyFetch(
                    var: Expr_Variable(
                        name: a
                    )
                    name: Expr_Variable(
                        name: b
                    )
                )
                dim: Scalar_String(
                    value: c
                )
            )
            args: array(
            )
        )
    )
    6: Stmt_Expression(
        expr: Expr_ArrayDimFetch(
            var: Expr_MethodCall(
                var: Expr_Variable(
                    name: a
                )
                name: Identifier(
                    name: b
                )
                args: array(
                )
            )
            dim: Scalar_String(
                value: c
            )
        )
        comments: array(
            0: // array dereferencing
        )
    )
)
