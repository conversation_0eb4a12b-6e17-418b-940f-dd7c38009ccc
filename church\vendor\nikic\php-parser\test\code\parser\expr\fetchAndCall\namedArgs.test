Named arguments
-----
<?php
foo(a: $b, c: $d);
bar(class: 0);
-----
array(
    0: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: foo
            )
            args: array(
                0: Arg(
                    name: Identifier(
                        name: a
                    )
                    value: Expr_Variable(
                        name: b
                    )
                    byRef: false
                    unpack: false
                )
                1: Arg(
                    name: Identifier(
                        name: c
                    )
                    value: Expr_Variable(
                        name: d
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: bar
            )
            args: array(
                0: Arg(
                    name: Identifier(
                        name: class
                    )
                    value: Scalar_Int(
                        value: 0
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
)
