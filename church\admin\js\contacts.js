/**
 * Church Admin - Contact Management JavaScript
 * 
 * This file contains functions for handling contact data in the admin panel,
 * including pagination, selection, and group management.
 */

// Simplified global variables - removed AJAX state management

// Process contact groups to ensure uniqueness
window.renderGroups = function(groups) {
    if (!groups || groups.length === 0) {
        return '<span class="badge bg-secondary">No Groups</span>';
    }
    
    const uniqueGroups = [...new Set(groups)];
    const maxDisplay = 3;
    
    let html = '<div class="group-badges-container">';
    const displayGroups = uniqueGroups.slice(0, maxDisplay);
    
    displayGroups.forEach(group => {
        html += `<span class="badge bg-info me-1">${escapeHtml(group)}</span>`;
    });
    
    if (uniqueGroups.length > maxDisplay) {
        html += `<span class="badge bg-secondary">+${uniqueGroups.length - maxDisplay} more</span>`;
    }
    
    html += '</div>';
    return html;
}

// Helper function to escape HTML
window.escapeHtml = function(text) {
    if (!text) return '';
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, m => map[m]);
}

// Notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Removed complex AJAX loadContacts function - using server-side rendering instead

// Update contacts table with data
function updateContactsTable(contacts) {
    const tbody = document.querySelector('#contactsTable tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    contacts.forEach((contact, index) => {
        const isSelected = selectedContactIds.includes(contact.id.toString());
        
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input contact-checkbox" 
                       name="selected_contacts[]" value="${contact.id}" 
                       ${isSelected ? 'checked' : ''}>
            </td>
            <td>${escapeHtml(contact.name)}</td>
            <td>${escapeHtml(contact.email)}</td>
            <td>${renderGroups(contact.groups)}</td>
            <td>${new Date(contact.created_at).toLocaleDateString()}</td>
        `;
        tbody.appendChild(tr);
    });
    
    // Update "Select All" checkbox state
    updateSelectAllCheckbox();
}

// Update pagination controls
function updatePagination(pagination) {
    const paginationContainer = document.querySelector('#contactPagination');
    if (!paginationContainer) return;
    
    // Use global variables from bulk_email.php if available, otherwise use defaults
    const itemsPerPage = window.currentContactLimit || 10;
    const currentPage = window.currentContactPage || 1;

    const totalPages = Math.ceil(pagination.total / itemsPerPage);
    let html = '';
    
    // Previous button
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage - 1}">&laquo;</a>
        </li>
    `;
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (
            i === 1 || 
            i === totalPages || 
            (i >= currentPage - 2 && i <= currentPage + 2)
        ) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        } else if (
            i === currentPage - 3 || 
            i === currentPage + 3
        ) {
            html += `
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            `;
        }
    }
    
    // Next button
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage + 1}">&raquo;</a>
        </li>
    `;
    
    paginationContainer.innerHTML = html;
    
    // Add click handlers to pagination links
    paginationContainer.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.dataset.page);
            if (page && !isNaN(page)) {
                // Update global variables if they exist (bulk_email.php context)
                if (window.currentContactPage !== undefined) {
                    window.currentContactPage = page;
                    if (window.loadContacts) {
                        window.loadContacts();
                    }
                } else {
                    // Fallback for contacts.php context
                    currentPage = page;
                    loadContacts(page);
                }
            }
        });
    });
}

// Update selection info display
function updateSelectionInfo() {
    const infoElement = document.getElementById('contactSelectionInfo');
    if (!infoElement) return;

    // Use global variables from bulk_email.php if available, otherwise use defaults
    const selectedIds = window.selectedContactIds || [];
    const allContacts = window.allAvailableContacts || [];

    infoElement.innerHTML = `
        <span class="badge bg-primary">${selectedIds.length} contacts selected</span>
        <small class="text-muted ms-2">out of ${allContacts.length} total</small>
    `;
}

// Update "Select All" checkbox state
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAllContactsCheckbox');
    if (!selectAllCheckbox) return;
    
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    const checkedCount = document.querySelectorAll('.contact-checkbox:checked').length;
    
    selectAllCheckbox.checked = checkboxes.length > 0 && checkedCount === checkboxes.length;
    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing contacts page...');

    // Handle items per page change
    const limitSelect = document.getElementById('limitSelect');
    if (limitSelect) {
        console.log('Found limitSelect element, attaching event listener');
        limitSelect.addEventListener('change', function() {
            console.log('Limit select changed to:', this.value);
            const newLimit = parseInt(this.value);
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('limit', newLimit);
            currentUrl.searchParams.set('page', '1'); // Reset to first page
            console.log('Redirecting to:', currentUrl.toString());
            window.location.href = currentUrl.toString();
        });
    } else {
        console.log('limitSelect element not found');
    }

    // Handle group filter change
    const groupSelect = document.getElementById('sortGroupSelect');
    if (groupSelect) {
        console.log('Found sortGroupSelect element, attaching event listener');
        groupSelect.addEventListener('change', function() {
            console.log('Group select changed to:', this.value);
            const selectedGroupId = this.value;
            const currentUrl = new URL(window.location);
            if (selectedGroupId) {
                currentUrl.searchParams.set('group_id', selectedGroupId);
            } else {
                currentUrl.searchParams.delete('group_id');
            }
            currentUrl.searchParams.set('page', '1'); // Reset to first page
            console.log('Redirecting to:', currentUrl.toString());
            window.location.href = currentUrl.toString();
        });
    } else {
        console.log('sortGroupSelect element not found');
    }

    // Handle import functionality
    const importBtn = document.getElementById('startImport');
    if (importBtn) {
        importBtn.addEventListener('click', function() {
            const fileInput = document.getElementById('import_file');
            const skipDuplicates = document.getElementById('skip_duplicates');

            if (!fileInput.files.length) {
                alert('Please select a CSV file to import.');
                return;
            }

            const file = fileInput.files[0];
            if (file.size > 2 * 1024 * 1024) { // 2MB limit
                alert('File size must be less than 2MB.');
                return;
            }

            // Disable button and show loading
            importBtn.disabled = true;
            importBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Importing...';

            // Create FormData
            const formData = new FormData();
            formData.append('import_file', file);
            formData.append('skip_duplicates', skipDuplicates.checked ? '1' : '0');

            // Send import request
            fetch('ajax/import_contacts.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let message = `Import successful! ${data.imported_count} contacts imported.`;
                    if (data.skipped_count > 0) {
                        message += ` ${data.skipped_count} duplicates skipped.`;
                    }
                    showNotification(message, 'success');

                    // Show warnings if any
                    if (data.warnings && data.warnings.length > 0) {
                        showNotification('Some rows had issues: ' + data.warnings.slice(0, 3).join(', '), 'warning');
                    }

                    // Close modal and reload page
                    const modal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
                    modal.hide();
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification('Import failed: ' + (data.message || data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                console.error('Import error:', error);
                showNotification('Import failed. Please try again.', 'error');
            })
            .finally(() => {
                // Re-enable button
                importBtn.disabled = false;
                importBtn.innerHTML = '<i class="bi bi-upload me-2"></i>Import Contacts';
            });
        });
    }
});

/**
 * Edit Contact Functionality
 */
function initializeEditContactFunctionality() {
    console.log('Initializing edit contact functionality...');

    // Handle edit contact buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edit-contact')) {
            console.log('Edit contact button clicked!');
            e.preventDefault();
            const button = e.target.closest('.edit-contact');
            const contactId = button.getAttribute('data-id');
            const contactName = button.getAttribute('data-name');
            const contactEmail = button.getAttribute('data-email');

            console.log('Contact data:', { contactId, contactName, contactEmail });

            // Populate the edit modal
            document.getElementById('edit_contact_id').value = contactId;
            document.getElementById('edit_name').value = contactName;
            document.getElementById('edit_email').value = contactEmail;

            // Load current groups for this contact
            loadContactGroups(contactId);

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('editContactModal'));
            modal.show();
        }
    });

    // Handle save contact button click
    const saveContactBtn = document.getElementById('saveContact');
    console.log('Save contact button found:', saveContactBtn);

    if (saveContactBtn) {
        saveContactBtn.addEventListener('click', function(e) {
            console.log('Save contact button clicked!');
            e.preventDefault();

            const contactId = document.getElementById('edit_contact_id').value;
            const name = document.getElementById('edit_name').value.trim();
            const email = document.getElementById('edit_email').value.trim();

            console.log('Form data:', { contactId, name, email });

            // Get selected groups
            const selectedGroups = [];
            const groupCheckboxes = document.querySelectorAll('#editContactModal input[name="groups[]"]:checked');
            groupCheckboxes.forEach(checkbox => {
                selectedGroups.push(checkbox.value);
            });

            console.log('Selected groups:', selectedGroups);

            if (!name || !email) {
                alert('Please fill in all required fields.');
                return;
            }

            // Disable button and show loading
            saveContactBtn.disabled = true;
            saveContactBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Saving...';

            const updateData = {
                id: contactId,
                name: name,
                email: email,
                groups: selectedGroups
            };

            console.log('Sending update request:', updateData);

            // Send update request
            fetch('ajax/update_contact.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    alert('Contact updated successfully!');
                    // Close modal and reload page
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editContactModal'));
                    modal.hide();
                    location.reload();
                } else {
                    alert('Error updating contact: ' + (data.message || data.error || 'Unknown error'));
                    // Re-enable button
                    saveContactBtn.disabled = false;
                    saveContactBtn.innerHTML = 'Save Changes';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating contact. Please try again.');
                // Re-enable button
                saveContactBtn.disabled = false;
                saveContactBtn.innerHTML = 'Save Changes';
            });
        });
    } else {
        console.log('Save contact button not found!');
    }
}

// Initialize when DOM is ready or immediately if already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeEditContactFunctionality);
} else {
    initializeEditContactFunctionality();
}

// Function to load contact groups for editing
function loadContactGroups(contactId) {
    fetch(`ajax/get_contact_groups.php?id=${contactId}`)
        .then(response => response.json())
        .then(data => {
            // Uncheck all group checkboxes first
            const groupCheckboxes = document.querySelectorAll('#editContactModal input[name="groups[]"]');
            groupCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });

            // The response is directly an array of group IDs
            if (Array.isArray(data)) {
                data.forEach(groupId => {
                    const checkbox = document.querySelector(`#edit_group_${groupId}`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error loading contact groups:', error);
        });
}

/**
 * Bulk Delete Functionality for Contacts
 */
window.initializeBulkDelete = function() {
    let selectedContacts = [];

    // Get DOM elements
    const selectAllCheckbox = document.getElementById('selectAllContacts');
    const contactCheckboxes = document.querySelectorAll('.contact-checkbox');
    const bulkActionsContainer = document.getElementById('bulkActionsContainer');
    const selectedCountSpan = document.getElementById('selectedCount');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const clearSelectionBtn = document.getElementById('clearSelectionBtn');

    // Select All functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            contactCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateSelectedContacts();
        });
    }

    // Individual checkbox change
    contactCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedContacts);
    });

    // Update selected contacts array and UI
    function updateSelectedContacts() {
        selectedContacts = [];
        const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');

        checkedBoxes.forEach(checkbox => {
            selectedContacts.push({
                id: checkbox.value,
                name: checkbox.dataset.name,
                email: checkbox.dataset.email
            });
        });

        // Update UI
        if (selectedContacts.length > 0) {
            bulkActionsContainer.style.display = 'block';
            selectedCountSpan.textContent = selectedContacts.length;
        } else {
            bulkActionsContainer.style.display = 'none';
        }

        // Update select all checkbox state
        if (selectAllCheckbox) {
            if (selectedContacts.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (selectedContacts.length === contactCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        }
    }

    // Clear selection
    if (clearSelectionBtn) {
        clearSelectionBtn.addEventListener('click', function() {
            contactCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }
            updateSelectedContacts();
        });
    }

    // Bulk delete button click
    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', function() {
            if (selectedContacts.length === 0) {
                alert('Please select contacts to delete.');
                return;
            }

            // Populate modal with selected contacts
            const bulkDeleteCount = document.getElementById('bulkDeleteCount');
            const bulkDeletePreview = document.getElementById('bulkDeletePreview');

            if (bulkDeleteCount) {
                bulkDeleteCount.textContent = selectedContacts.length;
            }

            if (bulkDeletePreview) {
                let previewHtml = '<div class="row">';
                selectedContacts.forEach((contact, index) => {
                    if (index > 0 && index % 2 === 0) {
                        previewHtml += '</div><div class="row">';
                    }
                    previewHtml += `
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-circle me-2 text-muted"></i>
                                <div>
                                    <div class="fw-bold">${window.escapeHtml(contact.name)}</div>
                                    <small class="text-muted">${window.escapeHtml(contact.email)}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                previewHtml += '</div>';

                bulkDeletePreview.innerHTML = previewHtml;
            }

            // Show modal
            const bulkDeleteModal = document.getElementById('bulkDeleteModal');
            if (bulkDeleteModal) {
                const modal = new bootstrap.Modal(bulkDeleteModal);
                modal.show();
            }
        });
    }

    // Modal confirmation logic
    const confirmCheckbox = document.getElementById('confirmBulkDelete');
    const confirmTextInput = document.getElementById('bulkDeleteConfirmText');
    const confirmButton = document.getElementById('confirmBulkDeleteBtn');

    function updateConfirmButton() {
        const isChecked = confirmCheckbox && confirmCheckbox.checked;
        const isTextCorrect = confirmTextInput && confirmTextInput.value.toUpperCase() === 'DELETE';

        if (confirmButton) {
            confirmButton.disabled = !(isChecked && isTextCorrect);
        }
    }

    if (confirmCheckbox) {
        confirmCheckbox.addEventListener('change', updateConfirmButton);
    }

    if (confirmTextInput) {
        confirmTextInput.addEventListener('input', updateConfirmButton);
    }

    // Confirm bulk delete
    if (confirmButton) {
        confirmButton.addEventListener('click', function() {
            if (selectedContacts.length === 0) {
                alert('No contacts selected.');
                return;
            }

            // Disable button and show loading
            confirmButton.disabled = true;
            confirmButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Deleting...';

            // Prepare data
            const contactIds = selectedContacts.map(contact => contact.id);

            // Send AJAX request
            fetch('ajax/bulk_delete_contacts.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contact_ids: contactIds,
                    confirmation_token: 'BULK_DELETE_CONFIRMED'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showNotification(data.message, 'success');

                    // Show detailed results if available
                    if (data.results && data.results.failed_deletions && data.results.failed_deletions.length > 0) {
                        console.warn('Some deletions failed:', data.results.failed_deletions);
                        showNotification(data.warnings || 'Some contacts could not be deleted', 'warning');
                    }

                    // Close modal and reload page
                    const modal = bootstrap.Modal.getInstance(document.getElementById('bulkDeleteModal'));
                    if (modal) {
                        modal.hide();
                    }

                    // Reload page after short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);

                } else {
                    showNotification(data.message || 'Error occurred during bulk deletion', 'error');

                    // Re-enable button
                    confirmButton.disabled = false;
                    confirmButton.innerHTML = '<i class="bi bi-trash me-2"></i>Delete Selected Contacts';
                }
            })
            .catch(error => {
                console.error('Bulk delete error:', error);
                showNotification('Network error occurred during bulk deletion', 'error');

                // Re-enable button
                confirmButton.disabled = false;
                confirmButton.innerHTML = '<i class="bi bi-trash me-2"></i>Delete Selected Contacts';
            });
        });
    }

    // Reset modal when closed
    const bulkDeleteModal = document.getElementById('bulkDeleteModal');
    if (bulkDeleteModal) {
        bulkDeleteModal.addEventListener('hidden.bs.modal', function() {
            // Reset form
            if (confirmCheckbox) confirmCheckbox.checked = false;
            if (confirmTextInput) confirmTextInput.value = '';
            if (confirmButton) {
                confirmButton.disabled = true;
                confirmButton.innerHTML = '<i class="bi bi-trash me-2"></i>Delete Selected Contacts';
            }
        });
    }

    // Notification function
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
};