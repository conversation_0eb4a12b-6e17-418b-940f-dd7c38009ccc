Return and pass by ref
-----
<?php

function a(&$b) {}
function &a($b) {}
-----
array(
    0: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: a
        )
        params: array(
            0: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: true
                variadic: false
                var: Expr_Variable(
                    name: b
                )
                default: null
                hooks: array(
                )
            )
        )
        returnType: null
        stmts: array(
        )
    )
    1: Stmt_Function(
        attrGroups: array(
        )
        byRef: true
        name: Identifier(
            name: a
        )
        params: array(
            0: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: b
                )
                default: null
                hooks: array(
                )
            )
        )
        returnType: null
        stmts: array(
        )
    )
)
