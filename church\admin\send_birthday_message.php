<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    if (isAjaxRequest()) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Not authenticated']);
        exit;
    }
    header('Location: login.php');
    exit;
}

// Function to check if request is AJAX
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
}

// Enable error logging for debugging
error_log("Birthday message form submitted by admin ID: " . $_SESSION['admin_id']);
error_log("POST data: " . print_r($_POST, true));

// Set default response
$response = [
    'success' => false,
    'message' => 'An error occurred while sending the message.'
];

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate required fields
    $missing_fields = [];
    if (empty($_POST['member_id'])) $missing_fields[] = 'member_id';
    if (empty($_POST['template_id'])) $missing_fields[] = 'template_id';
    if (empty($_POST['subject'])) $missing_fields[] = 'subject';
    if (empty($_POST['recipient_email'])) $missing_fields[] = 'recipient_email';
    if (empty($_POST['recipient_name'])) $missing_fields[] = 'recipient_name';

    if (!empty($missing_fields)) {
        error_log("Birthday message validation failed. Missing fields: " . implode(', ', $missing_fields));
        error_log("POST data: " . print_r($_POST, true));
        $error_message = 'All required fields must be filled out. Missing: ' . implode(', ', $missing_fields);

        if (isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $error_message]);
            exit;
        }

        $_SESSION['error'] = $error_message;
        $redirect_to = $_POST['redirect_to'] ?? 'birthday';
        $redirect_page = ($redirect_to === 'dashboard') ? 'dashboard.php' : 'birthday.php';
        header("Location: $redirect_page");
        exit;
    }

    try {
        // Get form data
        $member_id = $_POST['member_id'];
        $template_id = $_POST['template_id'];
        $subject = $_POST['subject'];
        $custom_message = $_POST['custom_message'] ?? '';
        $recipient_email = $_POST['recipient_email'];
        $recipient_name = $_POST['recipient_name'];

        // If no template selected, try to get the first birthday template
        if (empty($template_id)) {
            $db = isset($pdo) ? $pdo : $conn;
            $stmt = $db->prepare("SELECT id FROM email_templates WHERE is_birthday_template = 1 LIMIT 1");
            $stmt->execute();
            $firstTemplate = $stmt->fetch();
            if ($firstTemplate) {
                $template_id = $firstTemplate['id'];
                error_log("No template selected, using first birthday template: " . $template_id);
            } else {
                $error_message = 'No birthday email templates found. Please create a birthday template first.';

                if (isAjaxRequest()) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => $error_message]);
                    exit;
                }

                $_SESSION['error'] = $error_message;
                $redirect_to = $_POST['redirect_to'] ?? 'birthday';
                $redirect_page = ($redirect_to === 'dashboard') ? 'dashboard.php' : 'birthday.php';
                header("Location: $redirect_page");
                exit;
            }
        }

        // Get the email template
        $db = isset($pdo) ? $pdo : $conn;
        $stmt = $db->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch();

        if (!$template) {
            $error_message = 'Email template not found.';

            if (isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => $error_message]);
                exit;
            }

            $_SESSION['error'] = $error_message;
            $redirect_to = $_POST['redirect_to'] ?? 'birthday';
            $redirect_page = ($redirect_to === 'dashboard') ? 'dashboard.php' : 'birthday.php';
            header("Location: $redirect_page");
            exit;
        }

        // Get member details for image path
        $stmt = $db->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();

        if (!$member) {
            // Create a basic member data array if not found
            $member = [
                'full_name' => $recipient_name,
                'email' => $recipient_email,
                'phone_number' => '',
                'image_path' => '' // No image available
            ];
        }

        // CRITICAL: Add birthday notification flags for proper email processing
        $member['_is_birthday_notification'] = true;
        $member['_skip_attachments'] = true;
        $member['_birthday_member_original_image_path'] = $member['image_path'] ?? null;

        // Set birthday member specific placeholders
        $member['birthday_member_name'] = $member['first_name'] ?? explode(' ', $member['full_name'] ?? '')[0];
        $member['birthday_member_full_name'] = $member['full_name'] ?? '';
        $member['birthday_member_email'] = $member['email'] ?? '';

        // ENHANCED LOGGING: Debug birthday notification data
        if (function_exists('logBirthdayNotificationDebug')) {
            logBirthdayNotificationDebug($member, 'admin/send_birthday_message.php - Before template replacement');
        }

        // Replace placeholders in the template using the standardized function
        $subject = replaceTemplatePlaceholders($template['subject'], $member);
        $body = replaceTemplatePlaceholders($template['content'], $member);
        
        // Add custom message if provided
        if (!empty($custom_message)) {
            $body = str_replace('{custom_message}', $custom_message, $body);
        } else {
            $body = str_replace('{custom_message}', '', $body);
        }

        // Send the email
        if (sendEmail($recipient_email, $recipient_name, $subject, $body, true, $member)) {
            // Log the email
            $stmt = $db->prepare("INSERT INTO email_logs (member_id, template_id, subject, status, sent_at) VALUES (?, ?, ?, 'success', NOW())");
            $stmt->execute([$member_id, $template_id, $subject]);

            // Generate a unique tracking ID
            $tracking_id = uniqid('track_', true);

            // Create tracking record
            $stmt = $db->prepare("INSERT INTO email_tracking (member_id, tracking_id, email_type, sent_at) VALUES (?, ?, 'birthday', NOW())");
            $stmt->execute([$member_id, $tracking_id]);

            $success_message = "Birthday message sent successfully to $recipient_name.";

            if (isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => $success_message]);
                exit;
            }

            $_SESSION['message'] = $success_message;
        } else {
            // Log the failed email
            global $last_email_error;
            $error_message = $last_email_error ?? 'Unknown error';

            $stmt = $db->prepare("INSERT INTO email_logs (member_id, template_id, subject, status, error_message, sent_at) VALUES (?, ?, ?, 'failed', ?, NOW())");
            $stmt->execute([$member_id, $template_id, $subject, $error_message]);

            $full_error_message = "Failed to send email. Error: $error_message";

            if (isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => $full_error_message]);
                exit;
            }

            $_SESSION['error'] = $full_error_message;
        }
    } catch (Exception $e) {
        $error_message = 'Error: ' . $e->getMessage();

        if (isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $error_message]);
            exit;
        }

        $_SESSION['error'] = $error_message;
    }
}

// Only redirect for non-AJAX requests
if (!isAjaxRequest()) {
    $redirect_to = $_POST['redirect_to'] ?? 'birthday';
    $redirect_page = ($redirect_to === 'dashboard') ? 'dashboard.php' : 'birthday.php';
    header("Location: $redirect_page");
    exit;
} else {
    // For AJAX requests that reach here without explicit response
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'No action taken']);
    exit;
}