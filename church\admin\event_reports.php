<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Handle print view generation
if (isset($_GET['action']) && $_GET['action'] === 'print_report' && isset($_GET['event_id'])) {
    $event_id = (int)$_GET['event_id'];
    generatePrintView($event_id);
    exit();
}

// Handle detailed attendance report generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'generate_detailed_report':
            $event_id = (int)$_POST['event_id'];

            if ($event_id) {
                // Redirect to show the detailed report on the same page
                header("Location: event_reports.php?view=detailed&event_id=" . $event_id);
                exit();
            } else {
                $error = "Please select an event to generate a detailed report.";
            }
            break;
    }
}

// Function to generate print view
function generatePrintView($event_id) {
    global $pdo;

    // Get event details
    $event_stmt = $pdo->prepare("
        SELECT id, title, event_date, location, description, max_attendees
        FROM events
        WHERE id = ?
    ");
    $event_stmt->execute([$event_id]);
    $event = $event_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$event) {
        echo "Event not found.";
        return;
    }

    // Get attendees data
    $attendees_data = getEventAttendeesData($event_id);

    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Attendance Report - <?= htmlspecialchars($event['title']) ?></title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                color: #333;
                line-height: 1.4;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }
            .header h1 {
                margin: 0;
                color: #333;
                font-size: 24px;
            }
            .event-details {
                background: #f8f9fa;
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 5px;
            }
            .stats {
                display: flex;
                justify-content: space-around;
                margin-bottom: 20px;
                text-align: center;
            }
            .stat-item {
                padding: 10px;
                background: #e9ecef;
                border-radius: 5px;
                min-width: 100px;
            }
            .stat-number {
                font-size: 24px;
                font-weight: bold;
                color: #007bff;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
                font-size: 12px;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            .member { background-color: #e8f5e8; }
            .guest { background-color: #fff3cd; }
            .attended { background-color: #d4edda; }
            .not-attended { background-color: #f8d7da; }
            @media print {
                body { margin: 0; }
                .no-print { display: none; }
                .stats { display: block; }
                .stat-item { display: inline-block; margin: 5px; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Event Attendance Report</h1>
            <h2><?= htmlspecialchars($event['title']) ?></h2>
            <p><strong>Date:</strong> <?= date('F j, Y \a\t g:i A', strtotime($event['event_date'])) ?></p>
            <p><strong>Location:</strong> <?= htmlspecialchars($event['location']) ?></p>
            <p><strong>Generated:</strong> <?= date('F j, Y \a\t g:i A') ?></p>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number"><?= $attendees_data['stats']['total'] ?></div>
                <div>Total RSVPs</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $attendees_data['stats']['members'] ?></div>
                <div>Members</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $attendees_data['stats']['guests'] ?></div>
                <div>Guests</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $attendees_data['stats']['attending'] ?></div>
                <div>Attending</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $attendees_data['stats']['actually_attended'] ?></div>
                <div>Actually Attended</div>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>RSVP Status</th>
                    <th>Actually Attended</th>
                    <th>RSVP Date</th>
                    <th>Notes</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($attendees_data['attendees'] as $attendee): ?>
                <tr class="<?= $attendee['attendee_type'] ?> <?= $attendee['actually_attended'] == 1 ? 'attended' : ($attendee['actually_attended'] === '0' ? 'not-attended' : '') ?>">
                    <td><?= htmlspecialchars($attendee['full_name']) ?></td>
                    <td><?= ucfirst($attendee['attendee_type']) ?></td>
                    <td><?= htmlspecialchars($attendee['email']) ?></td>
                    <td><?= htmlspecialchars($attendee['phone_number']) ?></td>
                    <td><?= ucfirst(str_replace('_', ' ', $attendee['status'])) ?></td>
                    <td>
                        <?php if ($attendee['actually_attended'] === null): ?>
                            Not Marked
                        <?php elseif ($attendee['actually_attended'] == 1): ?>
                            ✓ Yes
                        <?php else: ?>
                            ✗ No
                        <?php endif; ?>
                    </td>
                    <td><?= date('M j, Y g:i A', strtotime($attendee['rsvp_date'])) ?></td>
                    <td><?= htmlspecialchars($attendee['notes']) ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <script>
            window.onload = function() {
                window.print();
            };
        </script>
    </body>
    </html>
    <?php
}

// Function to get event attendees data
function getEventAttendeesData($event_id) {
    global $pdo;

    // Ensure the actually_attended column exists in both tables
    try {
        $pdo->exec("ALTER TABLE event_rsvps ADD COLUMN actually_attended TINYINT(1) DEFAULT NULL");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $pdo->exec("ALTER TABLE event_rsvps_guests ADD COLUMN actually_attended TINYINT(1) DEFAULT NULL");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    // Check if event_rsvps table uses user_id or member_id, and rsvp_date or created_at
    $columns_stmt = $pdo->query("SHOW COLUMNS FROM event_rsvps");
    $columns = $columns_stmt->fetchAll(PDO::FETCH_COLUMN);
    $member_id_column = in_array('user_id', $columns) ? 'user_id' : 'member_id';
    $date_column = in_array('rsvp_date', $columns) ? 'rsvp_date' : 'created_at';

    // Get member attendees
    $member_query = "
        SELECT
            er.id as rsvp_id,
            er.$member_id_column as member_id,
            er.status,
            er.notes,
            er.actually_attended,
            er.$date_column as rsvp_date,
            m.full_name,
            m.email,
            m.phone_number,
            'member' as attendee_type
        FROM event_rsvps er
        JOIN members m ON er.$member_id_column = m.id
        WHERE er.event_id = ?
        ORDER BY m.full_name ASC
    ";

    $member_stmt = $pdo->prepare($member_query);
    $member_stmt->execute([$event_id]);
    $member_attendees = $member_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get guest attendees
    $guest_query = "
        SELECT
            erg.id as rsvp_id,
            NULL as member_id,
            erg.status,
            erg.special_requirements as notes,
            erg.actually_attended,
            erg.created_at as rsvp_date,
            erg.guest_name as full_name,
            erg.guest_email as email,
            erg.guest_phone as phone_number,
            'guest' as attendee_type
        FROM event_rsvps_guests erg
        WHERE erg.event_id = ?
        ORDER BY erg.guest_name ASC
    ";

    $guest_stmt = $pdo->prepare($guest_query);
    $guest_stmt->execute([$event_id]);
    $guest_attendees = $guest_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Combine all attendees
    $all_attendees = array_merge($member_attendees, $guest_attendees);

    // Calculate statistics
    $total_attendees = count($all_attendees);
    $member_count = count($member_attendees);
    $guest_count = count($guest_attendees);
    $attending_count = count(array_filter($all_attendees, function($a) { return $a['status'] === 'attending'; }));
    $actually_attended_count = count(array_filter($all_attendees, function($a) { return $a['actually_attended'] == 1; }));

    return [
        'attendees' => $all_attendees,
        'stats' => [
            'total' => $total_attendees,
            'members' => $member_count,
            'guests' => $guest_count,
            'attending' => $attending_count,
            'actually_attended' => $actually_attended_count
        ]
    ];
}

// Get current view
$current_view = $_GET['view'] ?? 'main';
$selected_event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

// Set page variables
$page_title = "Event Reports";
$page_header = "Event Reports";
$page_description = "Generate comprehensive event attendance reports and analytics.";

// Include header
include 'includes/header.php';

// Get events for dropdown
$events_stmt = $pdo->query("SELECT id, title, event_date FROM events ORDER BY event_date DESC");
$events = $events_stmt->fetchAll(PDO::FETCH_ASSOC);

// If showing detailed view, get the event data
$detailed_report_data = null;
if ($current_view === 'detailed' && $selected_event_id) {
    $detailed_report_data = getEventAttendeesData($selected_event_id);

    // Get event details
    $event_stmt = $pdo->prepare("SELECT id, title, event_date, location, description, max_attendees FROM events WHERE id = ?");
    $event_stmt->execute([$selected_event_id]);
    $selected_event = $event_stmt->fetch(PDO::FETCH_ASSOC);
}
?>

// Define event report generation function
function generateEventReport($type, $date_from, $date_to, $event_id = null) {
    global $pdo;

    // Build query based on report type
    $where_conditions = [];
    $params = [];

    if ($date_from) {
        $where_conditions[] = "e.event_date >= ?";
        $params[] = $date_from;
    }

    if ($date_to) {
        $where_conditions[] = "e.event_date <= ?";
        $params[] = $date_to . ' 23:59:59';
    }

    if ($event_id) {
        $where_conditions[] = "e.id = ?";
        $params[] = $event_id;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    if ($type === 'attendance') {
        // Attendance report - combine member and guest RSVPs
        $sql = "
            SELECT e.title, e.event_date, e.location, e.max_attendees,
                   (
                       (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id) +
                       (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id)
                   ) as total_rsvps,
                   (
                       (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'attending') +
                       (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'attending')
                   ) as attending_count,
                   (
                       (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'not_attending') +
                       (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'not_attending')
                   ) as not_attending_count,
                   (
                       (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'maybe') +
                       (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'maybe')
                   ) as maybe_count
            FROM events e
            $where_clause
            ORDER BY e.event_date DESC
        ";
    } else {
        // Event summary report - combine member and guest RSVPs
        $sql = "
            SELECT e.title, e.event_date, e.location, e.max_attendees, e.is_active,
                   (
                       (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id) +
                       (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id)
                   ) as total_rsvps
            FROM events e
            $where_clause
            ORDER BY e.event_date DESC
        ";
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Generate PDF
    generatePDFReport($data, $type, $date_from, $date_to);
}

// Handle report generation BEFORE any output
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'generate_report':
            $report_type = $_POST['report_type'];
            $date_from = $_POST['date_from'];
            $date_to = $_POST['date_to'];
            $event_id = !empty($_POST['event_id']) ? (int)$_POST['event_id'] : null;

            // Generate and download the report
            generateEventReport($report_type, $date_from, $date_to, $event_id);
            exit(); // Important: exit after generating report
            break;
    }
}

// Set page variables
$page_title = "Event Reports";
$page_header = "Event Reports";
$page_description = "Generate and download event attendance reports.";

// Include header
include 'includes/header.php';

// Get events for dropdown
$events_stmt = $pdo->query("SELECT id, title, event_date FROM events ORDER BY event_date DESC");
$events = $events_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Generate Event Reports</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="" id="reportForm" target="_blank">
                        <input type="hidden" name="action" value="generate_report">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="report_type" class="form-label">Report Type</label>
                                    <select class="form-select" id="report_type" name="report_type" required>
                                        <option value="">Select Report Type</option>
                                        <option value="attendance">Attendance Report</option>
                                        <option value="summary">Event Summary Report</option>
                                    </select>
                                    <div class="form-text">Choose the type of report you want to generate</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="event_id" class="form-label">Specific Event (Optional)</label>
                                    <select class="form-select" id="event_id" name="event_id">
                                        <option value="">All Events</option>
                                        <?php foreach ($events as $event): ?>
                                            <option value="<?= $event['id'] ?>">
                                                <?= htmlspecialchars($event['title']) ?> - <?= date('M j, Y', strtotime($event['event_date'])) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">Leave blank to include all events</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_from" class="form-label">From Date</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from">
                                    <div class="form-text">Start date for the report range</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_to" class="form-label">To Date</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to">
                                    <div class="form-text">End date for the report range</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-file-earmark-pdf"></i> Generate Report
                                </button>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        The report will open in a new window where you can print or save as PDF.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('reportForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalBtnText = submitBtn.innerHTML;

    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Generating Report...';

        // Re-enable button after a delay
        setTimeout(function() {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;
        }, 3000);
    });

    // Set default date range to current month
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    document.getElementById('date_from').value = firstDay.toISOString().split('T')[0];
    document.getElementById('date_to').value = lastDay.toISOString().split('T')[0];

    // Update form help text based on report type
    document.getElementById('report_type').addEventListener('change', function() {
        const helpTexts = {
            'attendance': 'Detailed attendance breakdown showing who is attending, not attending, or maybe attending each event.',
            'summary': 'High-level overview of events with basic statistics and status information.'
        };

        const existingHelp = document.querySelector('.report-type-help');
        if (existingHelp) {
            existingHelp.remove();
        }

        if (this.value && helpTexts[this.value]) {
            const helpDiv = document.createElement('div');
            helpDiv.className = 'alert alert-info mt-2 report-type-help';
            helpDiv.innerHTML = '<i class="bi bi-info-circle"></i> ' + helpTexts[this.value];
            this.closest('.mb-3').appendChild(helpDiv);
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
