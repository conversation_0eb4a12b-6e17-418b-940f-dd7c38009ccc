<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include language system
require_once 'includes/language.php';

// Include the SecurityManager class
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize SecurityManager
$security = new SecurityManager($conn);

// Set security headers
$security->setSecurityHeaders();

$admin_id = $_SESSION['admin_id'];
$message = '';
$error = '';

// Get admin details
$stmt = $conn->prepare("SELECT username, email, full_name FROM admins WHERE id = ?");
$stmt->execute([$admin_id]);
$admin = $stmt->fetch();

// Check if 2FA is enabled for this admin
$stmt = $conn->prepare("SELECT * FROM admin_2fa WHERE admin_id = ?");
$stmt->execute([$admin_id]);
$twoFactorAuth = $stmt->fetch();
$is2FAEnabled = $twoFactorAuth && $twoFactorAuth['is_enabled'] == 1;

// Check if we need to show the 2FA modal
$show2FAModal = isset($_SESSION['show_2fa_modal']) && $_SESSION['show_2fa_modal'] === true;
$secretKey = $_SESSION['2fa_secret'] ?? '';
$backupCodes = $_SESSION['2fa_backup_codes'] ?? [];
$is2FAInSetup = $show2FAModal;

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        if (isset($_POST['update_profile'])) {
            // Update profile
            $full_name = $security->sanitizeInput($_POST['full_name'], 'text');
            $email = $security->sanitizeInput($_POST['email'], 'email');
            
            // Validate email
            if (!$security->validateInput($email, 'email')) {
                $error = "Please enter a valid email address.";
            } else {
                $stmt = $conn->prepare("UPDATE admins SET full_name = ?, email = ? WHERE id = ?");
                
                if ($stmt->execute([$full_name, $email, $admin_id])) {
                    $_SESSION['admin_name'] = $full_name; // Update session
                    $message = "Profile updated successfully!";
                    
                    // Refresh admin data
                    $admin['full_name'] = $full_name;
                    $admin['email'] = $email;
                    
                    // Log profile update
                    $security->logSecurityEvent('Profile updated', [
                        'admin_id' => $admin_id,
                        'username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $error = "Error updating profile: " . $conn->errorInfo()[2];
                }
            }
        } elseif (isset($_POST['change_password'])) {
            // Change password
            $current_password = $_POST['current_password'];
            $new_password = $_POST['new_password'];
            $confirm_password = $_POST['confirm_password'];
            
            // Verify current password
            $stmt = $conn->prepare("SELECT password FROM admins WHERE id = ?");
            $stmt->execute([$admin_id]);
            $row = $stmt->fetch();
            
            if (!$security->verifyPassword($current_password, $row['password'])) {
                $error = "Current password is incorrect.";
            } elseif ($new_password !== $confirm_password) {
                $error = "New passwords do not match.";
            } elseif (!$security->validateInput($new_password, 'password')) {
                $error = "Password does not meet the requirements. Please use a stronger password.";
            } else {
                // Hash new password
                $hashed_password = $security->hashPassword($new_password);
                
                $stmt = $conn->prepare("
                    UPDATE admins 
                    SET password = ?, password_changed_at = NOW() 
                    WHERE id = ?
                ");
                
                if ($stmt->execute([$hashed_password, $admin_id])) {
                    $message = "Password changed successfully!";
                    
                    // Log password change
                    $security->logSecurityEvent('Password change', [
                        'admin_id' => $admin_id,
                        'username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $error = "Error changing password: " . $conn->errorInfo()[2];
                }
            }
        } elseif (isset($_POST['setup_2fa'])) {
            // Setup 2FA with a simpler approach
            
            // Generate a new secret key (16 characters)
            $secretKey = bin2hex(random_bytes(8));
            
            // Generate backup codes
            $backupCodes = [];
            for ($i = 0; $i < 5; $i++) {
                $backupCodes[] = strtoupper(bin2hex(random_bytes(4)));
            }
            $backupCodesJson = json_encode($backupCodes);
            
            // Store the secret key and backup codes in session for the modal
            $_SESSION['show_2fa_modal'] = true;
            $_SESSION['2fa_secret'] = $secretKey;
            $_SESSION['2fa_backup_codes'] = $backupCodes;
            
            // Don't save to database yet - only save when user confirms in the modal
            $message = "Two-factor authentication setup initiated. Please save your secret key and backup codes.";
            
            // Log 2FA setup initiation
            $security->logSecurityEvent('2FA setup initiated', [
                'admin_id' => $admin_id,
                'username' => $_SESSION['admin_username']
            ]);
            
            // Set flag to indicate 2FA is in setup process
            $is2FAInSetup = true;
        } elseif (isset($_POST['close_2fa_modal'])) {
            // User has completed the 2FA setup
            
            // Get the secret key and backup codes from session
            $secretKey = $_SESSION['2fa_secret'] ?? '';
            $backupCodes = $_SESSION['2fa_backup_codes'] ?? [];
            $backupCodesJson = json_encode($backupCodes);
            
            if (empty($secretKey) || empty($backupCodes)) {
                $error = "Setup information is missing. Please restart the 2FA setup process.";
            } else {
                // Save to database
                // Check if 2FA is already set up
                $stmt = $conn->prepare("SELECT id FROM admin_2fa WHERE admin_id = ?");
                $stmt->execute([$admin_id]);
                $existing2fa = $stmt->fetch();
                
                if ($existing2fa) {
                    // Update existing 2FA
                    $stmt = $conn->prepare("
                        UPDATE admin_2fa 
                        SET secret_key = ?, backup_codes = ?, is_enabled = 1, updated_at = NOW() 
                        WHERE admin_id = ?
                    ");
                    $result = $stmt->execute([$secretKey, $backupCodesJson, $admin_id]);
                } else {
                    // Create new 2FA record
                    $stmt = $conn->prepare("
                        INSERT INTO admin_2fa 
                        (admin_id, secret_key, backup_codes, is_enabled, created_at, updated_at) 
                        VALUES (?, ?, ?, 1, NOW(), NOW())
                    ");
                    $result = $stmt->execute([$admin_id, $secretKey, $backupCodesJson]);
                }
                
                if ($result) {
                    // Clear session data
                    unset($_SESSION['show_2fa_modal']);
                    unset($_SESSION['2fa_secret']);
                    unset($_SESSION['2fa_backup_codes']);
                    
                    $message = "Two-factor authentication has been successfully set up and activated.";
                    
                    // Update the 2FA status for the current page
                    $is2FAEnabled = true;
                    
                    // Log 2FA setup completion
                    $security->logSecurityEvent('2FA setup completed', [
                        'admin_id' => $admin_id,
                        'username' => $_SESSION['admin_username']
                    ]);
                    
                    // If this is a form submission that should redirect, set a flag in the session
                    if (isset($_POST['redirect_after_submit'])) {
                        $_SESSION['show_success_message'] = $message;
                        header("Location: " . htmlspecialchars($_SERVER["PHP_SELF"]));
                        exit();
                    }
                } else {
                    $error = "Error saving 2FA settings to database: " . $conn->errorInfo()[2];
                }
            }
        } elseif (isset($_POST['disable_2fa'])) {
            // Disable 2FA
            $stmt = $conn->prepare("
                UPDATE admin_2fa 
                SET is_enabled = 0, updated_at = NOW() 
                WHERE admin_id = ?
            ");
            
            if ($stmt->execute([$admin_id])) {
                $message = "Two-factor authentication has been disabled.";
                
                // Update the 2FA status for the current page
                $is2FAEnabled = false;
                
                // Log 2FA disable
                $security->logSecurityEvent('2FA disabled', [
                    'admin_id' => $admin_id,
                    'username' => $_SESSION['admin_username']
                ]);
            } else {
                $error = "Error disabling two-factor authentication: " . $conn->errorInfo()[2];
            }
        } elseif (isset($_POST['show_2fa_modal'])) {
            // User has clicked "Continue Setup"
            $_SESSION['show_2fa_modal'] = true;
            $show2FAModal = true;
            $is2FAInSetup = true;
        } elseif (isset($_POST['clear_2fa_modal'])) {
            // User has clicked the close button on the 2FA modal
            // Clear all 2FA setup session variables
            unset($_SESSION['show_2fa_modal']);
            unset($_SESSION['2fa_secret']);
            unset($_SESSION['2fa_backup_codes']);
            
            // Set message to inform user
            $message = "Two-factor authentication setup was cancelled.";
            
            // Log the cancellation
            $security->logSecurityEvent('2FA setup cancelled', [
                'admin_id' => $admin_id,
                'username' => $_SESSION['admin_username']
            ]);
        }
    }
}

// Check for success message in session
if (isset($_SESSION['show_success_message'])) {
    $message = $_SESSION['show_success_message'];
    unset($_SESSION['show_success_message']);
}

// Close the database connection
$conn = null;

// Set page variables
$page_title = __('my_profile');
$page_header = __('my_profile');
$page_description = __('view_manage_admin_account_description');

// Include header
include 'includes/header.php';

// Display success message if it exists
if (!empty($message)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

// Display error message if it exists
if (!empty($error)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}
?>

<div class="row">
    <div class="col-md-6">
        <!-- Profile Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><?php _e('profile_information'); ?></h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                    <?php echo $security->generateCSRFInput(); ?>
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" value="<?php echo htmlspecialchars($admin['username']); ?>" readonly>
                        <div class="form-text">Username cannot be changed.</div>
                    </div>
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo htmlspecialchars($admin['full_name']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($admin['email']); ?>" required>
                    </div>
                    <div class="text-end">
                        <button type="submit" name="update_profile" class="btn btn-primary">Update Profile</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <!-- Change Password -->
        <div class="card">
            <div class="card-header">
                <h5><?php _e('change_password'); ?></h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                    <?php echo $security->generateCSRFInput(); ?>
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                        <div class="password-strength-meter mt-2"></div>
                        <div class="form-text password-requirements">
                            Password must be at least 8 characters long and include uppercase letters, 
                            lowercase letters, numbers, and special characters.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <div class="text-end">
                        <button type="submit" name="change_password" class="btn btn-primary">Change Password</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <!-- Two-Factor Authentication -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><?php _e('two_factor_authentication'); ?></h5>
            </div>
            <div class="card-body">
                <?php if ($is2FAEnabled): ?>
                    <!-- 2FA is enabled - Show status and disable option -->
                    <div class="alert alert-success">
                        <i class="bi bi-shield-check"></i> Two-factor authentication is currently <strong>enabled</strong> for your account.
                    </div>
                    
                    <p>Two-factor authentication adds an extra layer of security to your account. When enabled, you'll need to enter both your password and a verification code from your authenticator app when logging in.</p>
                    
                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                        <?php echo $security->generateCSRFInput(); ?>
                        <button type="submit" name="disable_2fa" class="btn btn-danger" 
                                onclick="return confirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')">
                            <i class="bi bi-shield-x"></i> Disable Two-Factor Authentication
                        </button>
                    </form>
                <?php elseif ($is2FAInSetup): ?>
                    <!-- 2FA is in setup process - Show status -->
                    <div class="alert alert-info">
                        <i class="bi bi-shield-lock"></i> Two-factor authentication setup is in progress.
                    </div>
                    
                    <p>Please complete the setup process in the popup window. If you don't see the popup, please click the button below to continue setup.</p>
                    
                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                        <?php echo $security->generateCSRFInput(); ?>
                        <button type="submit" name="show_2fa_modal" class="btn btn-primary">
                            <i class="bi bi-window"></i> Continue Setup
                        </button>
                    </form>
                <?php else: ?>
                    <!-- 2FA is not enabled - Show setup option -->
                    <div class="alert alert-warning">
                        <i class="bi bi-shield-exclamation"></i> Two-factor authentication is currently <strong>disabled</strong> for your account.
                    </div>
                    
                    <p>Two-factor authentication adds an extra layer of security to your account. When enabled, you'll need to enter both your password and a verification code from your authenticator app when logging in.</p>
                    
                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                        <?php echo $security->generateCSRFInput(); ?>
                        <button type="submit" name="setup_2fa" class="btn btn-primary">
                            <i class="bi bi-shield-plus"></i> Set Up Two-Factor Authentication
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Password strength meter
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('new_password');
    const confirmInput = document.getElementById('confirm_password');
    const strengthMeter = document.querySelector('.password-strength-meter');
    
    if (passwordInput && strengthMeter) {
        // Style the strength meter
        strengthMeter.style.height = '5px';
        strengthMeter.style.width = '0%';
        strengthMeter.style.transition = 'all 0.3s ease';
        
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;
            let feedback = '';
            
            // Length check
            if (password.length >= 8) {
                strength += 25;
            }
            
            // Character type checks
            if (/[A-Z]/.test(password)) strength += 25;
            if (/[a-z]/.test(password)) strength += 25;
            if (/[0-9]/.test(password)) strength += 12.5;
            if (/[^A-Za-z0-9]/.test(password)) strength += 12.5;
            
            // Update strength meter
            strengthMeter.style.width = strength + '%';
            
            // Set color based on strength
            if (strength < 50) {
                strengthMeter.style.backgroundColor = '#dc3545'; // red
            } else if (strength < 75) {
                strengthMeter.style.backgroundColor = '#ffc107'; // yellow
            } else {
                strengthMeter.style.backgroundColor = '#28a745'; // green
            }
        });
        
        // Check password match
        if (confirmInput) {
            confirmInput.addEventListener('input', function() {
                if (this.value === passwordInput.value) {
                    this.style.borderColor = '#28a745';
                } else {
                    this.style.borderColor = '#dc3545';
                }
            });
        }
    }
});

// Function to convert hex to base32 for Google Authenticator
function hexToBase32(hexString) {
    // Base32 alphabet (RFC 4648)
    const BASE32_ALPHABET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    
    // Remove any spaces or separators
    hexString = hexString.replace(/\s/g, '');
    
    // Ensure the hex string is at least 32 characters (16 bytes) long
    // Google Authenticator requires at least 16 bytes of entropy
    while (hexString.length < 32) {
        hexString += '0'; // Pad with zeros if needed
    }
    
    // Convert hex to byte array
    const bytes = [];
    for (let i = 0; i < hexString.length; i += 2) {
        bytes.push(parseInt(hexString.substr(i, 2), 16));
    }
    
    // Convert bytes to base32
    let result = '';
    let buffer = 0;
    let bufferLength = 0;
    
    for (let i = 0; i < bytes.length; i++) {
        buffer = (buffer << 8) | bytes[i];
        bufferLength += 8;
        
        while (bufferLength >= 5) {
            bufferLength -= 5;
            result += BASE32_ALPHABET[(buffer >> bufferLength) & 31];
        }
    }
    
    // Handle remaining bits
    if (bufferLength > 0) {
        result += BASE32_ALPHABET[(buffer << (5 - bufferLength)) & 31];
    }
    
    // Remove padding characters (=) as Google Authenticator doesn't accept them
    return result.replace(/=/g, '');
}

// Script to handle 2FA modal
document.addEventListener('DOMContentLoaded', function() {
    // Check if the 2FA modal exists
    var modalElement = document.getElementById('twoFactorSetupModal');
    if (modalElement) {
        try {
            // Initialize the modal
            var twoFactorModal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',
                keyboard: false
            });
            
            // Show the modal
            twoFactorModal.show();
            
            // Convert hex secret key to base32 and populate the field
            var hexSecretKey = document.querySelector('#twoFactorSetupModal input[value="<?php echo $secretKey; ?>"]').value;
            var base32SecretKey = hexToBase32(hexSecretKey);
            document.getElementById('base32SecretKey').value = base32SecretKey;
            
            // Handle close button click
            var closeBtn = document.querySelector('#twoFactorSetupModal .btn-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    if (confirm("Are you sure you want to close this window? Your 2FA setup will not be completed, and you will need to start over.")) {
                        // Hide the modal
                        twoFactorModal.hide();
                        
                        // Create a form to submit to clear the session variables
                        var clearForm = document.createElement('form');
                        clearForm.method = 'POST';
                        clearForm.action = window.location.href;
                        
                        // Add CSRF token
                        var csrfInput = document.querySelector('input[name="csrf_token"]');
                        if (csrfInput) {
                            var csrfToken = document.createElement('input');
                            csrfToken.type = 'hidden';
                            csrfToken.name = 'csrf_token';
                            csrfToken.value = csrfInput.value;
                            clearForm.appendChild(csrfToken);
                        }
                        
                        // Add action to clear 2FA modal
                        var clearAction = document.createElement('input');
                        clearAction.type = 'hidden';
                        clearAction.name = 'clear_2fa_modal';
                        clearAction.value = '1';
                        clearForm.appendChild(clearAction);
                        
                        // Append form to body, submit it, and remove it
                        document.body.appendChild(clearForm);
                        clearForm.submit();
                    }
                });
            }
            
            // Handle form submission
            var form = document.getElementById('completeSetupForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    // Prevent default form submission
                    e.preventDefault();
                    
                    // Hide the modal
                    twoFactorModal.hide();
                    
                    // Add a hidden field to indicate this is a form submission that should redirect
                    var redirectInput = document.createElement('input');
                    redirectInput.type = 'hidden';
                    redirectInput.name = 'redirect_after_submit';
                    redirectInput.value = '1';
                    form.appendChild(redirectInput);
                    
                    // Submit the form after a short delay to ensure modal is closed
                    setTimeout(function() {
                        form.submit();
                    }, 300);
                });
            }
        } catch (error) {
            console.error('Error initializing modal:', error);
        }
    }
});

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('Secret key copied to clipboard!');
    }, function(err) {
        console.error('Could not copy text: ', err);
    });
}

function copyBackupCodes() {
    const codes = document.querySelectorAll('#backup-codes-list li span');
    let text = '';
    codes.forEach(item => {
        text += item.textContent.trim() + '\n';
    });
    
    navigator.clipboard.writeText(text).then(function() {
        alert('All backup codes copied to clipboard!');
    }, function(err) {
        console.error('Could not copy backup codes: ', err);
    });
}
</script>

<?php include_once 'includes/footer.php'; ?>

<!-- 2FA Setup Modal -->
<?php if ($show2FAModal): ?>
<!-- Make sure Bootstrap JS is loaded -->
<script>
// Check if Bootstrap is loaded
if (typeof bootstrap === 'undefined') {
    // Load Bootstrap JS if not already loaded
    document.write('<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"><\/script>');
}
</script>

<div class="modal fade" id="twoFactorSetupModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="twoFactorSetupModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="twoFactorSetupModalLabel"><?php _e('two_factor_authentication_setup'); ?></h5>
                <button type="button" class="btn-close btn-close-white" aria-label="Close" id="closeModalBtn"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h5><i class="bi bi-exclamation-triangle-fill"></i> Important Security Information</h5>
                    <p>You <strong>MUST</strong> save your secret key and backup codes before closing this window. You will not be able to see this information again!</p>
                    <p>If you lose access to your secret key and backup codes, you will be locked out of your account.</p>
                </div>
                
                <h5 class="mt-4"><i class="bi bi-key"></i> Your Secret Keys</h5>
                
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <span class="fw-bold">Hex Secret Key</span> (for backup purposes)
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <input type="text" class="form-control" value="<?php echo $secretKey; ?>" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard(this.previousElementSibling.value)">
                                <i class="bi bi-clipboard"></i> Copy
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-header bg-success text-white">
                        <span class="fw-bold">Base32 Secret Key</span> (for Google Authenticator)
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <input type="text" class="form-control" id="base32SecretKey" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard(this.previousElementSibling.value)">
                                <i class="bi bi-clipboard"></i> Copy
                            </button>
                        </div>
                        <div class="mt-2 small">
                            <strong>Instructions for Google Authenticator:</strong>
                            <ol class="mb-0">
                                <li>Open Google Authenticator app</li>
                                <li>Tap the "+" button</li>
                                <li>Select "Enter a setup key"</li>
                                <li>For "Account name", enter "Freedom Assembly Church"</li>
                                <li>For "Your key", enter the Base32 key above</li>
                                <li>Make sure "Time-based" is selected</li>
                                <li>Tap "Add"</li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <h5 class="mt-4"><i class="bi bi-shield-lock"></i> <?php _e('your_backup_codes'); ?></h5>
                <p>Save these backup codes in a secure place. You can use them to log in if you lose access to your secret key:</p>
                
                <div class="d-flex justify-content-end mb-2">
                    <button class="btn btn-sm btn-outline-secondary" type="button" onclick="copyBackupCodes()">
                        <i class="bi bi-clipboard"></i> Copy All Codes
                    </button>
                </div>
                
                <ul class="list-group" id="backup-codes-list">
                    <?php foreach ($backupCodes as $code): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><?php echo $code; ?></span>
                            <button class="btn btn-sm btn-outline-secondary" type="button" 
                                    onclick="copyToClipboard('<?php echo $code; ?>')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <div class="modal-footer">
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="completeSetupForm">
                    <?php echo $security->generateCSRFInput(); ?>
                    <input type="hidden" name="close_2fa_modal" value="1">
                    <div class="form-check me-auto">
                        <input class="form-check-input" type="checkbox" id="confirmSaved" required 
                               onchange="document.getElementById('completeSetupBtn').disabled = !this.checked;">
                        <label class="form-check-label" for="confirmSaved">
                            I have saved my secret key and backup codes
                        </label>
                    </div>
                    <button type="submit" class="btn btn-primary" id="completeSetupBtn" disabled>
                        Complete Setup
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endif; ?> 