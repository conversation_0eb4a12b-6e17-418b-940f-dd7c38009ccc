Different integer syntaxes
-----
<?php

0;
1;
@@{ PHP_INT_MAX     }@@;
@@{ PHP_INT_MAX + 1 }@@;
0xFFF;
0xfff;
0XfFf;
0777;
0b111000111000;
-----
array(
    0: Stmt_Expression(
        expr: Scalar_Int(
            value: 0
        )
    )
    1: Stmt_Expression(
        expr: Scalar_Int(
            value: 1
        )
    )
    2: Stmt_Expression(
        expr: Scalar_Int(
            value: @@{ PHP_INT_MAX }@@
        )
    )
    3: Stmt_Expression(
        expr: Scalar_Float(
            value: @@{ PHP_INT_MAX + 1 }@@
        )
    )
    4: Stmt_Expression(
        expr: Scalar_Int(
            value: 4095
        )
    )
    5: Stmt_Expression(
        expr: Scalar_Int(
            value: 4095
        )
    )
    6: Stmt_Expression(
        expr: Scalar_Int(
            value: 4095
        )
    )
    7: Stmt_Expression(
        expr: Scalar_Int(
            value: 511
        )
    )
    8: Stmt_Expression(
        expr: Scalar_Int(
            value: 3640
        )
    )
)
