Handling of inline HTML
-----
<?php

function test() {
    ?>Foo<?php
}
-----
$stmts[0]->setAttribute('origNode', null);
-----
<?php

function test()
{
    ?>Foo<?php
}
-----
<?php

function test() {
    foo();
    ?>Bar<?php
    baz();
}
-----
// TODO Fix broken result
$stmts[0]->stmts[2] = $stmts[0]->stmts[1];
-----
<?php

function test() {
    foo();
    ?>Bar<?php
    Bar
}
-----
<?php

function test() {
    foo();
    ?>Bar<?php
    baz();
}
-----
// TODO Preserve formatting
$stmts[0]->stmts[1] = $stmts[0]->stmts[2];
-----
<?php

function test()
{
    foo();
    baz();
    baz();
}
-----
<?php

function test() {
    foo();
    ?>Bar<?php
    baz();
}
-----
// TODO Preserve formatting
unset($stmts[0]->stmts[2]);
-----
<?php

function test()
{
    foo();
    ?>Bar<?php
}
-----
<?php

function test() {
    foo();
    ?>Bar<?php
    baz();
}
-----
// TODO Preserve formatting
array_splice($stmts[0]->stmts, 0, 1, []);
-----
<?php

function test()
{
    ?>Bar<?php
    baz();
}
-----
<?php

function test() {
    foo();
    ?>Bar<?php
    baz();
}
-----
// TODO Preserve formatting
array_splice($stmts[0]->stmts, 1, 1, []);
-----
<?php

function test()
{
    foo();
    baz();
}
-----


<?php $x;
-----
/* Do nothing, but make sure leading newlines are preserved. */
-----


<?php $x;
