<?php
session_start();
require_once '../../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Get JSON data
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['group_id']) || !isset($data['contact_ids']) || empty($data['contact_ids'])) {
    echo json_encode(['success' => false, 'error' => 'Group ID and contact IDs are required']);
    exit();
}

try {
    $pdo->beginTransaction();
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO contact_group_members (contact_id, group_id) VALUES (?, ?)");
    foreach ($data['contact_ids'] as $contact_id) {
        $stmt->execute([$contact_id, $data['group_id']]);
    }
    
    $pdo->commit();
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?> 