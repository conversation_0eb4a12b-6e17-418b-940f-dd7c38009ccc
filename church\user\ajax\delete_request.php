<?php
/**
 * Delete Request AJAX Endpoint
 * 
 * Allows users to delete their own prayer requests
 */

session_start();
require_once '../../config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

$requestId = $input['request_id'] ?? null;

if (!$requestId) {
    echo json_encode(['success' => false, 'error' => 'Request ID is required']);
    exit;
}

try {
    // First, verify that the request belongs to the current user
    $stmt = $pdo->prepare("
        SELECT member_id, title 
        FROM prayer_requests 
        WHERE id = ?
    ");
    $stmt->execute([$requestId]);
    $request = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$request) {
        echo json_encode(['success' => false, 'error' => 'Request not found']);
        exit;
    }
    
    if ($request['member_id'] != $_SESSION['user_id']) {
        echo json_encode(['success' => false, 'error' => 'You can only delete your own requests']);
        exit;
    }
    
    // Begin transaction
    $pdo->beginTransaction();
    
    try {
        // Delete related prayer responses first (foreign key constraint)
        $stmt = $pdo->prepare("DELETE FROM prayer_responses WHERE prayer_request_id = ?");
        $stmt->execute([$requestId]);
        
        // Delete the prayer request
        $stmt = $pdo->prepare("DELETE FROM prayer_requests WHERE id = ?");
        $stmt->execute([$requestId]);
        
        // Commit transaction
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Request deleted successfully'
        ]);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $pdo->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log('Error deleting request: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Failed to delete request']);
}
?>
