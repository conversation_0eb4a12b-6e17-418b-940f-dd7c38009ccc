<?php
// Include config to access organization functions
if (!function_exists('get_organization_name')) {
    require_once 'config.php';
}
?>
<footer class="bg-dark text-white py-5 mt-5">
    <div class="container">
        <div class="row">
            <div class="col-md-4 mb-4 mb-md-0">
                <h5><?php echo htmlspecialchars(get_organization_name()); ?></h5>
                <p class="mt-3">
                    <i class="bi bi-geo-alt-fill me-2"></i> <?php echo htmlspecialchars(get_site_setting('contact_address', '123 Main Street')); ?><br>
                    <span class="ms-4"><?php echo htmlspecialchars(get_site_setting('contact_city', 'Anytown')); ?>, <?php echo htmlspecialchars(get_site_setting('contact_state', 'ST')); ?> <?php echo htmlspecialchars(get_site_setting('contact_zip', '12345')); ?></span>
                </p>
                <p>
                    <i class="bi bi-telephone-fill me-2"></i> <?php echo htmlspecialchars(get_site_setting('contact_phone', '(*************')); ?>
                </p>
                <p>
                    <i class="bi bi-envelope-fill me-2"></i> <?php echo htmlspecialchars(get_site_setting('contact_email', '<EMAIL>')); ?>
                </p>
            </div>
            <div class="col-md-4 mb-4 mb-md-0">
                <h5>Quick Links</h5>
                <ul class="list-unstyled mt-3">
                    <li class="mb-2"><a href="register.php" class="text-white text-decoration-none">Home</a></li>
                    <li class="mb-2"><a href="about.php" class="text-white text-decoration-none">About Us</a></li>
                    <li class="mb-2"><a href="events.php" class="text-white text-decoration-none">Events</a></li>
                    <li class="mb-2"><a href="donate.php" class="text-white text-decoration-none">Donate</a></li>
                    <li><a href="contact.php" class="text-white text-decoration-none">Contact</a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <h5>Connect With Us</h5>
                <div class="mt-3">
                    <a href="#" class="text-white me-3 fs-4"><i class="bi bi-facebook"></i></a>
                    <a href="#" class="text-white me-3 fs-4"><i class="bi bi-twitter"></i></a>
                    <a href="#" class="text-white me-3 fs-4"><i class="bi bi-instagram"></i></a>
                    <a href="#" class="text-white fs-4"><i class="bi bi-youtube"></i></a>
                </div>
                <div class="mt-4">
                    <a href="donate.php" class="btn btn-primary">Donate Now</a>
                </div>
            </div>
        </div>
        <hr class="my-4">
        <div class="row">
            <div class="col-md-6 mb-3 mb-md-0">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars(get_organization_name()); ?>. All rights reserved.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="privacy.php" class="text-white text-decoration-none me-3">Privacy Policy</a>
                <a href="terms.php" class="text-white text-decoration-none">Terms of Service</a>
            </div>
        </div>
    </div>
</footer> 