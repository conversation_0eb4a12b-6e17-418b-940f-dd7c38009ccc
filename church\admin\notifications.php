<?php
require_once '../config.php';
require_once 'includes/session-manager.php';
require_once 'includes/admin_notification_functions.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$adminId = $_SESSION['admin_id'];
$message = '';

// Use $conn if $pdo is not available
$db = isset($pdo) ? $pdo : $conn;

// Set page variables for header
$page_title = 'Admin Notifications';
$page_header = 'Admin Notifications';
$page_description = 'Manage and view your administrative notifications';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'mark_read':
                if (isset($_POST['notification_id'])) {
                    if (markAdminNotificationAsRead($db, $_POST['notification_id'], $adminId)) {
                        $message = 'Notification marked as read.';
                    }
                }
                break;

            case 'mark_all_read':
                if (markAllAdminNotificationsAsRead($db, $adminId)) {
                    $message = 'All notifications marked as read.';
                }
                break;

            case 'delete_notification':
                if (isset($_POST['notification_id'])) {
                    try {
                        $stmt = $db->prepare("DELETE FROM admin_notifications WHERE id = ? AND recipient_id = ?");
                        if ($stmt->execute([$_POST['notification_id'], $adminId])) {
                            $message = 'Notification deleted successfully.';
                        }
                    } catch (Exception $e) {
                        $error = 'Error deleting notification.';
                    }
                }
                break;

            case 'delete_all_read':
                try {
                    $stmt = $db->prepare("DELETE FROM admin_notifications WHERE recipient_id = ? AND is_read = 1");
                    $deletedCount = $stmt->execute([$adminId]) ? $stmt->rowCount() : 0;
                    if ($deletedCount > 0) {
                        $message = "Deleted $deletedCount read notifications.";
                    } else {
                        $message = 'No read notifications to delete.';
                    }
                } catch (Exception $e) {
                    $error = 'Error deleting notifications.';
                }
                break;
        }
    }
}

// Pagination settings
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 15; // Reduced for better mobile experience
$offset = ($page - 1) * $limit;

// Filter settings
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$unreadOnly = $filter === 'unread';

// Get total count with filter
$countQuery = "
    SELECT COUNT(*)
    FROM admin_notifications
    WHERE recipient_id = ?
    AND (expires_at IS NULL OR expires_at > NOW())
";
$countParams = [$adminId];

if ($unreadOnly) {
    $countQuery .= " AND is_read = 0";
}

$stmt = $db->prepare($countQuery);
$stmt->execute($countParams);
$totalNotifications = $stmt->fetchColumn();
$totalPages = ceil($totalNotifications / $limit);

// Get notifications with pagination and filter
$notifications = getAdminNotifications($db, $adminId, $limit, $offset, $unreadOnly);

include 'includes/header.php';
?>

<!-- Filter and Action Bar -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="d-flex align-items-center mb-2 mb-md-0">
                        <h5 class="mb-0 me-3"><i class="bi bi-bell"></i> Notifications</h5>
                        <span class="badge bg-primary"><?php echo $totalNotifications; ?> total</span>
                    </div>
                    <div class="d-flex align-items-center gap-2 flex-wrap">
                        <!-- Filter Buttons -->
                        <div class="btn-group" role="group">
                            <a href="?filter=all&page=1" class="btn btn-sm <?php echo $filter === 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                All
                            </a>
                            <a href="?filter=unread&page=1" class="btn btn-sm <?php echo $filter === 'unread' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                Unread
                            </a>
                        </div>

                        <?php if ($totalNotifications > 0): ?>
                            <div class="d-flex gap-2">
                                <form method="post" class="d-inline">
                                    <input type="hidden" name="action" value="mark_all_read">
                                    <button type="submit" class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-check-all"></i> Mark All Read
                                    </button>
                                </form>
                                <form method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to delete all read notifications? This action cannot be undone.')">
                                    <input type="hidden" name="action" value="delete_all_read">
                                    <button type="submit" class="btn btn-outline-danger btn-sm">
                                        <i class="bi bi-trash"></i> Delete Read
                                    </button>
                                </form>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
            
<!-- Success Message -->
<?php if ($message): ?>
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error) && $error): ?>
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Notifications Content -->
<?php if (empty($notifications)): ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-bell-slash display-1 text-muted"></i>
                    <h4 class="mt-3 text-muted">
                        <?php echo $unreadOnly ? 'No Unread Notifications' : 'No Notifications'; ?>
                    </h4>
                    <p class="text-muted">
                        <?php echo $unreadOnly ? 'All your notifications have been read.' : 'You don\'t have any notifications at the moment.'; ?>
                    </p>
                    <?php if ($unreadOnly): ?>
                        <a href="?filter=all" class="btn btn-outline-primary">View All Notifications</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>
    <!-- Notifications List -->
    <div class="row">
        <?php foreach ($notifications as $notification): ?>
            <div class="col-12 mb-3">
                <div class="card notification-card <?php echo $notification['is_read'] ? '' : 'border-primary'; ?> <?php echo $notification['priority'] !== 'normal' ? 'priority-' . $notification['priority'] : ''; ?>">
                    <div class="card-body">
                        <div class="d-flex">
                            <div class="notification-icon me-3 p-2 rounded-circle bg-light <?php echo getAdminNotificationColorClass($notification['priority']); ?>">
                                <i class="bi <?php echo getAdminNotificationIcon($notification['notification_type']); ?> fs-5"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="card-title mb-1 d-flex align-items-center flex-wrap">
                                            <span class="me-2"><?php echo htmlspecialchars($notification['title']); ?></span>
                                            <?php if (!$notification['is_read']): ?>
                                                <span class="badge bg-primary me-1">New</span>
                                            <?php endif; ?>
                                            <?php if ($notification['priority'] === 'urgent'): ?>
                                                <span class="badge bg-danger me-1">Urgent</span>
                                            <?php elseif ($notification['priority'] === 'high'): ?>
                                                <span class="badge bg-warning text-dark me-1">High</span>
                                            <?php endif; ?>
                                        </h6>
                                        <p class="card-text text-muted mb-2"><?php echo nl2br(htmlspecialchars($notification['message'])); ?></p>
                                        <small class="text-muted d-block">
                                            <i class="bi bi-clock me-1"></i>
                                            <?php echo formatAdminNotificationTime($notification['created_at']); ?>
                                            <?php if ($notification['sender_name']): ?>
                                                • From: <?php echo htmlspecialchars($notification['sender_name']); ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <div class="d-flex align-items-start gap-2">
                                        <?php if ($notification['action_url']): ?>
                                            <a href="<?php echo htmlspecialchars($notification['action_url']); ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-arrow-right"></i> View
                                            </a>
                                        <?php endif; ?>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <?php if (!$notification['is_read']): ?>
                                                    <li>
                                                        <form method="post" class="d-inline">
                                                            <input type="hidden" name="action" value="mark_read">
                                                            <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="bi bi-check me-2"></i>Mark as Read
                                                            </button>
                                                        </form>
                                                    </li>
                                                <?php endif; ?>
                                                <?php if ($notification['action_url']): ?>
                                                    <li>
                                                        <a class="dropdown-item" href="<?php echo htmlspecialchars($notification['action_url']); ?>">
                                                            <i class="bi bi-arrow-right me-2"></i>View Details
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <form method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this notification?')">
                                                        <input type="hidden" name="action" value="delete_notification">
                                                        <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                        <button type="submit" class="dropdown-item text-danger">
                                                            <i class="bi bi-trash me-2"></i>Delete
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Enhanced Pagination -->
    <?php if ($totalPages > 1): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <div class="mb-2 mb-md-0">
                                <small class="text-muted">
                                    Showing <?php echo (($page - 1) * $limit) + 1; ?> to
                                    <?php echo min($page * $limit, $totalNotifications); ?> of
                                    <?php echo $totalNotifications; ?> notifications
                                </small>
                            </div>
                            <nav aria-label="Notifications pagination">
                                <ul class="pagination pagination-sm mb-0">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?filter=<?php echo $filter; ?>&page=1">
                                                <i class="bi bi-chevron-double-left"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?filter=<?php echo $filter; ?>&page=<?php echo $page - 1; ?>">
                                                <i class="bi bi-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php
                                    $start = max(1, $page - 2);
                                    $end = min($totalPages, $page + 2);

                                    if ($start > 1) {
                                        echo '<li class="page-item"><a class="page-link" href="?filter=' . $filter . '&page=1">1</a></li>';
                                        if ($start > 2) {
                                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                        }
                                    }

                                    for ($i = $start; $i <= $end; $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?filter=<?php echo $filter; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                        </li>
                                    <?php endfor;

                                    if ($end < $totalPages) {
                                        if ($end < $totalPages - 1) {
                                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                        }
                                        echo '<li class="page-item"><a class="page-link" href="?filter=' . $filter . '&page=' . $totalPages . '">' . $totalPages . '</a></li>';
                                    }
                                    ?>

                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?filter=<?php echo $filter; ?>&page=<?php echo $page + 1; ?>">
                                                <i class="bi bi-chevron-right"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?filter=<?php echo $filter; ?>&page=<?php echo $totalPages; ?>">
                                                <i class="bi bi-chevron-double-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php endif; ?>

<style>
.notification-card {
    transition: all 0.3s ease;
    border-radius: 8px;
}

.notification-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.notification-card.priority-high {
    border-left: 4px solid #ffc107;
}

.notification-card.priority-urgent {
    border-left: 4px solid #dc3545;
    animation: pulse-urgent 2s infinite;
}

@keyframes pulse-urgent {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

.notification-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.card-title {
    line-height: 1.4;
}

.pagination-sm .page-link {
    padding: 0.375rem 0.75rem;
}

/* Delete button styling */
.dropdown-item.text-danger:hover {
    background-color: #dc3545;
    color: white !important;
}

.btn-outline-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Notification badge improvements */
.badge.bg-danger {
    animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@media (max-width: 768px) {
    .notification-card .d-flex {
        flex-direction: column;
    }

    .notification-icon {
        align-self: flex-start;
        margin-bottom: 1rem;
        margin-right: 0 !important;
    }

    .card-title {
        font-size: 1rem;
    }

    .pagination {
        justify-content: center;
    }

    .pagination .page-item:not(.active):not(.disabled) {
        display: none;
    }

    .pagination .page-item.active,
    .pagination .page-item:first-child,
    .pagination .page-item:last-child,
    .pagination .page-item:nth-child(2),
    .pagination .page-item:nth-last-child(2) {
        display: block;
    }

    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    .d-flex.gap-2 .btn {
        width: 100%;
    }
}
</style>

<script>
// Enhanced delete confirmation
document.addEventListener('DOMContentLoaded', function() {
    // Add loading state to delete buttons
    const deleteButtons = document.querySelectorAll('form[onsubmit*="delete"] button[type="submit"]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const form = this.closest('form');
            const isDeleteAll = form.querySelector('input[value="delete_all_read"]');

            if (isDeleteAll) {
                e.preventDefault();
                if (confirm('Are you sure you want to delete all read notifications? This action cannot be undone.')) {
                    this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Deleting...';
                    this.disabled = true;
                    form.submit();
                }
            } else {
                e.preventDefault();
                if (confirm('Are you sure you want to delete this notification?')) {
                    this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Deleting...';
                    this.disabled = true;
                    form.submit();
                }
            }
        });
    });

    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.classList.contains('show')) {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 150);
            }
        }, 5000);
    });
});

// Add spinning animation for loading states
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>

<?php include 'includes/footer.php'; ?>
