<?php
/**
 * User Logout
 * 
 * Handles user logout and session cleanup
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Log logout activity if user was authenticated
if (isset($_SESSION['user_id'])) {
    $userAuth->logUserActivity($_SESSION['user_id'], 'logout', 'User logged out');
}

// Clear all session variables
$_SESSION = array();

// Delete the session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

// Get site settings for branding
$sitename = get_organization_name() . ' - Logout';
try {
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
    $stmt->execute();
    $siteNameResult = $stmt->fetch();
    if ($siteNameResult) {
        $sitename = $siteNameResult['setting_value'];
    }
} catch (Exception $e) {
    // Use default site name if database query fails
    $sitename = get_organization_name() . ' - Logout';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logged Out - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .logout-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }
        
        .logout-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1.5rem;
        }
        
        .logout-title {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .logout-message {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-outline-secondary {
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            border-color: #6c757d;
            color: #6c757d;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-outline-secondary:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }
        
        .logout-links {
            margin-top: 2rem;
        }
        
        .logout-links .btn {
            margin: 0.5rem;
        }
        
        .site-info {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #dee2e6;
        }
        
        .site-info h6 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .site-info p {
            color: #6c757d;
            margin: 0;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <div class="logout-icon">
            <i class="bi bi-check-circle-fill"></i>
        </div>
        
        <h1 class="logout-title">Successfully Logged Out</h1>
        
        <p class="logout-message">
            Thank you for using our system. You have been safely logged out and your session has been ended.
        </p>
        
        <div class="logout-links">
            <a href="login.php" class="btn btn-primary">
                <i class="bi bi-box-arrow-in-right"></i> Login Again
            </a>
            <a href="../../index.html" class="btn btn-outline-secondary">
                <i class="bi bi-house"></i> Go Home
            </a>
        </div>
        
        <div class="site-info">
            <h6><?php echo htmlspecialchars($sitename); ?></h6>
            <p>Your trusted <?php echo strtolower(get_organization_type()); ?> management system</p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-redirect to login page after 10 seconds
        setTimeout(function() {
            window.location.href = 'login.php';
        }, 10000);
        
        // Show countdown
        let countdown = 10;
        const countdownElement = document.createElement('div');
        countdownElement.className = 'mt-3 text-muted';
        countdownElement.innerHTML = '<small>Redirecting to login page in <span id="countdown">10</span> seconds...</small>';
        document.querySelector('.logout-container').appendChild(countdownElement);
        
        const countdownSpan = document.getElementById('countdown');
        const countdownInterval = setInterval(function() {
            countdown--;
            countdownSpan.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(countdownInterval);
            }
        }, 1000);
    </script>
</body>
</html>
