<?php
/**
 * Reusable Pagination Component
 * Provides consistent pagination functionality across the application
 */

/**
 * Generate pagination HTML
 * 
 * @param int $current_page Current page number
 * @param int $total_pages Total number of pages
 * @param int $total_records Total number of records
 * @param int $records_per_page Records per page
 * @param string $base_url Base URL for pagination links
 * @param array $url_params Additional URL parameters to preserve
 * @param string $page_param Name of the page parameter (default: 'page')
 * @return string HTML for pagination
 */
function generate_pagination($current_page, $total_pages, $total_records, $records_per_page, $base_url, $url_params = [], $page_param = 'page') {
    if ($total_pages <= 1) {
        return '';
    }
    
    // Calculate record range
    $start_record = (($current_page - 1) * $records_per_page) + 1;
    $end_record = min($current_page * $records_per_page, $total_records);
    
    // Build URL parameters
    $url_params_string = '';
    if (!empty($url_params)) {
        foreach ($url_params as $key => $value) {
            if ($key !== $page_param && $value !== '') {
                $url_params_string .= '&' . urlencode($key) . '=' . urlencode($value);
            }
        }
    }
    
    $html = '<div class="d-flex justify-content-between align-items-center mt-3">';
    
    // Record count info
    $html .= '<div class="pagination-info">';
    $html .= '<span class="text-muted">Showing ' . number_format($start_record) . '-' . number_format($end_record) . ' of ' . number_format($total_records) . ' records</span>';
    $html .= '</div>';
    
    // Pagination controls
    $html .= '<div class="d-flex align-items-center gap-2">';
    
    // Records per page selector
    $html .= '<div class="d-flex align-items-center me-3">';
    $html .= '<label class="form-label me-2 mb-0 text-nowrap">Per page:</label>';
    $html .= '<select class="form-select form-select-sm" style="width: auto;" onchange="changePaginationLimit(this.value)">';
    foreach ([10, 20, 50, 100] as $limit) {
        $selected = ($limit == $records_per_page) ? 'selected' : '';
        $html .= '<option value="' . $limit . '" ' . $selected . '>' . $limit . '</option>';
    }
    $html .= '</select>';
    $html .= '</div>';
    
    // Go to page input
    $html .= '<div class="d-flex align-items-center me-3">';
    $html .= '<label class="form-label me-2 mb-0 text-nowrap">Go to:</label>';
    $html .= '<input type="number" class="form-control form-control-sm" style="width: 80px;" min="1" max="' . $total_pages . '" value="' . $current_page . '" onchange="goToPage(this.value)" placeholder="Page">';
    $html .= '</div>';
    
    // Pagination navigation
    $html .= '<nav aria-label="Page navigation">';
    $html .= '<ul class="pagination pagination-sm mb-0">';
    
    // Previous button
    if ($current_page > 1) {
        $prev_url = $base_url . '?' . $page_param . '=' . ($current_page - 1) . $url_params_string;
        $html .= '<li class="page-item">';
        $html .= '<a class="page-link" href="' . htmlspecialchars($prev_url) . '" aria-label="Previous">';
        $html .= '<span aria-hidden="true">&laquo;</span>';
        $html .= '</a>';
        $html .= '</li>';
    } else {
        $html .= '<li class="page-item disabled">';
        $html .= '<span class="page-link" aria-label="Previous">';
        $html .= '<span aria-hidden="true">&laquo;</span>';
        $html .= '</span>';
        $html .= '</li>';
    }
    
    // Page numbers
    $start_page = max(1, $current_page - 2);
    $end_page = min($total_pages, $current_page + 2);
    
    // Show first page if not in range
    if ($start_page > 1) {
        $first_url = $base_url . '?' . $page_param . '=1' . $url_params_string;
        $html .= '<li class="page-item">';
        $html .= '<a class="page-link" href="' . htmlspecialchars($first_url) . '">1</a>';
        $html .= '</li>';
        if ($start_page > 2) {
            $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // Page number links
    for ($i = $start_page; $i <= $end_page; $i++) {
        if ($i == $current_page) {
            $html .= '<li class="page-item active">';
            $html .= '<span class="page-link">' . $i . '</span>';
            $html .= '</li>';
        } else {
            $page_url = $base_url . '?' . $page_param . '=' . $i . $url_params_string;
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . htmlspecialchars($page_url) . '">' . $i . '</a>';
            $html .= '</li>';
        }
    }
    
    // Show last page if not in range
    if ($end_page < $total_pages) {
        if ($end_page < $total_pages - 1) {
            $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        $last_url = $base_url . '?' . $page_param . '=' . $total_pages . $url_params_string;
        $html .= '<li class="page-item">';
        $html .= '<a class="page-link" href="' . htmlspecialchars($last_url) . '">' . $total_pages . '</a>';
        $html .= '</li>';
    }
    
    // Next button
    if ($current_page < $total_pages) {
        $next_url = $base_url . '?' . $page_param . '=' . ($current_page + 1) . $url_params_string;
        $html .= '<li class="page-item">';
        $html .= '<a class="page-link" href="' . htmlspecialchars($next_url) . '" aria-label="Next">';
        $html .= '<span aria-hidden="true">&raquo;</span>';
        $html .= '</a>';
        $html .= '</li>';
    } else {
        $html .= '<li class="page-item disabled">';
        $html .= '<span class="page-link" aria-label="Next">';
        $html .= '<span aria-hidden="true">&raquo;</span>';
        $html .= '</span>';
        $html .= '</li>';
    }
    
    $html .= '</ul>';
    $html .= '</nav>';
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Calculate pagination values
 * 
 * @param int $total_records Total number of records
 * @param int $current_page Current page number
 * @param int $records_per_page Records per page
 * @return array Array with pagination values
 */
function calculate_pagination($total_records, $current_page = 1, $records_per_page = 20) {
    $total_pages = ceil($total_records / $records_per_page);
    $current_page = max(1, min($current_page, $total_pages));
    $offset = ($current_page - 1) * $records_per_page;
    
    return [
        'total_records' => $total_records,
        'total_pages' => $total_pages,
        'current_page' => $current_page,
        'records_per_page' => $records_per_page,
        'offset' => $offset,
        'start_record' => $offset + 1,
        'end_record' => min($offset + $records_per_page, $total_records)
    ];
}

/**
 * Generate JavaScript for pagination functionality
 * 
 * @param string $base_url Base URL for pagination
 * @param array $url_params URL parameters to preserve
 * @param string $page_param Page parameter name
 * @param string $limit_param Limit parameter name
 * @return string JavaScript code
 */
function generate_pagination_js($base_url, $url_params = [], $page_param = 'page', $limit_param = 'limit') {
    $url_params_js = json_encode($url_params);
    
    return "
    <script>
    function changePaginationLimit(newLimit) {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('{$limit_param}', newLimit);
        urlParams.set('{$page_param}', '1'); // Reset to first page
        window.location.href = '{$base_url}?' + urlParams.toString();
    }
    
    function goToPage(pageNumber) {
        const totalPages = " . (isset($GLOBALS['total_pages']) ? $GLOBALS['total_pages'] : 1) . ";
        pageNumber = parseInt(pageNumber);
        
        if (pageNumber < 1 || pageNumber > totalPages || isNaN(pageNumber)) {
            alert('Please enter a valid page number between 1 and ' + totalPages);
            return;
        }
        
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('{$page_param}', pageNumber);
        window.location.href = '{$base_url}?' + urlParams.toString();
    }
    </script>
    ";
}
?>
