<?php
/**
 * Theme CSS and Logo Include for User Pages
 *
 * This file includes the custom theme CSS and logo from admin appearance settings
 * Include this file in the <head> section of user pages after Bootstrap CSS
 */

// Include config to access settings functions
if (!function_exists('get_site_setting')) {
    require_once '../config.php';
}

// Load static CSS file instead of dynamic injection to prevent blue flash
$css_file_path = dirname(__DIR__) . '/admin/css/custom-theme.css';
$cache_file_path = dirname(__DIR__) . '/cache/theme-cache.css';

// Generate URLs using dynamic base URL
$css_file_url = get_base_url() . '/admin/css/custom-theme.css';
$cache_file_url = get_base_url() . '/cache/theme-cache.css';

// Check if CSS file exists, if not try cache file
if (file_exists($css_file_path)) {
    echo '<link rel="stylesheet" href="' . $css_file_url . '?v=' . filemtime($css_file_path) . '">' . "\n";
} elseif (file_exists($cache_file_path)) {
    echo '<link rel="stylesheet" href="' . $cache_file_url . '?v=' . filemtime($cache_file_path) . '">' . "\n";
} else {
    // Fallback: generate CSS inline if no files exist
    $primary_color = get_site_setting('primary_color', '#495057');
    $secondary_color = get_site_setting('secondary_color', '#6c757d');
    $font_family = get_site_setting('primary_font', 'Inter');
    $font_size = get_site_setting('font_size_base', '16');
    $line_height = get_site_setting('line_height_base', '1.5');
    $border_radius = get_site_setting('border_radius', '0.375');

    echo '<style>';
    echo ':root {';
    echo '--bs-primary: ' . $primary_color . ';';
    echo '--bs-secondary: ' . $secondary_color . ';';
    echo '--bs-font-sans-serif: \'' . $font_family . '\', system-ui, -apple-system, sans-serif;';
    echo '--bs-body-font-size: ' . $font_size . 'px;';
    echo '--bs-body-line-height: ' . $line_height . ';';
    echo '--bs-border-radius: ' . $border_radius . 'rem;';
    echo '}';
    echo '</style>' . "\n";
}
?>

<!-- Load favicon if available -->
<?php
$favicon_path = '';

// First try to get favicon from the new logo management system
$favicon_32_path = get_site_setting('favicon_32', '');
if ($favicon_32_path) {
    // Check if file exists in church directory (correct location)
    // __DIR__ is church/user/includes, dirname(__DIR__) is church/user, dirname(dirname(__DIR__)) is church
    $full_path = dirname(dirname(__DIR__)) . '/' . $favicon_32_path;
    if (file_exists($full_path)) {
        $favicon_path = $favicon_32_path;
    }
}

if (!$favicon_path) {
    // Fallback to favicon_logo setting (used by admin)
    $favicon_logo_path = get_site_setting('favicon_logo', '');
    if ($favicon_logo_path) {
        // Check if file exists in church directory
        if (file_exists(dirname(dirname(__DIR__)) . '/' . $favicon_logo_path)) {
            $favicon_path = $favicon_logo_path;
        }
    }
}

if (!$favicon_path) {
    // Final fallback to old favicon_path setting
    $favicon_path_old = get_site_setting('favicon_path', '');
    if ($favicon_path_old) {
        // Check if file exists in church directory
        if (file_exists(dirname(dirname(__DIR__)) . '/' . $favicon_path_old)) {
            $favicon_path = $favicon_path_old;
        }
    }
}

if ($favicon_path && file_exists(dirname(dirname(__DIR__)) . '/' . $favicon_path)): ?>
    <link rel="icon" type="image/x-icon" href="<?php echo get_base_url() . '/' . $favicon_path; ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo get_base_url() . '/' . $favicon_path; ?>">
    <?php
    // Also add 16x16 favicon if available
    $favicon_16_path = get_site_setting('favicon_16', '');
    if ($favicon_16_path && file_exists(dirname(dirname(__DIR__)) . '/' . $favicon_16_path)): ?>
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo get_base_url() . '/' . $favicon_16_path; ?>">
    <?php endif; ?>
<?php endif; ?>

<script>
// Load logo and organization name dynamically
document.addEventListener('DOMContentLoaded', function() {
    // Get logo and organization info
    <?php
    // Check for header logo first (preferred for navbar), then main logo
    $header_logo_path = get_site_setting('header_logo', '');
    $main_logo_path = get_site_setting('main_logo', '');
    $logo_path = '';

    if ($header_logo_path && file_exists(dirname(__DIR__) . '/' . $header_logo_path)) {
        $logo_path = $header_logo_path;
    } elseif ($main_logo_path && file_exists(dirname(__DIR__) . '/' . $main_logo_path)) {
        $logo_path = $main_logo_path;
    }

    $organization_name = get_organization_name();
    ?>

    const logoPath = '<?php echo $logo_path ? get_base_url() . '/' . $logo_path : ''; ?>';
    const orgName = '<?php echo htmlspecialchars($organization_name, ENT_QUOTES); ?>';

    // Update navbar logo and title
    const navbarBrand = document.querySelector('.navbar-brand');
    if (navbarBrand) {
        // Add logo if available
        if (logoPath) {
            // Check if logo image already exists
            let logoImg = navbarBrand.querySelector('img');
            if (!logoImg) {
                logoImg = document.createElement('img');
                logoImg.style.height = '40px';
                logoImg.style.marginRight = '10px';
                logoImg.alt = 'Logo';
                navbarBrand.insertBefore(logoImg, navbarBrand.firstChild);
            }
            logoImg.src = logoPath;
            logoImg.style.display = 'inline-block';
        }

        // Update organization name - handle different navbar structures
        if (orgName) {
            // Method 1: Look for span element (dashboard style)
            const navbarTitle = navbarBrand.querySelector('span');
            if (navbarTitle) {
                navbarTitle.textContent = orgName;
            } else {
                // Method 2: Update text content directly (events style)
                // Get current text content and replace the site name part
                const currentText = navbarBrand.textContent || navbarBrand.innerText;
                if (currentText) {
                    // Replace common site names with dynamic organization name
                    const updatedText = currentText
                        .replace('Church Management System', orgName)
                        .replace('Freedom Assembly Church', orgName)
                        .replace('Organization Management System', orgName)
                        .replace(/^\s*\w+\s+/, ''); // Remove icon text if present

                    // Update the text content while preserving the icon
                    const icon = navbarBrand.querySelector('i');
                    if (icon) {
                        navbarBrand.innerHTML = '';
                        navbarBrand.appendChild(icon);
                        navbarBrand.appendChild(document.createTextNode(' ' + orgName));
                    } else {
                        navbarBrand.textContent = orgName;
                    }
                }
            }
        }
    }

    // Update page title - replace any hardcoded organization names
    if (orgName) {
        if (document.title.includes('Freedom Assembly Church')) {
            document.title = document.title.replace('Freedom Assembly Church', orgName);
        }
        if (document.title.includes('Church Management System')) {
            document.title = document.title.replace('Church Management System', orgName);
        }
        if (document.title.includes('Organization Management System')) {
            document.title = document.title.replace('Organization Management System', orgName);
        }
    }
});
</script>
