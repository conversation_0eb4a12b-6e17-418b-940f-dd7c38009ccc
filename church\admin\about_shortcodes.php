<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';
require_once '../send_birthday_reminders.php';

// Set page variables
$page_title = 'About & Shortcodes';
$page_header = 'About & Shortcodes';
$page_description = 'Information about the system and available shortcodes for templates.';

// Include header
include 'includes/header.php';
?>

<div class="row">
    <!-- System Information -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-info-circle me-1"></i>
                About the System
            </div>
            <div class="card-body">
                <h5 class="card-title"><?php echo htmlspecialchars(get_organization_name()); ?> <?php echo ucfirst(get_member_term()); ?> System</h5>
                <p class="card-text">This system helps you manage <?php echo strtolower(get_organization_type()); ?> <?php echo strtolower(get_member_term(true)); ?> and send automated birthday emails and notifications.</p>
                
                <div class="mt-4">
                    <h6><i class="bi bi-stars me-2"></i>Key Features:</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item"><i class="bi bi-people me-2 text-primary"></i>Member Management</li>
                        <li class="list-group-item"><i class="bi bi-gift me-2 text-primary"></i>Automated Birthday Emails</li>
                        <li class="list-group-item"><i class="bi bi-bell me-2 text-primary"></i>Birthday Notifications</li>
                        <li class="list-group-item"><i class="bi bi-envelope me-2 text-primary"></i>Customizable Email Templates</li>
                        <li class="list-group-item"><i class="bi bi-graph-up me-2 text-primary"></i>Email Analytics</li>
                    </ul>
                </div>
                
                <div class="alert alert-info mt-4">
                    <i class="bi bi-lightbulb me-2"></i>
                    <strong>Tip:</strong> Use the shortcodes listed on this page in your email templates to personalize messages.
                </div>
            </div>
        </div>
    </div>
    
    <!-- Version Information -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-code-square me-1"></i>
                System Information
            </div>
            <div class="card-body">
                <table class="table">
                    <tbody>
                        <tr>
                            <th scope="row"><i class="bi bi-tag me-2"></i>Version</th>
                            <td>1.0.0</td>
                        </tr>
                        <tr>
                            <th scope="row"><i class="bi bi-calendar-check me-2"></i>Last Updated</th>
                            <td><?php echo date("F j, Y", filemtime(__FILE__)); ?></td>
                        </tr>
                        <tr>
                            <th scope="row"><i class="bi bi-database me-2"></i>Database</th>
                            <td>MySQL</td>
                        </tr>
                        <tr>
                            <th scope="row"><i class="bi bi-code-slash me-2"></i>Backend</th>
                            <td>PHP <?php echo phpversion(); ?></td>
                        </tr>
                        <tr>
                            <th scope="row"><i class="bi bi-envelope-check me-2"></i>Email System</th>
                            <td>PHPMailer with SMTP</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                    <a href="email_templates.php" class="btn btn-outline-primary">
                        <i class="bi bi-envelope me-2"></i>Go to Templates
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Shortcodes -->
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-braces me-1"></i>
                Available Shortcodes
            </div>
            <div class="card-body">
                <p class="card-text">
                    Shortcodes can be used in your email templates to dynamically insert content. 
                    Use these shortcodes in your email templates to personalize messages for each recipient.
                </p>
                
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Important:</strong> All shortcodes must be enclosed in curly braces like <code>{shortcode}</code>.
                </div>
                
                <ul class="nav nav-tabs" id="shortcodeTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="member-tab" data-bs-toggle="tab" data-bs-target="#member" type="button" role="tab">
                            <i class="bi bi-person me-1"></i>Member Shortcodes
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="birthday-tab" data-bs-toggle="tab" data-bs-target="#birthday" type="button" role="tab">
                            <i class="bi bi-gift me-1"></i>Birthday Shortcodes
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="notification-tab" data-bs-toggle="tab" data-bs-target="#notification" type="button" role="tab">
                            <i class="bi bi-bell me-1"></i>Notification Shortcodes
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="bulk-tab" data-bs-toggle="tab" data-bs-target="#bulk" type="button" role="tab">
                            <i class="bi bi-envelope-fill me-1"></i>Bulk Email Shortcodes
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content p-3 border border-top-0 rounded-bottom" id="shortcodeTabsContent">
                    <!-- Member Shortcodes -->
                    <div class="tab-pane fade show active" id="member" role="tabpanel">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">Shortcode</th>
                                        <th scope="col">Description</th>
                                        <th scope="col">Example</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>{full_name}</code></td>
                                        <td>The member's full name</td>
                                        <td>John Doe</td>
                                    </tr>
                                    <tr>
                                        <td><code>{first_name}</code></td>
                                        <td>The member's first name</td>
                                        <td>John</td>
                                    </tr>
                                    <tr>
                                        <td><code>{email}</code></td>
                                        <td>The member's email address</td>
                                        <td><EMAIL></td>
                                    </tr>
                                    <tr>
                                        <td><code>{phone}</code></td>
                                        <td>The member's phone number</td>
                                        <td>(*************</td>
                                    </tr>
                                    <tr>
                                        <td><code>{member_image}</code></td>
                                        <td>URL to the member's profile image</td>
                                        <td>https://example.com/uploads/john_doe.jpg</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Birthday Shortcodes -->
                    <div class="tab-pane fade" id="birthday" role="tabpanel">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">Shortcode</th>
                                        <th scope="col">Description</th>
                                        <th scope="col">Example</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>{birth_date}</code></td>
                                        <td>The member's birth date</td>
                                        <td>1990-05-15</td>
                                    </tr>
                                    <tr>
                                        <td><code>{birth_date_formatted}</code></td>
                                        <td>Formatted birth date</td>
                                        <td>May 15, 1990</td>
                                    </tr>
                                    <tr>
                                        <td><code>{age}</code></td>
                                        <td>The member's current age</td>
                                        <td>33</td>
                                    </tr>
                                    <tr>
                                        <td><code>{upcoming_birthday_formatted}</code></td>
                                        <td>Formatted upcoming birthday date</td>
                                        <td>May 15, 2023</td>
                                    </tr>
                                    <tr>
                                        <td><code>{days_text}</code></td>
                                        <td>Text indicating when the birthday is</td>
                                        <td>"tomorrow", "in 3 days", etc.</td>
                                    </tr>
                                    <tr>
                                        <td><code>{birthday_member_age}</code></td>
                                        <td>Age the birthday member will be turning</td>
                                        <td>35</td>
                                    </tr>
                                    <tr>
                                        <td><code>{birthday_member_full_name}</code></td>
                                        <td>Full name of the member having a birthday</td>
                                        <td>Jane Marie Smith</td>
                                    </tr>
                                    <tr>
                                        <td><code>{birthday_member_phone}</code></td>
                                        <td>Phone number of the member having a birthday</td>
                                        <td>(*************</td>
                                    </tr>
                                    <tr>
                                        <td><code>{upcoming_birthday_formatted}</code></td>
                                        <td>Formatted birthday date (with full year and day of week)</td>
                                        <td>Monday, May 15, 2023</td>
                                    </tr>
                                    <tr>
                                        <td><code>{days_text}</code></td>
                                        <td>Text describing when the birthday is occurring</td>
                                        <td>"today", "tomorrow", "in 3 days"</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Notification Shortcodes -->
                    <div class="tab-pane fade" id="notification" role="tabpanel">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">Shortcode</th>
                                        <th scope="col">Description</th>
                                        <th scope="col">Example</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>{birthday_member_name}</code></td>
                                        <td>Name of the member having a birthday</td>
                                        <td>Jane Smith</td>
                                    </tr>
                                    <tr>
                                        <td><code>{birthday_member_email}</code></td>
                                        <td>Email address of the member having a birthday</td>
                                        <td><EMAIL></td>
                                    </tr>
                                    <tr>
                                        <td><code>{birthday_member_image}</code></td>
                                        <td>URL to the birthday member's profile image</td>
                                        <td>https://example.com/uploads/jane_smith.jpg</td>
                                    </tr>
                                    <tr>
                                        <td><code>{birthday_member_age}</code></td>
                                        <td>Age the birthday member will be turning</td>
                                        <td>35</td>
                                    </tr>
                                    <tr>
                                        <td><code>{days_until_birthday}</code></td>
                                        <td>Number of days until the birthday</td>
                                        <td>3</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Bulk Email Shortcodes -->
                    <div class="tab-pane fade" id="bulk" role="tabpanel">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">Shortcode</th>
                                        <th scope="col">Description</th>
                                        <th scope="col">Example</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>{recipient_full_name}</code></td>
                                        <td>Full name of the person receiving the email</td>
                                        <td>John Doe</td>
                                    </tr>
                                    <tr>
                                        <td><code>{recipient_first_name}</code></td>
                                        <td>First name of the recipient</td>
                                        <td>John</td>
                                    </tr>
                                    <tr>
                                        <td><code>{recipient_email}</code></td>
                                        <td>Email address of the recipient</td>
                                        <td><EMAIL></td>
                                    </tr>
                                    <tr>
                                        <td><code>{sender_name}</code></td>
                                        <td>Name of the admin sending the email</td>
                                        <td>Admin Name</td>
                                    </tr>
                                    <tr>
                                        <td><code>{sender_email}</code></td>
                                        <td>Email of the admin sending the email</td>
                                        <td><EMAIL></td>
                                    </tr>
                                    <tr>
                                        <td><code>{total_recipients}</code></td>
                                        <td>Total number of selected recipients</td>
                                        <td>50</td>
                                    </tr>
                                    <tr>
                                        <td><code>{church_name}</code></td>
                                        <td>Name of the church</td>
                                        <td>Freedom Assembly Church</td>
                                    </tr>
                                    <tr>
                                        <td><code>{recipient_phone}</code></td>
                                        <td>Phone number of the recipient</td>
                                        <td>+1234567890</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Note:</strong> These shortcodes are specifically designed for bulk emails and will be replaced with each recipient's information when sending.
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header bg-light">
                        <i class="bi bi-code-square me-1"></i>Example Usage
                    </div>
                    <div class="card-body">
                        <pre><code>Hello {full_name},

We wanted to remind you that your birthday is coming up {days_text} on {upcoming_birthday_formatted}. 
You will be turning {age} years old!

Blessings,
Freedom Assembly Church</code></pre>

                        <hr>
                        <h6>Example with Button to Email Birthday Member:</h6>
                        <pre><code>&lt;a href="mailto:{birthday_member_email}" class="button" style="display: inline-block; padding: 10px 20px; background-color: #4a6cf7; color: white; text-decoration: none; border-radius: 5px;"&gt;
    Send Birthday Wishes to {birthday_member_name}
&lt;/a&gt;</code></pre>

                        <hr>
                        <h6>Complete Interactive Example with Pre-filled Email:</h6>
                        <pre><code>&lt;a href="mailto:{birthday_member_email}?subject=Happy%20Birthday%20Wishes&body=Dear%20{birthday_member_name},%0A%0AI%20wanted%20to%20wish%20you%20a%20very%20happy%20birthday!%20May%20your%20day%20be%20filled%20with%20joy,%20and%20God's%20blessings%20be%20upon%20you%20in%20this%20new%20year%20of%20your%20life.%0A%0ABlessings,%0A{full_name}" class="button" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #ff5b94, #ff9b7d); color: white; text-decoration: none; border-radius: 30px; font-weight: bold; margin: 8px; box-shadow: 0 4px 10px rgba(255, 91, 148, 0.3);"&gt;
    Send Birthday Wishes
&lt;/a&gt;</code></pre>
                        
                        <div class="alert alert-info mt-3">
                            <i class="bi bi-lightbulb me-2"></i>
                            <strong>Tip:</strong> A new "Interactive Birthday Notification" template has been added to the system that includes interactive buttons for sending emails and making calls to birthday members.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    // Enable Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })
});
</script>

<?php
// Include footer
include 'includes/footer.php';
?> 