Type hints
-----
<?php

function a($b, array $c, callable $d, E $f) {}
-----
array(
    0: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: a
        )
        params: array(
            0: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: b
                )
                default: null
                hooks: array(
                )
            )
            1: Param(
                attrGroups: array(
                )
                flags: 0
                type: Identifier(
                    name: array
                )
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: c
                )
                default: null
                hooks: array(
                )
            )
            2: Param(
                attrGroups: array(
                )
                flags: 0
                type: Identifier(
                    name: callable
                )
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: d
                )
                default: null
                hooks: array(
                )
            )
            3: Param(
                attrGroups: array(
                )
                flags: 0
                type: Name(
                    name: E
                )
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: f
                )
                default: null
                hooks: array(
                )
            )
        )
        returnType: null
        stmts: array(
        )
    )
)
