Lexer errors
-----
<?php

$a = 42;
/*
$b = 24;
-----
Unterminated comment from 4:1 to 5:9
array(
    0: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_Variable(
                name: a
            )
            expr: <PERSON>ala<PERSON>_Int(
                value: 42
            )
        )
    )
    1: Stmt_Nop(
        comments: array(
            0: /*
            $b = 24;
        )
    )
)
-----
<?php

$a = 42;
@@{ "\1" }@@
$b = 24;
-----
!!positions
Unexpected character "" (ASCII 1) from 4:1 to 4:1
array(
    0: Stmt_Expression[3:1 - 3:8](
        expr: Expr_Assign[3:1 - 3:7](
            var: Expr_Variable[3:1 - 3:2](
                name: a
            )
            expr: Scalar_Int[3:6 - 3:7](
                value: 42
            )
        )
    )
    1: Stmt_Expression[5:1 - 5:8](
        expr: Expr_Assign[5:1 - 5:7](
            var: Expr_Variable[5:1 - 5:2](
                name: b
            )
            expr: <PERSON><PERSON>r_Int[5:6 - 5:7](
                value: 24
            )
        )
    )
)
-----
<?php

$a = 42;
@@{ "\0" }@@
$b = 24;
-----
!!positions
Unexpected null byte from 4:1 to 4:1
array(
    0: Stmt_Expression[3:1 - 3:8](
        expr: Expr_Assign[3:1 - 3:7](
            var: Expr_Variable[3:1 - 3:2](
                name: a
            )
            expr: Scalar_Int[3:6 - 3:7](
                value: 42
            )
        )
    )
    1: Stmt_Expression[5:1 - 5:8](
        expr: Expr_Assign[5:1 - 5:7](
            var: Expr_Variable[5:1 - 5:2](
                name: b
            )
            expr: Scalar_Int[5:6 - 5:7](
                value: 24
            )
        )
    )
)
-----
<?php

$a = 1;
@@{ "\1" }@@
$b = 2;
@@{ "\2" }@@
$c = 3;
-----
!!positions
Unexpected character "" (ASCII 1) from 4:1 to 4:1
Unexpected character "" (ASCII 2) from 6:1 to 6:1
array(
    0: Stmt_Expression[3:1 - 3:7](
        expr: Expr_Assign[3:1 - 3:6](
            var: Expr_Variable[3:1 - 3:2](
                name: a
            )
            expr: Scalar_Int[3:6 - 3:6](
                value: 1
            )
        )
    )
    1: Stmt_Expression[5:1 - 5:7](
        expr: Expr_Assign[5:1 - 5:6](
            var: Expr_Variable[5:1 - 5:2](
                name: b
            )
            expr: Scalar_Int[5:6 - 5:6](
                value: 2
            )
        )
    )
    2: Stmt_Expression[7:1 - 7:7](
        expr: Expr_Assign[7:1 - 7:6](
            var: Expr_Variable[7:1 - 7:2](
                name: c
            )
            expr: Scalar_Int[7:6 - 7:6](
                value: 3
            )
        )
    )
)
-----
<?php

if ($b) {
    $a = 1;
    /* unterminated
}
-----
Unterminated comment from 5:5 to 6:2
Syntax error, unexpected EOF from 6:2 to 6:2
