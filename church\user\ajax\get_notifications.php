<?php
/**
 * Get Notifications AJAX Endpoint
 * 
 * Returns notifications for the current user
 */

session_start();
require_once '../../config.php';
require_once '../../includes/notification_functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit;
}

$userId = $_SESSION['user_id'];

try {
    // Get notifications for the user
    $notifications = getUserNotifications($pdo, $userId, 10, false);
    
    // Format the notifications with time
    foreach ($notifications as &$notification) {
        $notification['formatted_time'] = formatNotificationTime($notification['created_at']);
    }
    
    echo json_encode([
        'success' => true,
        'notifications' => $notifications
    ]);
    
} catch (Exception $e) {
    error_log("Error getting notifications: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load notifications'
    ]);
}
?>
