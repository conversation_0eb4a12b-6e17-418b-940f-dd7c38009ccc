Flexible heredoc/nowdoc (PHP 7.3)
-----
<?php

$ary = [
    <<<FOO
Test
FOO,
    <<<'BAR'
    Test
    BAR,
];

<<<'END'
 END;

<<<END

  END;

<<<END
@@{ " " }@@
  END;

<<<'END'
     a
    b

   c

  d
 e
 END;

<<<END
	    a
	   b
	  $test
	 d
	e
	END;

<<<'END'

    a

   b

  c

 d

e

END;

<<<END
	a\r\n
\ta\n
   b\r\n
  $test\n
 d\r\n
e\n
END;

<<<BAR
 $one-
 BAR;

<<<BAR
 $two -
 BAR;

<<<BAR
 $three	-
 BAR;

<<<BAR
 $four-$four
 BAR;

<<<BAR
 $five-$five-
 BAR;

<<<BAR
 $six-$six-$six
 BAR;

<<<BAR
 $seven
 -
 BAR;

<<<BAR
 $eight
  -
 BAR;

<<<BAR
$nine
BAR;

<<<BAR
 -
 BAR;

<<<BAR
  -
 BAR;
-----
array(
    0: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_Variable(
                name: ary
            )
            expr: Expr_Array(
                items: array(
                    0: ArrayItem(
                        key: null
                        value: Scalar_String(
                            value: Test
                        )
                        byRef: false
                        unpack: false
                    )
                    1: ArrayItem(
                        key: null
                        value: Scalar_String(
                            value: Test
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Scalar_String(
            value:
        )
    )
    2: Stmt_Expression(
        expr: Scalar_String(
            value:
        )
    )
    3: Stmt_Expression(
        expr: Scalar_String(
            value:
        )
    )
    4: Stmt_Expression(
        expr: Scalar_String(
            value:     a
               b

              c

             d
            e
        )
    )
    5: Stmt_Expression(
        expr: Scalar_InterpolatedString(
            parts: array(
                0: InterpolatedStringPart(
                    value:     a
                       b

                )
                1: Expr_Variable(
                    name: test
                )
                2: InterpolatedStringPart(
                    value:
                     d
                    e
                )
            )
        )
    )
    6: Stmt_Expression(
        expr: Scalar_String(
            value:
                a

               b

              c

             d

            e

        )
    )
    7: Stmt_Expression(
        expr: Scalar_InterpolatedString(
            parts: array(
                0: InterpolatedStringPart(
                    value: 	a

                    @@{ "\t" }@@a

                       b


                )
                1: Expr_Variable(
                    name: test
                )
                2: InterpolatedStringPart(
                    value:

                     d

                    e

                )
            )
        )
    )
    8: Stmt_Expression(
        expr: Scalar_InterpolatedString(
            parts: array(
                0: Expr_Variable(
                    name: one
                )
                1: InterpolatedStringPart(
                    value: -
                )
            )
        )
    )
    9: Stmt_Expression(
        expr: Scalar_InterpolatedString(
            parts: array(
                0: Expr_Variable(
                    name: two
                )
                1: InterpolatedStringPart(
                    value:  -
                )
            )
        )
    )
    10: Stmt_Expression(
        expr: Scalar_InterpolatedString(
            parts: array(
                0: Expr_Variable(
                    name: three
                )
                1: InterpolatedStringPart(
                    value: 	-
                )
            )
        )
    )
    11: Stmt_Expression(
        expr: Scalar_InterpolatedString(
            parts: array(
                0: Expr_Variable(
                    name: four
                )
                1: InterpolatedStringPart(
                    value: -
                )
                2: Expr_Variable(
                    name: four
                )
            )
        )
    )
    12: Stmt_Expression(
        expr: Scalar_InterpolatedString(
            parts: array(
                0: Expr_Variable(
                    name: five
                )
                1: InterpolatedStringPart(
                    value: -
                )
                2: Expr_Variable(
                    name: five
                )
                3: InterpolatedStringPart(
                    value: -
                )
            )
        )
    )
    13: Stmt_Expression(
        expr: Scalar_InterpolatedString(
            parts: array(
                0: Expr_Variable(
                    name: six
                )
                1: InterpolatedStringPart(
                    value: -
                )
                2: Expr_Variable(
                    name: six
                )
                3: InterpolatedStringPart(
                    value: -
                )
                4: Expr_Variable(
                    name: six
                )
            )
        )
    )
    14: Stmt_Expression(
        expr: Scalar_InterpolatedString(
            parts: array(
                0: Expr_Variable(
                    name: seven
                )
                1: InterpolatedStringPart(
                    value:
                    -
                )
            )
        )
    )
    15: Stmt_Expression(
        expr: Scalar_InterpolatedString(
            parts: array(
                0: Expr_Variable(
                    name: eight
                )
                1: InterpolatedStringPart(
                    value:
                     -
                )
            )
        )
    )
    16: Stmt_Expression(
        expr: Scalar_InterpolatedString(
            parts: array(
                0: Expr_Variable(
                    name: nine
                )
            )
        )
    )
    17: Stmt_Expression(
        expr: Scalar_String(
            value: -
        )
    )
    18: Stmt_Expression(
        expr: Scalar_String(
            value:  -
        )
    )
)
