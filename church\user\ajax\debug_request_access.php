<?php
/**
 * Debug Request Access
 * 
 * Helps debug why comments might not be loading for specific requests
 */

// Set headers first before any output
header('Content-Type: application/json');

// Prevent any output before JSON
ob_start();

session_start();

// Try different paths to find config.php
$config_paths = [
    '../../config.php',
    '../../../config.php',
    dirname(__DIR__, 2) . '/config.php',
    __DIR__ . '/../../config.php'
];

$config_loaded = false;
foreach ($config_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $config_loaded = true;
        break;
    }
}

if (!$config_loaded) {
    ob_end_clean();
    echo json_encode(['success' => false, 'error' => 'Configuration file not found']);
    exit;
}

// Clear any output that might have been generated
ob_end_clean();

// Suppress error display
ini_set('display_errors', 0);

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit;
}

$requestId = $_GET['request_id'] ?? null;

if (!$requestId || !is_numeric($requestId)) {
    echo json_encode(['success' => false, 'error' => 'Valid request ID required']);
    exit;
}

try {
    // Get detailed request information
    $stmt = $pdo->prepare("
        SELECT pr.*, m.full_name, m.first_name, m.last_name,
               COUNT(prr.id) as comment_count,
               COALESCE(pr.allow_comments, 1) as allow_comments
        FROM prayer_requests pr
        LEFT JOIN members m ON pr.member_id = m.id
        LEFT JOIN prayer_responses prr ON pr.id = prr.prayer_request_id
        WHERE pr.id = ?
        GROUP BY pr.id
    ");
    $stmt->execute([$requestId]);
    $request = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$request) {
        echo json_encode([
            'success' => false, 
            'error' => 'Request not found',
            'debug_info' => [
                'request_id' => $requestId,
                'user_id' => $_SESSION['user_id']
            ]
        ]);
        exit;
    }
    
    // Check access permissions
    $canView = false;
    $accessReason = '';
    
    if ($request['member_id'] == $_SESSION['user_id']) {
        $canView = true;
        $accessReason = 'owner';
    } elseif ($request['privacy_level'] === 'public') {
        $canView = true;
        $accessReason = 'public';
    } elseif ($request['privacy_level'] === 'members') {
        $canView = true;
        $accessReason = 'members';
    } elseif ($request['privacy_level'] === 'private') {
        $canView = false;
        $accessReason = 'private_denied';
    } else {
        $canView = false;
        $accessReason = 'unknown_privacy_level';
    }
    
    // Get session info
    $sessionInfo = [
        'user_id' => $_SESSION['user_id'] ?? null,
        'username' => $_SESSION['username'] ?? null,
        'full_name' => $_SESSION['full_name'] ?? null
    ];
    
    echo json_encode([
        'success' => true,
        'can_view' => $canView,
        'access_reason' => $accessReason,
        'request_info' => [
            'id' => $request['id'],
            'title' => $request['title'],
            'privacy_level' => $request['privacy_level'],
            'status' => $request['status'],
            'member_id' => $request['member_id'],
            'owner_name' => $request['full_name'],
            'comment_count' => $request['comment_count'],
            'allow_comments' => $request['allow_comments'],
            'created_at' => $request['created_at']
        ],
        'session_info' => $sessionInfo,
        'debug_info' => [
            'is_owner' => ($request['member_id'] == $_SESSION['user_id']),
            'privacy_check_public' => ($request['privacy_level'] === 'public'),
            'privacy_check_members' => ($request['privacy_level'] === 'members'),
            'privacy_check_private' => ($request['privacy_level'] === 'private')
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Error in debug_request_access: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Debug failed: ' . $e->getMessage(),
        'debug_info' => [
            'request_id' => $requestId,
            'user_id' => $_SESSION['user_id'] ?? null
        ]
    ]);
}
?>
