<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

echo "<h1>Church System Diagnostic</h1>";

echo "<h2>1. File Path Information</h2>";
echo "<p><strong>Current file path:</strong> " . __FILE__ . "</p>";
echo "<p><strong>Document root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Script name:</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p><strong>Request URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>Server name:</strong> " . $_SERVER['SERVER_NAME'] . "</p>";

echo "<h2>2. Settings.php File Check</h2>";
$settingsFile = __DIR__ . '/settings.php';
echo "<p><strong>Settings file path:</strong> $settingsFile</p>";
echo "<p><strong>Settings file exists:</strong> " . (file_exists($settingsFile) ? 'YES' : 'NO') . "</p>";

if (file_exists($settingsFile)) {
    echo "<p><strong>Settings file size:</strong> " . filesize($settingsFile) . " bytes</p>";
    echo "<p><strong>Settings file modified:</strong> " . date('Y-m-d H:i:s', filemtime($settingsFile)) . "</p>";
    
    // Check if our email sync code is in the file
    $content = file_get_contents($settingsFile);
    $hasEmailSync = strpos($content, 'emailSettingsData') !== false;
    echo "<p><strong>Contains email sync code:</strong> " . ($hasEmailSync ? 'YES' : 'NO') . "</p>";
    
    if ($hasEmailSync) {
        echo "<p style='color: green;'>✓ Email synchronization code is present in settings.php</p>";
    } else {
        echo "<p style='color: red;'>✗ Email synchronization code is NOT found in settings.php</p>";
    }
}

echo "<h2>3. Database Connection Test</h2>";
try {
    require_once '../config.php';
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test database tables
    echo "<h3>Database Tables Check:</h3>";
    
    // Check settings table
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM settings");
        $count = $stmt->fetchColumn();
        echo "<p>✓ Settings table exists with $count records</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Settings table error: " . $e->getMessage() . "</p>";
    }
    
    // Check email_settings table
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM email_settings");
        $count = $stmt->fetchColumn();
        echo "<p>✓ Email_settings table exists with $count records</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ Email_settings table error: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>4. Current Email Settings</h2>";
try {
    // Show current email settings from settings table
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'from_email', 'from_name', 'reply_to_email') ORDER BY setting_key");
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>From Settings Table:</h3>";
    if (empty($settings)) {
        echo "<p>No email settings found in settings table</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Key</th><th>Value</th></tr>";
        foreach ($settings as $setting) {
            echo "<tr><td>" . htmlspecialchars($setting['setting_key']) . "</td><td>" . htmlspecialchars($setting['setting_value']) . "</td></tr>";
        }
        echo "</table>";
    }
    
    // Show current email settings from email_settings table
    try {
        $stmt = $pdo->prepare("SELECT setting_key, setting_value, updated_at FROM email_settings ORDER BY setting_key");
        $stmt->execute();
        $emailSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>From Email_Settings Table:</h3>";
        if (empty($emailSettings)) {
            echo "<p>No settings found in email_settings table</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Key</th><th>Value</th><th>Updated</th></tr>";
            foreach ($emailSettings as $setting) {
                echo "<tr><td>" . htmlspecialchars($setting['setting_key']) . "</td><td>" . htmlspecialchars($setting['setting_value']) . "</td><td>" . htmlspecialchars($setting['updated_at']) . "</td></tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p style='color: orange;'>Email_settings table not accessible: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error reading settings: " . $e->getMessage() . "</p>";
}

echo "<h2>5. Test Links</h2>";
echo "<p><a href='settings.php'>Go to Settings Page</a></p>";
echo "<p><a href='email_settings.php'>Go to Email Settings Page</a></p>";
echo "<p><a href='test_email_sync.php'>Go to Email Sync Test</a></p>";

echo "<h2>6. Instructions</h2>";
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>To test the email settings synchronization:</strong></p>";
echo "<ol>";
echo "<li>Go to the Settings page using the link above</li>";
echo "<li>Click on the 'Email Config' tab</li>";
echo "<li>Make a small change to any email setting (like changing the SMTP host)</li>";
echo "<li>Click 'Save Email Settings'</li>";
echo "<li>Come back to this diagnostic page and refresh to see if the email_settings table was updated</li>";
echo "</ol>";
echo "<p><strong>If the email_settings table is not being updated:</strong></p>";
echo "<ul>";
echo "<li>Check if you're accessing the correct settings page (the one that shows 'Contains email sync code: YES' above)</li>";
echo "<li>Check the browser's developer console for any JavaScript errors</li>";
echo "<li>Try the 'Email Sync Test' link above to manually test the synchronization</li>";
echo "</ul>";
echo "</div>";

?>
