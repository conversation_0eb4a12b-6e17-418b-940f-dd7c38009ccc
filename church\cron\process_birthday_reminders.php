<?php
/**
 * Process Birthday Reminders
 * 
 * This script sends out birthday reminder emails to church members.
 * It's designed to be run as a scheduled task through cron.
 */

// Validate secure access via cron key
$expected_key = 'fac_2024_secure_cron_8x9q2p5m';
if (!isset($_GET['cron_key']) || $_GET['cron_key'] !== $expected_key) {
    http_response_code(403);
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
    exit;
}

// Set unlimited execution time for large member databases
set_time_limit(0);

// Include required files
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/email_functions.php';

// Setup logging
$logDir = __DIR__ . '/../logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}
$logFile = $logDir . '/birthday_reminders_' . date('Ymd') . '.log';
file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Birthday reminder process started' . PHP_EOL);

// Function to log messages
function logMessage($message, $logFile) {
    $logEntry = '[' . date('Y-m-d H:i:s') . '] ' . $message . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND);
    return $logEntry;
}

try {
    // Get the birthday email template
    $stmt = $pdo->prepare("
        SELECT * FROM email_templates 
        WHERE is_birthday_template = 1 
        OR template_category = 'birthday' 
        OR template_name LIKE '%birthday%' 
        LIMIT 1
    ");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        logMessage("No birthday email template found!", $logFile);
        echo json_encode(['status' => 'error', 'message' => 'No birthday email template found']);
        exit;
    }
    
    logMessage("Using template: " . $template['template_name'], $logFile);
    
    // Get email settings for sender info
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('sender_email', 'sender_name')");
    $stmt->execute();
    $settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    
    // Get members with birthdays coming up
    $currentMonth = date('m');
    $currentDay = date('d');
    $nextWeek = date('Y-m-d', strtotime('+7 days'));
    $nextWeekMonth = date('m', strtotime($nextWeek));
    $nextWeekDay = date('d', strtotime($nextWeek));
    
    // Find members with birthdays in the next 7 days
    // Use MONTH() and DAY() functions for date comparison
    $stmt = $pdo->prepare("
        SELECT *
        FROM members
        WHERE 
            email IS NOT NULL 
            AND birth_date IS NOT NULL
            AND (
                -- Current month, day is greater than or equal to today and less than or equal to a week from now
                (MONTH(birth_date) = ? AND DAY(birth_date) BETWEEN ? AND ?)
                OR 
                -- Next month (if the 7-day period spans two months)
                (? != ? AND MONTH(birth_date) = ? AND DAY(birth_date) <= ?)
            )
        ORDER BY MONTH(birth_date), DAY(birth_date)
    ");
    
    $stmt->execute([
        $currentMonth, 
        $currentDay, 
        ($currentMonth == $nextWeekMonth) ? $nextWeekDay : 31,
        $currentMonth,
        $nextWeekMonth,
        $nextWeekMonth,
        $nextWeekDay
    ]);
    
    $birthdayMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    logMessage("Found " . count($birthdayMembers) . " members with upcoming birthdays", $logFile);
    
    $sentCount = 0;
    $errorCount = 0;
    
    // Process each member with a birthday
    foreach ($birthdayMembers as $member) {
        // Calculate days until birthday
        $birthMonth = date('m', strtotime($member['birth_date']));
        $birthDay = date('d', strtotime($member['birth_date']));
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        
        // Build birthday date for this year
        $birthdayThisYear = $currentYear . '-' . $birthMonth . '-' . $birthDay;
        
        // If birthday passed, use next year
        if (strtotime($birthdayThisYear) < strtotime('today')) {
            $birthdayDate = $nextYear . '-' . $birthMonth . '-' . $birthDay;
        } else {
            $birthdayDate = $birthdayThisYear;
        }
        
        // Calculate days until birthday
        $daysUntil = floor((strtotime($birthdayDate) - time()) / 86400);
        
        // Calculate age using consistent DateTime method
        try {
            $birth = new DateTime($member['birth_date']);
            $today = new DateTime();
            $age = $today->diff($birth)->y;
        } catch (Exception $e) {
            error_log("Error calculating age for member: " . $e->getMessage());
            $age = 'Unknown';
        }
        
        // Skip if we've already sent a birthday email recently (within the last 7 days)
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM sent_emails
            WHERE 
                recipient_email = ? 
                AND subject LIKE '%birthday%'
                AND sent_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
        ");
        $stmt->execute([$member['email']]);
        $emailCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($emailCount > 0) {
            logMessage("Skipping member {$member['full_name']} - already sent a birthday email recently", $logFile);
            continue;
        }
        
        // Prepare email contents
        $subject = $template['subject'];
        $content = $template['content'];
        
        // Prepare member data for placeholders
        $memberData = [
            'full_name' => $member['full_name'],
            'first_name' => explode(' ', $member['full_name'])[0] ?? '',
            'last_name' => (count(explode(' ', $member['full_name'])) > 1) ? explode(' ', $member['full_name'], 2)[1] : '',
            'email' => $member['email'],
            'birth_date' => $member['birth_date'],
            'upcoming_birthday_date' => date('F j, Y', strtotime($birthdayDate)),
            'upcoming_birthday_day' => date('l', strtotime($birthdayDate)),
            'upcoming_birthday_formatted' => date('l, F j, Y', strtotime($birthdayDate)),
            'days_until_birthday' => $daysUntil,
            'days_text' => $daysUntil == 0 ? 'today' : 
                          ($daysUntil == 1 ? 'tomorrow' : 
                          "in $daysUntil days"),
            'age' => $age,
            'birthday_member_age' => $age,
            'birthday_member_name' => explode(' ', $member['full_name'])[0] ?? '',
            'birthday_member_full_name' => $member['full_name']
        ];
        
        // Try to send the email
        try {
            $result = sendScheduledEmail(
                $member['email'],
                $member['full_name'],
                $subject,
                $content,
                ['id' => 0, 'track_opens' => 1, 'track_clicks' => 1],
                $memberData
            );
            
            // Handle both boolean true and array with success=true as successful results
            if ($result === true || (is_array($result) && isset($result['success']) && $result['success'])) {
                $sentCount++;
                logMessage("Successfully sent birthday email to {$member['full_name']} <{$member['email']}>", $logFile);
                
                // Record in the sent_emails table
                $stmt = $pdo->prepare("
                    INSERT INTO sent_emails 
                    (recipient_email, recipient_name, subject, content, sent_at, email_type) 
                    VALUES (?, ?, ?, ?, NOW(), 'birthday')
                ");
                $stmt->execute([
                    $member['email'],
                    $member['full_name'],
                    $subject,
                    $content
                ]);
            } else {
                $errorCount++;
                $errorMessage = (is_array($result) && isset($result['message'])) ? $result['message'] : 'Unknown error';
                logMessage("Failed to send birthday email to {$member['full_name']} <{$member['email']}>: {$errorMessage}", $logFile);
            }
        } catch (Exception $e) {
            $errorCount++;
            logMessage("Error sending to {$member['full_name']} <{$member['email']}>: " . $e->getMessage(), $logFile);
        }
        
        // Small delay to prevent overwhelming email server
        usleep(100000); // 100ms
    }
    
    // Log completion
    logMessage("Birthday reminder process completed. Sent: $sentCount, Errors: $errorCount", $logFile);
    
    // Return success response
    echo json_encode([
        'status' => 'success',
        'message' => "Birthday reminder emails processed. Sent: $sentCount, Errors: $errorCount",
        'sent' => $sentCount,
        'errors' => $errorCount
    ]);
    
} catch (Exception $e) {
    logMessage("Error in birthday reminder process: " . $e->getMessage(), $logFile);
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Error processing birthday reminders: ' . $e->getMessage()
    ]);
} 