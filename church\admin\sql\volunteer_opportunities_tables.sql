-- Volunteer Opportunities Management System Tables
-- This file creates all necessary tables for the volunteer opportunities management system

-- Create volunteer_opportunities table
CREATE TABLE IF NOT EXISTS volunteer_opportunities (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    required_skills JSON,
    preferred_skills JSON,
    time_commitment VARCHAR(255),
    schedule_type ENUM('one_time', 'recurring', 'ongoing') DEFAULT 'one_time',
    start_date DATE,
    end_date DATE,
    location VARCHAR(255),
    contact_person_id INT(11) DEFAULT NULL,
    max_volunteers INT(11) DEFAULT NULL,
    min_age INT(11) DEFAULT NULL,
    background_check_required TINYINT(1) DEFAULT 0,
    training_required TINYINT(1) DEFAULT 0,
    training_description TEXT,
    status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
    created_by INT(11) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_title (title),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_contact_person_id (contact_person_id),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (contact_person_id) REFERENCES members(id) ON DELETE SET NULL
);

-- Create volunteer_applications table
CREATE TABLE IF NOT EXISTS volunteer_applications (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    opportunity_id INT(11) NOT NULL,
    member_id INT(11) NOT NULL,
    application_message TEXT,
    status ENUM('pending', 'approved', 'rejected', 'withdrawn') DEFAULT 'pending',
    reviewed_by INT(11) DEFAULT NULL,
    reviewed_at TIMESTAMP NULL,
    review_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_application (opportunity_id, member_id),
    INDEX idx_opportunity_id (opportunity_id),
    INDEX idx_member_id (member_id),
    INDEX idx_status (status),
    INDEX idx_reviewed_by (reviewed_by),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (opportunity_id) REFERENCES volunteer_opportunities(id) ON DELETE CASCADE,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
);
