Dynamic class constant fetch
-----
<?php
Foo::{bar()};
$foo::{bar()};
-----
array(
    0: Stmt_Expression(
        expr: Expr_ClassConstFetch(
            class: Name(
                name: Foo
            )
            name: Expr_FuncCall(
                name: Name(
                    name: bar
                )
                args: array(
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_ClassConstFetch(
            class: Expr_Variable(
                name: foo
            )
            name: Expr_FuncCall(
                name: Name(
                    name: bar
                )
                args: array(
                )
            )
        )
    )
)
