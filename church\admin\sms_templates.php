<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['create_template'])) {
            $templateName = trim($_POST['template_name']);
            $messageContent = trim($_POST['message_content']);
            $category = trim($_POST['category']);
            
            if (empty($templateName) || empty($messageContent)) {
                throw new Exception("Template name and message content are required.");
            }
            
            if (strlen($messageContent) > 1000) {
                throw new Exception("Message content is too long. Maximum 1000 characters allowed.");
            }
            
            $stmt = $pdo->prepare("INSERT INTO sms_templates (template_name, message_content, category, created_by) VALUES (?, ?, ?, ?)");
            $stmt->execute([$templateName, $messageContent, $category ?: null, $_SESSION['admin_id']]);
            
            $success = "SMS template created successfully!";
            
        } elseif (isset($_POST['update_template'])) {
            $templateId = intval($_POST['template_id']);
            $templateName = trim($_POST['template_name']);
            $messageContent = trim($_POST['message_content']);
            $category = trim($_POST['category']);
            $status = $_POST['status'];
            
            if (empty($templateName) || empty($messageContent)) {
                throw new Exception("Template name and message content are required.");
            }
            
            $stmt = $pdo->prepare("UPDATE sms_templates SET template_name = ?, message_content = ?, category = ?, status = ? WHERE id = ?");
            $stmt->execute([$templateName, $messageContent, $category ?: null, $status, $templateId]);
            
            $success = "SMS template updated successfully!";
            
        } elseif (isset($_POST['delete_template'])) {
            $templateId = intval($_POST['template_id']);
            
            // Check if template is being used in any campaigns
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM sms_campaigns WHERE template_id = ?");
            $stmt->execute([$templateId]);
            $usageCount = $stmt->fetchColumn();
            
            if ($usageCount > 0) {
                throw new Exception("Cannot delete template. It is being used in $usageCount SMS campaign(s).");
            }
            
            $stmt = $pdo->prepare("DELETE FROM sms_templates WHERE id = ?");
            $stmt->execute([$templateId]);
            
            $success = "SMS template deleted successfully!";
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get templates with pagination
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Search and filter
$search = trim($_GET['search'] ?? '');
$categoryFilter = trim($_GET['category'] ?? '');
$statusFilter = trim($_GET['status'] ?? '');

$whereConditions = [];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(template_name LIKE ? OR message_content LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($categoryFilter)) {
    $whereConditions[] = "category = ?";
    $params[] = $categoryFilter;
}

if (!empty($statusFilter)) {
    $whereConditions[] = "status = ?";
    $params[] = $statusFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get total count
$countQuery = "SELECT COUNT(*) FROM sms_templates $whereClause";
$stmt = $pdo->prepare($countQuery);
$stmt->execute($params);
$totalTemplates = $stmt->fetchColumn();
$totalPages = ceil($totalTemplates / $limit);

// Get templates
$query = "SELECT st.*, 
          (SELECT COUNT(*) FROM sms_campaigns sc WHERE sc.template_id = st.id) as usage_count
          FROM sms_templates st 
          $whereClause 
          ORDER BY st.created_at DESC 
          LIMIT $limit OFFSET $offset";

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get categories for filter
$stmt = $pdo->prepare("SELECT DISTINCT category FROM sms_templates WHERE category IS NOT NULL AND category != '' ORDER BY category");
$stmt->execute();
$categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Set page variables
$page_title = 'SMS Templates';
$page_header = 'SMS Templates';
$page_description = 'Manage SMS message templates for quick sending.';

// Include header
include 'includes/header.php';
?>

<style>
.template-preview {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 0.75rem;
    border-radius: 0.375rem;
    font-family: monospace;
    white-space: pre-wrap;
    max-height: 100px;
    overflow-y: auto;
}
.character-count {
    font-size: 0.875rem;
    color: #6c757d;
}
.usage-badge {
    font-size: 0.75rem;
}
</style>

<!-- Page Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="bi bi-file-text me-2"></i><?php echo $page_header; ?>
    </h1>
    <div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
            <i class="bi bi-plus-circle me-2"></i>Create Template
        </button>
        <a href="bulk_sms.php" class="btn btn-outline-primary">
            <i class="bi bi-chat-text-fill me-2"></i>Send SMS
        </a>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
<?php endif; ?>

<?php if (isset($success)): ?>
    <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
<?php endif; ?>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Templates</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="Search by name or content...">
            </div>
            <div class="col-md-3">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category" name="category">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo htmlspecialchars($category); ?>" 
                                <?php echo $categoryFilter === $category ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="active" <?php echo $statusFilter === 'active' ? 'selected' : ''; ?>>Active</option>
                    <option value="inactive" <?php echo $statusFilter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search me-1"></i>Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Templates List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            SMS Templates 
            <span class="badge bg-secondary"><?php echo number_format($totalTemplates); ?> total</span>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($templates)): ?>
            <div class="text-center py-4">
                <i class="bi bi-file-text text-muted" style="font-size: 3rem;"></i>
                <h5 class="text-muted mt-2">No SMS Templates Found</h5>
                <p class="text-muted">Create your first SMS template to get started.</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                    <i class="bi bi-plus-circle me-2"></i>Create Template
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Template Name</th>
                            <th>Category</th>
                            <th>Message Preview</th>
                            <th>Length</th>
                            <th>Usage</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($templates as $template): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($template['template_name']); ?></strong>
                                </td>
                                <td>
                                    <?php if ($template['category']): ?>
                                        <span class="badge bg-light text-dark"><?php echo htmlspecialchars($template['category']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="template-preview">
                                        <?php echo htmlspecialchars(substr($template['message_content'], 0, 100)); ?>
                                        <?php if (strlen($template['message_content']) > 100): ?>...<?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="character-count">
                                        <?php 
                                        $length = strlen($template['message_content']);
                                        echo $length . ' chars';
                                        if ($length > 160) {
                                            $smsCount = ceil($length / 160);
                                            echo "<br><small class='text-warning'>($smsCount SMS)</small>";
                                        }
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-info usage-badge">
                                        <?php echo number_format($template['usage_count']); ?> campaigns
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $template['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                        <?php echo ucfirst($template['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo date('M j, Y', strtotime($template['created_at'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="editTemplate(<?php echo htmlspecialchars(json_encode($template)); ?>)">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteTemplate(<?php echo $template['id']; ?>, '<?php echo htmlspecialchars($template['template_name']); ?>')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Templates pagination" class="mt-3">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($categoryFilter); ?>&status=<?php echo urlencode($statusFilter); ?>">Previous</a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($categoryFilter); ?>&status=<?php echo urlencode($statusFilter); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($categoryFilter); ?>&status=<?php echo urlencode($statusFilter); ?>">Next</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Create Template Modal -->
<div class="modal fade" id="createTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create SMS Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="template_name" class="form-label">Template Name *</label>
                        <input type="text" class="form-control" id="template_name" name="template_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <input type="text" class="form-control" id="category" name="category" 
                               placeholder="e.g., Birthday, Event, Announcement">
                    </div>
                    
                    <div class="mb-3">
                        <label for="message_content" class="form-label">Message Content *</label>
                        <textarea class="form-control" id="message_content" name="message_content" rows="4" required
                                  placeholder="Enter your SMS message template..." onkeyup="updateCreateCharacterCount()"></textarea>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted">Use merge fields like {full_name}, {first_name}, etc.</small>
                            <span id="createCharacterCount" class="character-count">0 characters</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="create_template" class="btn btn-primary">Create Template</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Template Modal -->
<div class="modal fade" id="editTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit SMS Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <input type="hidden" id="edit_template_id" name="template_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_template_name" class="form-label">Template Name *</label>
                        <input type="text" class="form-control" id="edit_template_name" name="template_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_category" class="form-label">Category</label>
                        <input type="text" class="form-control" id="edit_category" name="category">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_message_content" class="form-label">Message Content *</label>
                        <textarea class="form-control" id="edit_message_content" name="message_content" rows="4" required
                                  onkeyup="updateEditCharacterCount()"></textarea>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted">Use merge fields like {full_name}, {first_name}, etc.</small>
                            <span id="editCharacterCount" class="character-count">0 characters</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status</label>
                        <select class="form-select" id="edit_status" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_template" class="btn btn-primary">Update Template</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteTemplateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete SMS Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the template "<strong id="deleteTemplateName"></strong>"?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <form method="post" style="display: inline;">
                    <input type="hidden" id="delete_template_id" name="template_id">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="delete_template" class="btn btn-danger">Delete Template</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function updateCreateCharacterCount() {
    const message = document.getElementById('message_content').value;
    const count = message.length;
    const counter = document.getElementById('createCharacterCount');
    
    counter.textContent = count + ' characters';
    
    if (count > 160) {
        const smsCount = Math.ceil(count / 160);
        counter.textContent = count + ' characters (' + smsCount + ' SMS)';
        counter.className = 'character-count text-warning';
    } else {
        counter.className = 'character-count';
    }
}

function updateEditCharacterCount() {
    const message = document.getElementById('edit_message_content').value;
    const count = message.length;
    const counter = document.getElementById('editCharacterCount');
    
    counter.textContent = count + ' characters';
    
    if (count > 160) {
        const smsCount = Math.ceil(count / 160);
        counter.textContent = count + ' characters (' + smsCount + ' SMS)';
        counter.className = 'character-count text-warning';
    } else {
        counter.className = 'character-count';
    }
}

function editTemplate(template) {
    document.getElementById('edit_template_id').value = template.id;
    document.getElementById('edit_template_name').value = template.template_name;
    document.getElementById('edit_category').value = template.category || '';
    document.getElementById('edit_message_content').value = template.message_content;
    document.getElementById('edit_status').value = template.status;
    
    updateEditCharacterCount();
    
    new bootstrap.Modal(document.getElementById('editTemplateModal')).show();
}

function deleteTemplate(templateId, templateName) {
    document.getElementById('delete_template_id').value = templateId;
    document.getElementById('deleteTemplateName').textContent = templateName;
    
    new bootstrap.Modal(document.getElementById('deleteTemplateModal')).show();
}
</script>

<?php include 'includes/footer.php'; ?>
