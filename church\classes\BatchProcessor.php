<?php

class BatchProcessor {
    private $batchSize = 50; // Default batch size
    private $sleepBetweenBatches = 2; // Sleep 2 seconds between batches
    private $maxRetries = 3;
    private $logFile;
    
    public function __construct($batchSize = null, $logFile = null) {
        if ($batchSize !== null) {
            $this->batchSize = $batchSize;
        }
        $this->logFile = $logFile ?? dirname(__DIR__) . '/logs/batch_process.log';
        $this->ensureLogDirectory();
    }
    
    private function ensureLogDirectory() {
        $logDir = dirname($this->logFile);
        if (!file_exists($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    public function processBatch(array $items, callable $processor) {
        $totalItems = count($items);
        $batches = array_chunk($items, $this->batchSize);
        $results = [
            'success' => 0,
            'failed' => 0,
            'retried' => 0
        ];
        
        foreach ($batches as $batchIndex => $batch) {
            $this->log(sprintf(
                "Processing batch %d/%d (%d items)",
                $batchIndex + 1,
                ceil($totalItems / $this->batchSize),
                count($batch)
            ));
            
            foreach ($batch as $item) {
                $retryCount = 0;
                $success = false;
                
                while (!$success && $retryCount < $this->maxRetries) {
                    try {
                        $processor($item);
                        $success = true;
                        $results['success']++;
                    } catch (Exception $e) {
                        $retryCount++;
                        $results['retried']++;
                        
                        if ($retryCount >= $this->maxRetries) {
                            $results['failed']++;
                            $this->log(sprintf(
                                "Failed to process item after %d retries: %s",
                                $retryCount,
                                $e->getMessage()
                            ));
                        } else {
                            sleep(1); // Wait before retry
                        }
                    }
                }
            }
            
            if ($batchIndex < count($batches) - 1) {
                sleep($this->sleepBetweenBatches);
            }
        }
        
        $this->log(sprintf(
            "Batch processing completed. Success: %d, Failed: %d, Retried: %d",
            $results['success'],
            $results['failed'],
            $results['retried']
        ));
        
        return $results;
    }
    
    public function setBatchSize($size) {
        $this->batchSize = max(1, (int)$size);
    }
    
    public function setSleepBetweenBatches($seconds) {
        $this->sleepBetweenBatches = max(0, (int)$seconds);
    }
    
    public function setMaxRetries($retries) {
        $this->maxRetries = max(1, (int)$retries);
    }
    
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message" . PHP_EOL;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }
    
    // Specific method for processing email notifications
    public function processEmailNotifications(array $recipients, $emailData, $mailer) {
        return $this->processBatch($recipients, function($recipient) use ($emailData, $mailer) {
            // Prepare email for this recipient
            $to = $recipient['email'];
            $subject = $emailData['subject'];
            $body = str_replace(
                ['{member_name}', '{member_full_name}'],
                [$recipient['first_name'], $recipient['first_name'] . ' ' . $recipient['last_name']],
                $emailData['body']
            );
            
            // Send email using the provided mailer
            $mailer->sendEmail($to, $subject, $body);
        });
    }
} 