Enum
-----
<?php

enum A {
    case class;
}
enum B implements Bar, Baz {
}
enum C: int implements Bar {
    case Foo = 1;
}
-----
array(
    0: Stmt_Enum(
        attrGroups: array(
        )
        name: Identifier(
            name: A
        )
        scalarType: null
        implements: array(
        )
        stmts: array(
            0: Stmt_EnumCase(
                attrGroups: array(
                )
                name: Identifier(
                    name: class
                )
                expr: null
            )
        )
    )
    1: Stmt_Enum(
        attrGroups: array(
        )
        name: Identifier(
            name: B
        )
        scalarType: null
        implements: array(
            0: Name(
                name: Bar
            )
            1: Name(
                name: Baz
            )
        )
        stmts: array(
        )
    )
    2: Stmt_Enum(
        attrGroups: array(
        )
        name: Identifier(
            name: C
        )
        scalarType: Identifier(
            name: int
        )
        implements: array(
            0: Name(
                name: Bar
            )
        )
        stmts: array(
            0: Stmt_EnumCase(
                attrGroups: array(
                )
                name: Identifier(
                    name: Foo
                )
                expr: <PERSON><PERSON>r_Int(
                    value: 1
                )
            )
        )
    )
)
