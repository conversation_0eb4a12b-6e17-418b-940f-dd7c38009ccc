Invalid class name
-----
<?php class self {}
-----
Cannot use 'self' as class name as it is reserved from 1:13 to 1:16
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: self
        )
        extends: null
        implements: array(
        )
        stmts: array(
        )
    )
)
-----
<?php class PARENT {}
-----
Cannot use 'PARENT' as class name as it is reserved from 1:13 to 1:18
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: PARENT
        )
        extends: null
        implements: array(
        )
        stmts: array(
        )
    )
)
-----
<?php class static {}
-----
Syntax error, unexpected T_STATIC, expecting T_STRING from 1:13 to 1:18
array(
    0: Stmt_Block(
        stmts: array(
        )
    )
)
-----
<?php class A extends self {}
-----
Cannot use 'self' as class name as it is reserved from 1:23 to 1:26
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: Name(
            name: self
        )
        implements: array(
        )
        stmts: array(
        )
    )
)
-----
<?php class A extends PARENT {}
-----
Cannot use 'PARENT' as class name as it is reserved from 1:23 to 1:28
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: Name(
            name: PARENT
        )
        implements: array(
        )
        stmts: array(
        )
    )
)
-----
<?php class A extends static {}
-----
Cannot use 'static' as class name as it is reserved from 1:23 to 1:28
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: Name(
            name: static
        )
        implements: array(
        )
        stmts: array(
        )
    )
)
-----
<?php class A implements self {}
-----
Cannot use 'self' as interface name as it is reserved from 1:26 to 1:29
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: null
        implements: array(
            0: Name(
                name: self
            )
        )
        stmts: array(
        )
    )
)
-----
<?php class A implements PARENT {}
-----
Cannot use 'PARENT' as interface name as it is reserved from 1:26 to 1:31
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: null
        implements: array(
            0: Name(
                name: PARENT
            )
        )
        stmts: array(
        )
    )
)
-----
<?php class A implements static {}
-----
Cannot use 'static' as interface name as it is reserved from 1:26 to 1:31
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: null
        implements: array(
            0: Name(
                name: static
            )
        )
        stmts: array(
        )
    )
)
-----
<?php interface self {}
-----
Cannot use 'self' as class name as it is reserved from 1:17 to 1:20
array(
    0: Stmt_Interface(
        attrGroups: array(
        )
        name: Identifier(
            name: self
        )
        extends: array(
        )
        stmts: array(
        )
    )
)
-----
<?php interface PARENT {}
-----
Cannot use 'PARENT' as class name as it is reserved from 1:17 to 1:22
array(
    0: Stmt_Interface(
        attrGroups: array(
        )
        name: Identifier(
            name: PARENT
        )
        extends: array(
        )
        stmts: array(
        )
    )
)
-----
<?php interface static {}
-----
Syntax error, unexpected T_STATIC, expecting T_STRING from 1:17 to 1:22
array(
    0: Stmt_Block(
        stmts: array(
        )
    )
)
-----
<?php interface A extends self {}
-----
Cannot use 'self' as interface name as it is reserved from 1:27 to 1:30
array(
    0: Stmt_Interface(
        attrGroups: array(
        )
        name: Identifier(
            name: A
        )
        extends: array(
            0: Name(
                name: self
            )
        )
        stmts: array(
        )
    )
)
-----
<?php interface A extends PARENT {}
-----
Cannot use 'PARENT' as interface name as it is reserved from 1:27 to 1:32
array(
    0: Stmt_Interface(
        attrGroups: array(
        )
        name: Identifier(
            name: A
        )
        extends: array(
            0: Name(
                name: PARENT
            )
        )
        stmts: array(
        )
    )
)
-----
<?php interface A extends static {}
-----
Cannot use 'static' as interface name as it is reserved from 1:27 to 1:32
array(
    0: Stmt_Interface(
        attrGroups: array(
        )
        name: Identifier(
            name: A
        )
        extends: array(
            0: Name(
                name: static
            )
        )
        stmts: array(
        )
    )
)
