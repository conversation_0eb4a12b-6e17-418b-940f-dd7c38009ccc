<?php
/**
 * Branding Helper Class
 * Provides methods to apply custom branding and terminology throughout the system
 */

class BrandingHelper {
    private $pdo;
    private $settings = [];
    private $terminology = [];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->loadSettings();
    }
    
    /**
     * Load all branding and terminology settings
     */
    private function loadSettings() {
        $stmt = $this->pdo->prepare("SELECT setting_name, setting_value FROM appearance_settings");
        $stmt->execute();
        
        while ($row = $stmt->fetch()) {
            $this->settings[$row['setting_name']] = $row['setting_value'];
            
            // Separate terminology settings
            if (strpos($row['setting_name'], 'term_') === 0) {
                $this->terminology[$row['setting_name']] = $row['setting_value'];
            }
        }
    }
    
    /**
     * Get organization name
     */
    public function getOrganizationName() {
        return $this->settings['organization_name'] ?? 'Organization Management System';
    }
    
    /**
     * Get organization tagline
     */
    public function getTagline() {
        return $this->settings['tagline'] ?? 'Connecting People, Building Community';
    }
    
    /**
     * Get custom terminology
     */
    public function getTerm($key, $default = null) {
        $termKey = 'term_' . $key;
        return $this->terminology[$termKey] ?? $default ?? ucfirst($key);
    }
    
    /**
     * Replace terminology in text
     */
    public function applyTerminology($text) {
        $replacements = [
            'Members' => $this->getTerm('members', 'Members'),
            'Member' => $this->getTerm('member', 'Member'),
            'Events' => $this->getTerm('events', 'Events'),
            'Event' => $this->getTerm('event', 'Event'),
            'Campaigns' => $this->getTerm('campaigns', 'Campaigns'),
            'Campaign' => $this->getTerm('campaign', 'Campaign'),
            'Groups' => $this->getTerm('groups', 'Groups'),
            'Group' => $this->getTerm('group', 'Group'),
            'Leaders' => $this->getTerm('leaders', 'Leaders'),
            'Leader' => $this->getTerm('leader', 'Leader'),
            'Volunteers' => $this->getTerm('volunteers', 'Volunteers'),
            'Volunteer' => $this->getTerm('volunteer', 'Volunteer'),
            'Donations' => $this->getTerm('donations', 'Donations'),
            'Donation' => $this->getTerm('donation', 'Donation'),
            'Attendance' => $this->getTerm('attendance', 'Attendance'),
            'Ministries' => $this->getTerm('ministries', 'Ministries'),
            'Ministry' => $this->getTerm('ministry', 'Ministry')
        ];
        
        return str_replace(array_keys($replacements), array_values($replacements), $text);
    }
    
    /**
     * Get custom CSS variables
     */
    public function getCSSVariables() {
        $css = ":root {\n";
        
        if (!empty($this->settings['primary_color'])) {
            $css .= "    --bs-primary: {$this->settings['primary_color']};\n";
            $css .= "    --primary-color: {$this->settings['primary_color']};\n";
        }
        
        if (!empty($this->settings['secondary_color'])) {
            $css .= "    --bs-secondary: {$this->settings['secondary_color']};\n";
            $css .= "    --secondary-color: {$this->settings['secondary_color']};\n";
        }
        
        if (!empty($this->settings['accent_color'])) {
            $css .= "    --accent-color: {$this->settings['accent_color']};\n";
        }
        
        if (!empty($this->settings['font_family']) && $this->settings['font_family'] !== 'system') {
            $css .= "    --font-family: '{$this->settings['font_family']}', sans-serif;\n";
        }
        
        $css .= "}\n";
        
        // Add font family application
        if (!empty($this->settings['font_family']) && $this->settings['font_family'] !== 'system') {
            $css .= "body, .btn, .form-control, .form-select { font-family: var(--font-family); }\n";
        }
        
        // Add custom CSS
        if (!empty($this->settings['custom_css'])) {
            $css .= "\n/* Custom CSS */\n" . $this->settings['custom_css'] . "\n";
        }
        
        return $css;
    }
    
    /**
     * Get logo HTML
     */
    public function getLogoHTML() {
        $html = '';
        $logoUrl = $this->settings['logo_url'] ?? '';
        $logoWidth = $this->settings['logo_width'] ?? 150;
        $logoHeight = $this->settings['logo_height'] ?? 75;
        $showLogoText = $this->settings['show_logo_text'] ?? 0;
        $logoText = $this->settings['logo_text'] ?? $this->getOrganizationName();
        $logoPosition = $this->settings['logo_position'] ?? 'left';
        
        if (!empty($logoUrl) && file_exists($logoUrl)) {
            $html .= '<div class="logo-container text-' . $logoPosition . '">';
            $html .= '<img src="' . htmlspecialchars($logoUrl) . '" alt="' . htmlspecialchars($this->getOrganizationName()) . '" ';
            $html .= 'style="width: ' . (int)$logoWidth . 'px; height: ' . (int)$logoHeight . 'px; object-fit: contain;">';
            
            if ($showLogoText) {
                $html .= '<span class="logo-text ms-2">' . htmlspecialchars($logoText) . '</span>';
            }
            
            $html .= '</div>';
        } else if ($showLogoText || empty($logoUrl)) {
            $html .= '<div class="logo-text text-' . $logoPosition . '">' . htmlspecialchars($logoText) . '</div>';
        }
        
        return $html;
    }
    
    /**
     * Get favicon HTML
     */
    public function getFaviconHTML() {
        $faviconUrl = $this->settings['favicon_url'] ?? '';
        
        if (!empty($faviconUrl)) {
            return '<link rel="icon" type="image/x-icon" href="' . htmlspecialchars($faviconUrl) . '">';
        }
        
        return '';
    }
    
    /**
     * Get meta tags HTML
     */
    public function getMetaTagsHTML() {
        $html = '';
        
        if (!empty($this->settings['meta_description'])) {
            $html .= '<meta name="description" content="' . htmlspecialchars($this->settings['meta_description']) . '">' . "\n";
        }
        
        if (!empty($this->settings['meta_keywords'])) {
            $html .= '<meta name="keywords" content="' . htmlspecialchars($this->settings['meta_keywords']) . '">' . "\n";
        }
        
        return $html;
    }
    
    /**
     * Get analytics code HTML
     */
    public function getAnalyticsHTML() {
        $html = '';
        
        // Google Analytics
        if (!empty($this->settings['google_analytics_id'])) {
            $gaId = htmlspecialchars($this->settings['google_analytics_id']);
            $html .= "
            <!-- Google Analytics -->
            <script async src=\"https://www.googletagmanager.com/gtag/js?id={$gaId}\"></script>
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '{$gaId}');
            </script>
            ";
        }
        
        // Facebook Pixel
        if (!empty($this->settings['facebook_pixel_id'])) {
            $pixelId = htmlspecialchars($this->settings['facebook_pixel_id']);
            $html .= "
            <!-- Facebook Pixel -->
            <script>
                !function(f,b,e,v,n,t,s)
                {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                n.queue=[];t=b.createElement(e);t.async=!0;
                t.src=v;s=b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t,s)}(window, document,'script',
                'https://connect.facebook.net/en_US/fbevents.js');
                fbq('init', '{$pixelId}');
                fbq('track', 'PageView');
            </script>
            <noscript><img height=\"1\" width=\"1\" style=\"display:none\"
                src=\"https://www.facebook.com/tr?id={$pixelId}&ev=PageView&noscript=1\"
            /></noscript>
            ";
        }
        
        return $html;
    }
    
    /**
     * Get custom head code
     */
    public function getCustomHeadCode() {
        return $this->settings['custom_head_code'] ?? '';
    }
    
    /**
     * Get custom body code
     */
    public function getCustomBodyCode() {
        return $this->settings['custom_body_code'] ?? '';
    }
    
    /**
     * Get footer text
     */
    public function getFooterText() {
        $customFooter = $this->settings['custom_footer_text'] ?? '';
        $hidePoweredBy = $this->settings['hide_powered_by'] ?? 0;
        
        if (!empty($customFooter)) {
            return $customFooter;
        }
        
        $footer = '© ' . date('Y') . ' ' . $this->getOrganizationName() . '. All rights reserved.';
        
        if (!$hidePoweredBy) {
            $footer .= ' | Powered by Organization Management System';
        }
        
        return $footer;
    }
    
    /**
     * Get support contact information
     */
    public function getSupportInfo() {
        return [
            'email' => $this->settings['support_email'] ?? '',
            'phone' => $this->settings['support_phone'] ?? '',
            'url' => $this->settings['support_url'] ?? ''
        ];
    }
    
    /**
     * Check if SSL is enabled
     */
    public function isSSLEnabled() {
        return (bool)($this->settings['ssl_enabled'] ?? 0);
    }
    
    /**
     * Get custom domain
     */
    public function getCustomDomain() {
        return $this->settings['custom_domain'] ?? '';
    }
    
    /**
     * Apply branding to page title
     */
    public function getPageTitle($pageTitle = '') {
        $orgName = $this->getOrganizationName();
        
        if (empty($pageTitle)) {
            return $orgName;
        }
        
        return $pageTitle . ' - ' . $orgName;
    }
    
    /**
     * Get organization type
     */
    public function getOrganizationType() {
        return $this->settings['organization_type'] ?? 'church';
    }
    
    /**
     * Check if white-label mode is enabled
     */
    public function isWhiteLabelMode() {
        return (bool)($this->settings['hide_powered_by'] ?? 0);
    }
}

// Global function to get branding helper instance
function getBrandingHelper() {
    global $pdo;
    static $brandingHelper = null;
    
    if ($brandingHelper === null) {
        $brandingHelper = new BrandingHelper($pdo);
    }
    
    return $brandingHelper;
}

// Helper functions for easy access
function getOrgName() {
    return getBrandingHelper()->getOrganizationName();
}

function getTerm($key, $default = null) {
    return getBrandingHelper()->getTerm($key, $default);
}

function applyTerminology($text) {
    return getBrandingHelper()->applyTerminology($text);
}
?>
