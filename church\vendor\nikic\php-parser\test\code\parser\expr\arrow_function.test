Arrow Functions
-----
<?php
fn(bool $a) => $a;
fn($x = 42) => $x;
static fn(&$x) => $x;
fn&($x) => $x;
fn($x, ...$rest) => $rest;
fn(): int => $x;

fn($a, $b) => $a and $b;
fn($a, $b) => $a && $b;
-----
array(
    0: Stmt_Expression(
        expr: Expr_ArrowFunction(
            attrGroups: array(
            )
            static: false
            byRef: false
            params: array(
                0: Param(
                    attrGroups: array(
                    )
                    flags: 0
                    type: Identifier(
                        name: bool
                    )
                    byRef: false
                    variadic: false
                    var: Expr_Variable(
                        name: a
                    )
                    default: null
                    hooks: array(
                    )
                )
            )
            returnType: null
            expr: Expr_Variable(
                name: a
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_ArrowFunction(
            attrGroups: array(
            )
            static: false
            byRef: false
            params: array(
                0: Param(
                    attrGroups: array(
                    )
                    flags: 0
                    type: null
                    byRef: false
                    variadic: false
                    var: Expr_Variable(
                        name: x
                    )
                    default: Scalar_Int(
                        value: 42
                    )
                    hooks: array(
                    )
                )
            )
            returnType: null
            expr: Expr_Variable(
                name: x
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_ArrowFunction(
            attrGroups: array(
            )
            static: true
            byRef: false
            params: array(
                0: Param(
                    attrGroups: array(
                    )
                    flags: 0
                    type: null
                    byRef: true
                    variadic: false
                    var: Expr_Variable(
                        name: x
                    )
                    default: null
                    hooks: array(
                    )
                )
            )
            returnType: null
            expr: Expr_Variable(
                name: x
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_ArrowFunction(
            attrGroups: array(
            )
            static: false
            byRef: true
            params: array(
                0: Param(
                    attrGroups: array(
                    )
                    flags: 0
                    type: null
                    byRef: false
                    variadic: false
                    var: Expr_Variable(
                        name: x
                    )
                    default: null
                    hooks: array(
                    )
                )
            )
            returnType: null
            expr: Expr_Variable(
                name: x
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_ArrowFunction(
            attrGroups: array(
            )
            static: false
            byRef: false
            params: array(
                0: Param(
                    attrGroups: array(
                    )
                    flags: 0
                    type: null
                    byRef: false
                    variadic: false
                    var: Expr_Variable(
                        name: x
                    )
                    default: null
                    hooks: array(
                    )
                )
                1: Param(
                    attrGroups: array(
                    )
                    flags: 0
                    type: null
                    byRef: false
                    variadic: true
                    var: Expr_Variable(
                        name: rest
                    )
                    default: null
                    hooks: array(
                    )
                )
            )
            returnType: null
            expr: Expr_Variable(
                name: rest
            )
        )
    )
    5: Stmt_Expression(
        expr: Expr_ArrowFunction(
            attrGroups: array(
            )
            static: false
            byRef: false
            params: array(
            )
            returnType: Identifier(
                name: int
            )
            expr: Expr_Variable(
                name: x
            )
        )
    )
    6: Stmt_Expression(
        expr: Expr_ArrowFunction(
            attrGroups: array(
            )
            static: false
            byRef: false
            params: array(
                0: Param(
                    attrGroups: array(
                    )
                    flags: 0
                    type: null
                    byRef: false
                    variadic: false
                    var: Expr_Variable(
                        name: a
                    )
                    default: null
                    hooks: array(
                    )
                )
                1: Param(
                    attrGroups: array(
                    )
                    flags: 0
                    type: null
                    byRef: false
                    variadic: false
                    var: Expr_Variable(
                        name: b
                    )
                    default: null
                    hooks: array(
                    )
                )
            )
            returnType: null
            expr: Expr_BinaryOp_LogicalAnd(
                left: Expr_Variable(
                    name: a
                )
                right: Expr_Variable(
                    name: b
                )
            )
        )
    )
    7: Stmt_Expression(
        expr: Expr_ArrowFunction(
            attrGroups: array(
            )
            static: false
            byRef: false
            params: array(
                0: Param(
                    attrGroups: array(
                    )
                    flags: 0
                    type: null
                    byRef: false
                    variadic: false
                    var: Expr_Variable(
                        name: a
                    )
                    default: null
                    hooks: array(
                    )
                )
                1: Param(
                    attrGroups: array(
                    )
                    flags: 0
                    type: null
                    byRef: false
                    variadic: false
                    var: Expr_Variable(
                        name: b
                    )
                    default: null
                    hooks: array(
                    )
                )
            )
            returnType: null
            expr: Expr_BinaryOp_BooleanAnd(
                left: Expr_Variable(
                    name: a
                )
                right: Expr_Variable(
                    name: b
                )
            )
        )
    )
)
