<?php
/**
 * Enhanced Activity Tracking System
 * Comprehensive activity logging and notification system for user dashboard
 */

require_once 'notification_functions.php';

class EnhancedActivityTracker {
    private $pdo;
    private $userAuth;
    
    public function __construct($pdo, $userAuth = null) {
        $this->pdo = $pdo;
        $this->userAuth = $userAuth;
        $this->ensureActivityTables();
    }
    
    /**
     * Ensure all necessary activity tracking tables exist
     */
    private function ensureActivityTables() {
        try {
            // Enhanced user_activity_log table
            $this->pdo->exec("CREATE TABLE IF NOT EXISTS user_activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                member_id INT NOT NULL,
                activity_type VARCHAR(50) NOT NULL,
                activity_description TEXT NOT NULL,
                entity_type VARCHAR(50) NULL,
                entity_id INT NULL,
                related_member_id INT NULL,
                metadata JSON NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_member_id (member_id),
                INDEX idx_activity_type (activity_type),
                INDEX idx_entity_type (entity_type),
                INDEX idx_related_member (related_member_id),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
            )");
            
            // Activity notifications table for real-time notifications
            $this->pdo->exec("CREATE TABLE IF NOT EXISTS activity_notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                recipient_id INT NOT NULL,
                activity_id INT NOT NULL,
                notification_type VARCHAR(50) NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP NULL,
                INDEX idx_recipient (recipient_id),
                INDEX idx_activity (activity_id),
                INDEX idx_is_read (is_read),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (recipient_id) REFERENCES members(id) ON DELETE CASCADE,
                FOREIGN KEY (activity_id) REFERENCES user_activity_log(id) ON DELETE CASCADE
            )");
            
        } catch (PDOException $e) {
            error_log("Error creating activity tables: " . $e->getMessage());
        }
    }
    
    /**
     * Log a comprehensive activity with notifications
     */
    public function logActivity($memberId, $activityType, $description, $entityType = null, $entityId = null, $relatedMemberId = null, $metadata = null, $createNotifications = true) {
        try {
            // Insert activity log
            $stmt = $this->pdo->prepare("
                INSERT INTO user_activity_log 
                (member_id, activity_type, activity_description, entity_type, entity_id, related_member_id, metadata, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $memberId,
                $activityType,
                $description,
                $entityType,
                $entityId,
                $relatedMemberId,
                $metadata ? json_encode($metadata) : null,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            $activityId = $this->pdo->lastInsertId();
            
            // Create notifications if requested
            if ($createNotifications) {
                $this->createActivityNotifications($activityId, $memberId, $activityType, $description, $relatedMemberId, $metadata);
            }
            
            return $activityId;
            
        } catch (Exception $e) {
            error_log("Failed to log activity: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create notifications based on activity type
     */
    private function createActivityNotifications($activityId, $memberId, $activityType, $description, $relatedMemberId = null, $metadata = null) {
        try {
            $member = $this->getMemberInfo($memberId);
            $memberName = $member['full_name'] ?? 'A member';
            
            switch ($activityType) {
                case 'gift_sent':
                    if ($relatedMemberId) {
                        $this->createNotification($relatedMemberId, $activityId, 'gift_received', 
                            "🎁 Gift Received!", 
                            "$memberName sent you a gift: " . ($metadata['gift_title'] ?? 'Special Gift'));
                        
                        // Admin notification
                        $this->createAdminNotification($activityId, 'gift_activity', 
                            "Gift Activity", 
                            "$memberName sent a gift to " . $this->getMemberInfo($relatedMemberId)['full_name']);
                    }
                    break;
                    
                case 'skill_added':
                case 'skill_updated':
                    // Notify admins about skill changes
                    $skillName = $metadata['skill_name'] ?? 'a skill';
                    $this->createAdminNotification($activityId, 'skill_update', 
                        "Member Skill Update", 
                        "$memberName " . ($activityType === 'skill_added' ? 'added' : 'updated') . " skill: $skillName");
                    
                    // Notify other members who might be interested (same skill category)
                    $this->notifyInterestedMembers($activityId, $memberId, $activityType, $skillName, $metadata);
                    break;
                    
                case 'prayer_request_created':
                    // Notify admins about new prayer requests
                    $requestTitle = $metadata['request_title'] ?? 'Prayer Request';
                    $this->createAdminNotification($activityId, 'prayer_request', 
                        "New Prayer Request", 
                        "$memberName created a new prayer request: $requestTitle");
                    
                    // Notify community members if public/members privacy
                    if (isset($metadata['privacy_level']) && in_array($metadata['privacy_level'], ['public', 'members'])) {
                        $this->notifyPrayerCommunity($activityId, $memberId, $requestTitle);
                    }
                    break;
                    
                case 'prayer_response_added':
                    if ($relatedMemberId) {
                        $this->createNotification($relatedMemberId, $activityId, 'prayer_response', 
                            "🙏 Prayer Response", 
                            "$memberName responded to your prayer request");
                    }
                    break;
                    
                case 'event_rsvp':
                    // Notify admins about event RSVPs
                    $eventTitle = $metadata['event_title'] ?? 'Event';
                    $rsvpStatus = $metadata['rsvp_status'] ?? 'attending';
                    $this->createAdminNotification($activityId, 'event_rsvp', 
                        "Event RSVP Update", 
                        "$memberName RSVP'd '$rsvpStatus' for: $eventTitle");
                    break;
            }
            
        } catch (Exception $e) {
            error_log("Error creating activity notifications: " . $e->getMessage());
        }
    }
    
    /**
     * Create user notification
     */
    private function createNotification($recipientId, $activityId, $type, $title, $message) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO activity_notifications (recipient_id, activity_id, notification_type, title, message)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$recipientId, $activityId, $type, $title, $message]);
            
            // Also create in main notifications table for compatibility
            createNotification($this->pdo, $recipientId, $title, $message, $type);
            
        } catch (Exception $e) {
            error_log("Error creating user notification: " . $e->getMessage());
        }
    }
    
    /**
     * Create admin notification
     */
    private function createAdminNotification($activityId, $type, $title, $message) {
        try {
            // Get all admin users
            $stmt = $this->pdo->prepare("SELECT id FROM admins WHERE status = 'active'");
            $stmt->execute();
            $admins = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($admins as $adminId) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO admin_notifications (recipient_id, notification_type, title, message)
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([$adminId, $type, $title, $message]);
            }
            
        } catch (Exception $e) {
            error_log("Error creating admin notification: " . $e->getMessage());
        }
    }
    
    /**
     * Get member information
     */
    private function getMemberInfo($memberId) {
        try {
            $stmt = $this->pdo->prepare("SELECT id, full_name, first_name, email FROM members WHERE id = ?");
            $stmt->execute([$memberId]);
            return $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Notify members interested in similar skills
     */
    private function notifyInterestedMembers($activityId, $memberId, $activityType, $skillName, $metadata) {
        try {
            // Find members with similar skills or interests
            $stmt = $this->pdo->prepare("
                SELECT DISTINCT m.id, m.full_name 
                FROM members m
                JOIN member_skills ms ON m.id = ms.member_id
                JOIN skills_catalog sc ON ms.skill_id = sc.id
                WHERE sc.skill_category = ? AND m.id != ?
                LIMIT 10
            ");
            
            $skillCategory = $metadata['skill_category'] ?? '';
            $stmt->execute([$skillCategory, $memberId]);
            $interestedMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $memberName = $this->getMemberInfo($memberId)['full_name'];
            
            foreach ($interestedMembers as $member) {
                $this->createNotification($member['id'], $activityId, 'skill_interest', 
                    "💡 Skill Update in Your Area", 
                    "$memberName " . ($activityType === 'skill_added' ? 'added' : 'updated') . " their $skillName skill");
            }
            
        } catch (Exception $e) {
            error_log("Error notifying interested members: " . $e->getMessage());
        }
    }
    
    /**
     * Notify prayer community about new requests
     */
    private function notifyPrayerCommunity($activityId, $memberId, $requestTitle) {
        try {
            // Get active members who have participated in prayer requests before
            $stmt = $this->pdo->prepare("
                SELECT DISTINCT m.id, m.full_name 
                FROM members m
                JOIN prayer_responses pr ON m.id = pr.member_id
                WHERE m.id != ? AND m.status = 'active'
                GROUP BY m.id
                HAVING COUNT(pr.id) > 0
                LIMIT 20
            ");
            $stmt->execute([$memberId]);
            $prayerMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $memberName = $this->getMemberInfo($memberId)['full_name'];
            
            foreach ($prayerMembers as $member) {
                $this->createNotification($member['id'], $activityId, 'prayer_community', 
                    "🙏 New Prayer Request", 
                    "$memberName shared a prayer request: $requestTitle");
            }
            
        } catch (Exception $e) {
            error_log("Error notifying prayer community: " . $e->getMessage());
        }
    }
    
    /**
     * Get comprehensive recent activities for a user
     */
    public function getUserRecentActivities($memberId, $limit = 10) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    ual.*,
                    rm.full_name as related_member_name,
                    rm.first_name as related_member_first_name
                FROM user_activity_log ual
                LEFT JOIN members rm ON ual.related_member_id = rm.id
                WHERE ual.member_id = ?
                ORDER BY ual.created_at DESC
                LIMIT ?
            ");
            $stmt->execute([$memberId, $limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting user activities: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get user notifications
     */
    public function getUserNotifications($memberId, $limit = 10, $unreadOnly = false) {
        try {
            $whereClause = "WHERE recipient_id = ?";
            $params = [$memberId];
            
            if ($unreadOnly) {
                $whereClause .= " AND is_read = FALSE";
            }
            
            $stmt = $this->pdo->prepare("
                SELECT * FROM activity_notifications 
                $whereClause
                ORDER BY created_at DESC
                LIMIT ?
            ");
            $params[] = $limit;
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting user notifications: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Mark notification as read
     */
    public function markNotificationRead($notificationId, $memberId) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE activity_notifications 
                SET is_read = TRUE, read_at = NOW()
                WHERE id = ? AND recipient_id = ?
            ");
            return $stmt->execute([$notificationId, $memberId]);
            
        } catch (Exception $e) {
            error_log("Error marking notification as read: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get activity statistics for dashboard
     */
    public function getActivityStats($memberId) {
        try {
            $stats = [];
            
            // Gifts sent/received counts
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM member_gifts WHERE sender_id = ?");
            $stmt->execute([$memberId]);
            $stats['gifts_sent'] = $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM member_gifts WHERE recipient_id = ?");
            $stmt->execute([$memberId]);
            $stats['gifts_received'] = $stmt->fetchColumn();
            
            // Skills count
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM member_skills WHERE member_id = ?");
            $stmt->execute([$memberId]);
            $stats['skills_count'] = $stmt->fetchColumn();
            
            // Prayer requests count
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM prayer_requests WHERE member_id = ?");
            $stmt->execute([$memberId]);
            $stats['prayer_requests'] = $stmt->fetchColumn();
            
            // Unread notifications count
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM activity_notifications WHERE recipient_id = ? AND is_read = FALSE");
            $stmt->execute([$memberId]);
            $stats['unread_notifications'] = $stmt->fetchColumn();
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Error getting activity stats: " . $e->getMessage());
            return [];
        }
    }
}

// Global instance for easy access
$activityTracker = new EnhancedActivityTracker($pdo, $userAuth ?? null);
