<?php
/**
 * Session Manager
 * 
 * Handles session initialization, timeout checking, and other session-related functions.
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    // Set session cookie parameters before starting the session
    session_set_cookie_params([
        'lifetime' => 0, // Session cookie expires when browser is closed
        'path' => '/',
        'secure' => isset($_SERVER['HTTPS']), // Only set secure if HTTPS is used
        'httponly' => true, // Prevent JavaScript access to session cookie
        'samesite' => 'Lax' // Protects against CSRF attacks
    ]);
    
    // Now start the session
    session_start();
}

// Get session timeout from database (in minutes) and convert to seconds
// Default to 45 minutes (2700 seconds) if not configured
$timeout_seconds = 2700; // 45 minutes in seconds

// Try to get timeout from database if PDO connection is available
global $pdo;
if (isset($pdo) && $pdo instanceof PDO) {
    try {
        $stmt = $pdo->prepare("SELECT session_timeout_minutes FROM security_settings LIMIT 1");
        $stmt->execute();
        $timeout_minutes = $stmt->fetchColumn();
        
        if ($timeout_minutes) {
            // Convert minutes to seconds
            $timeout_seconds = intval($timeout_minutes) * 60;
            error_log("Using database configured session timeout: $timeout_minutes minutes ($timeout_seconds seconds)");
        } else {
            error_log("No session timeout found in database, using default: 45 minutes (2700 seconds)");
        }
    } catch (PDOException $e) {
        // If error, use default value
        error_log("Could not fetch session timeout from database: " . $e->getMessage());
    }
} else {
    error_log("PDO database connection not available, using default session timeout: 45 minutes (2700 seconds)");
}

// Constants for session settings
define('SESSION_TIMEOUT', $timeout_seconds); // Session timeout in seconds

/**
 * Initialize the session with necessary timeout settings
 */
function initSession() {
    // Set session timeout if not set already
    if (!isset($_SESSION['CREATED'])) {
        $_SESSION['CREATED'] = time();
    }
    
    // Initialize last activity time
    updateLastActivity();
}

/**
 * Update the last activity timestamp
 */
function updateLastActivity() {
    $_SESSION['LAST_ACTIVITY'] = time();
}

/**
 * Check if the session has timed out
 * 
 * @return bool True if session has timed out
 */
function isSessionTimedOut() {
    // Check if last activity time is set
    if (!isset($_SESSION['LAST_ACTIVITY'])) {
        return false;
    }
    
    // Calculate time since last activity
    $inactiveTime = time() - $_SESSION['LAST_ACTIVITY'];
    
    // Return true if inactive time exceeds timeout
    return $inactiveTime >= SESSION_TIMEOUT;
}

/**
 * Handle session timeout - redirect to logout page if timed out
 * 
 * @return void
 */
function handleSessionTimeout() {
    if (isSessionTimedOut()) {
        // Log the timeout
        if (isset($_SESSION['admin_id'])) {
            error_log("Session timeout for admin ID: " . $_SESSION['admin_id'] . 
                     " after " . SESSION_TIMEOUT . " seconds of inactivity.");
        }
        
        // Force logout
        session_unset();
        session_destroy();
        
        // Redirect to login page with timeout message
        header("Location: " . ADMIN_URL . "/login.php?timeout=1");
        exit();
    }
    
    // Update last activity
    updateLastActivity();
}

/**
 * Get the remaining session time in seconds
 * 
 * @return int Remaining session time in seconds
 */
function getRemainingSessionTime() {
    if (!isset($_SESSION['LAST_ACTIVITY'])) {
        return SESSION_TIMEOUT;
    }
    
    $elapsedTime = time() - $_SESSION['LAST_ACTIVITY'];
    $remaining = SESSION_TIMEOUT - $elapsedTime;
    
    return max(0, $remaining);
}

/**
 * Output session timeout configuration as JavaScript
 * 
 * @return string JavaScript code with session timeout configuration
 */
function getSessionTimeoutConfig() {
    $remainingTime = getRemainingSessionTime() * 1000; // Convert to milliseconds
    
    return "
    <script>
        // Server-side session configuration
        window.sessionConfig = {
            timeoutDuration: " . (SESSION_TIMEOUT * 1000) . ",
            remainingTime: " . $remainingTime . ",
            logoutUrl: '" . ADMIN_URL . "/logout.php?timeout=1'
        };
    </script>";
}

// Initialize session when this file is included
initSession();

// Check for timeout on each page load (if the admin is logged in)
if (isset($_SESSION['admin_id'])) {
    handleSessionTimeout();
} 