<?php
/**
 * Bulk Delete Contacts AJAX Handler
 * 
 * This script handles bulk deletion of contacts with comprehensive safety measures:
 * - Transaction-based deletion for data integrity
 * - Foreign key constraint handling
 * - Detailed logging and error reporting
 * - Confirmation token validation for security
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Include configuration
require_once '../../config.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Validate input data
if (!$data || !isset($data['contact_ids']) || !is_array($data['contact_ids'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid input data']);
    exit();
}

// Validate confirmation token for security
if (!isset($data['confirmation_token']) || $data['confirmation_token'] !== 'BULK_DELETE_CONFIRMED') {
    echo json_encode(['success' => false, 'message' => 'Confirmation token required']);
    exit();
}

// Sanitize and validate contact IDs
$contact_ids = array_filter(array_map('intval', $data['contact_ids']));

if (empty($contact_ids)) {
    echo json_encode(['success' => false, 'message' => 'No valid contact IDs provided']);
    exit();
}

// Limit bulk operations to prevent system overload
if (count($contact_ids) > 1000) {
    echo json_encode(['success' => false, 'message' => 'Bulk delete limited to 1000 contacts at once']);
    exit();
}

try {
    $pdo->beginTransaction();
    
    // First, get contact details for logging
    $placeholders = str_repeat('?,', count($contact_ids) - 1) . '?';
    $stmt = $pdo->prepare("SELECT id, name, email FROM contacts WHERE id IN ($placeholders)");
    $stmt->execute($contact_ids);
    $contacts_to_delete = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($contacts_to_delete)) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'No contacts found with provided IDs']);
        exit();
    }
    
    $deletion_results = [
        'total_requested' => count($contact_ids),
        'contacts_found' => count($contacts_to_delete),
        'successfully_deleted' => 0,
        'failed_deletions' => [],
        'deleted_contacts' => []
    ];
    
    // Delete related records first (to handle foreign key constraints)
    foreach ($contacts_to_delete as $contact) {
        $contact_id = $contact['id'];
        
        try {
            // Delete from contact_email_logs (if exists)
            try {
                $stmt = $pdo->prepare("DELETE FROM contact_email_logs WHERE recipient_id = ?");
                $stmt->execute([$contact_id]);
            } catch (PDOException $e) {
                // Table might not exist, continue
            }

            // Delete group memberships
            $stmt = $pdo->prepare("DELETE FROM contact_group_members WHERE contact_id = ?");
            $stmt->execute([$contact_id]);
            
            // Delete the contact itself
            $stmt = $pdo->prepare("DELETE FROM contacts WHERE id = ?");
            $result = $stmt->execute([$contact_id]);
            
            if ($result && $stmt->rowCount() > 0) {
                $deletion_results['successfully_deleted']++;
                $deletion_results['deleted_contacts'][] = [
                    'id' => $contact_id,
                    'name' => $contact['name'],
                    'email' => $contact['email']
                ];
            } else {
                $deletion_results['failed_deletions'][] = [
                    'id' => $contact_id,
                    'name' => $contact['name'],
                    'email' => $contact['email'],
                    'reason' => 'Contact not found or already deleted'
                ];
            }
            
        } catch (PDOException $e) {
            $deletion_results['failed_deletions'][] = [
                'id' => $contact_id,
                'name' => $contact['name'],
                'email' => $contact['email'],
                'reason' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    // Log the bulk deletion activity
    $log_message = sprintf(
        "Bulk contact deletion: %d/%d contacts successfully deleted by admin ID %d",
        $deletion_results['successfully_deleted'],
        $deletion_results['total_requested'],
        $_SESSION['admin_id']
    );
    
    // Insert activity log
    try {
        $stmt = $pdo->prepare("INSERT INTO admin_activity_logs (admin_id, action, details, ip_address, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([
            $_SESSION['admin_id'],
            'bulk_delete_contacts',
            json_encode($deletion_results),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
    } catch (PDOException $e) {
        // Log insertion failed, but don't fail the main operation
        error_log("Failed to log bulk deletion activity: " . $e->getMessage());
    }
    
    $pdo->commit();
    
    // Prepare response
    $response = [
        'success' => true,
        'message' => sprintf(
            'Bulk deletion completed: %d out of %d contacts successfully deleted',
            $deletion_results['successfully_deleted'],
            $deletion_results['total_requested']
        ),
        'results' => $deletion_results
    ];
    
    // Add warnings if there were failures
    if (!empty($deletion_results['failed_deletions'])) {
        $response['warnings'] = sprintf(
            '%d contacts could not be deleted',
            count($deletion_results['failed_deletions'])
        );
    }
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Bulk contact deletion error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Database error occurred during bulk deletion',
        'error_details' => $e->getMessage()
    ]);
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Bulk contact deletion error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'An unexpected error occurred',
        'error_details' => $e->getMessage()
    ]);
}
?>
