Group use declarations
-----
<?php
use A\{B};
use A\{B\C, D};
use \A\B\{C\D, E};
use function A\{b\c, d};
use const \A\{B\C, D};
use A\B\{C\D, function b\c, const D};
-----
array(
    0: Stmt_GroupUse(
        type: TYPE_UNKNOWN (0)
        prefix: Name(
            name: A
        )
        uses: array(
            0: UseItem(
                type: TYPE_NORMAL (1)
                name: Name(
                    name: B
                )
                alias: null
            )
        )
    )
    1: Stmt_GroupUse(
        type: TYPE_UNKNOWN (0)
        prefix: Name(
            name: A
        )
        uses: array(
            0: UseItem(
                type: TYPE_NORMAL (1)
                name: Name(
                    name: B\C
                )
                alias: null
            )
            1: UseItem(
                type: TYPE_NORMAL (1)
                name: Name(
                    name: D
                )
                alias: null
            )
        )
    )
    2: Stmt_GroupUse(
        type: TYPE_UNKNOWN (0)
        prefix: Name(
            name: A\B
        )
        uses: array(
            0: UseItem(
                type: TYPE_NORMAL (1)
                name: Name(
                    name: C\D
                )
                alias: null
            )
            1: UseItem(
                type: TYPE_NORMAL (1)
                name: Name(
                    name: E
                )
                alias: null
            )
        )
    )
    3: Stmt_GroupUse(
        type: TYPE_FUNCTION (2)
        prefix: Name(
            name: A
        )
        uses: array(
            0: UseItem(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    name: b\c
                )
                alias: null
            )
            1: UseItem(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    name: d
                )
                alias: null
            )
        )
    )
    4: Stmt_GroupUse(
        type: TYPE_CONSTANT (3)
        prefix: Name(
            name: A
        )
        uses: array(
            0: UseItem(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    name: B\C
                )
                alias: null
            )
            1: UseItem(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    name: D
                )
                alias: null
            )
        )
    )
    5: Stmt_GroupUse(
        type: TYPE_UNKNOWN (0)
        prefix: Name(
            name: A\B
        )
        uses: array(
            0: UseItem(
                type: TYPE_NORMAL (1)
                name: Name(
                    name: C\D
                )
                alias: null
            )
            1: UseItem(
                type: TYPE_FUNCTION (2)
                name: Name(
                    name: b\c
                )
                alias: null
            )
            2: UseItem(
                type: TYPE_CONSTANT (3)
                name: Name(
                    name: D
                )
                alias: null
            )
        )
    )
)
