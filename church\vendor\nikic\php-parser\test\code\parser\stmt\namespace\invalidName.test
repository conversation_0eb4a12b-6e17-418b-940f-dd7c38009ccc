Invalid namespace names
-----
<?php use A as self;
-----
Cannot use A as self because 'self' is a special class name from 1:16 to 1:19
array(
    0: Stmt_Use(
        type: TYPE_NORMAL (1)
        uses: array(
            0: UseItem(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    name: A
                )
                alias: Identifier(
                    name: self
                )
            )
        )
    )
)
-----
<?php use B as PARENT;
-----
Cannot use B as PARENT because 'PARENT' is a special class name from 1:16 to 1:21
array(
    0: Stmt_Use(
        type: TYPE_NORMAL (1)
        uses: array(
            0: UseItem(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    name: B
                )
                alias: Identifier(
                    name: PARENT
                )
            )
        )
    )
)
-----
<?php use C as static;
-----
Syntax error, unexpected T_STATIC, expecting T_STRING from 1:16 to 1:21
array(
)
