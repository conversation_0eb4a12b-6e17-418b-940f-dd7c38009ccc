<?php
/**
 * Cron Jobs Management Page
 * 
 * This page provides information about setting up cron jobs for the email scheduling system
 * and other automated tasks in the church database system.
 */

// Initialize the session
session_start();

// Include config file
require_once "../config.php";

// Check if the user is logged in, if not then redirect to login page
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["user_type"] !== "admin") {
    header("location: login.php");
    exit;
}

// Get admin information
$admin_id = $_SESSION["admin_id"];
$admin_name = $_SESSION["admin_name"];

// Page title
$page_title = "Cron Jobs Management";

// Include header
include_once "admin_header.php";
?>

<div class="container-fluid px-4">
    <h1 class="mt-4"><?php echo $page_title; ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active"><?php echo $page_title; ?></li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-clock me-1"></i>
            Email Scheduler Cron Job
        </div>
        <div class="card-body">
            <h5>About Email Scheduler Cron Job</h5>
            <p>The email scheduler system requires a cron job to be set up to automatically process scheduled emails. This cron job should run at regular intervals (e.g., every 5 minutes) to check for and send emails that are due to be sent.</p>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> What is a Cron Job?</h5>
                <p>A cron job is a scheduled task that runs automatically at specified intervals. It's used to automate repetitive tasks, such as sending scheduled emails, generating reports, or performing system maintenance.</p>
            </div>
            
            <h5 class="mt-4">Cron Job Command</h5>
            <p>Use the following command to set up the email scheduler cron job:</p>
            
            <div class="card bg-light mb-3">
                <div class="card-body">
                    <code>wget -q -O /dev/null "<?php echo getBaseUrl(); ?>/cron/process_scheduled_emails.php?cron_key=fac_2024_secure_cron_8x9q2p5m"</code>
                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('cron-command')">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                </div>
                <div class="card-footer bg-transparent">
                    <small class="text-muted">This command should be set to run every 5 minutes for optimal email delivery.</small>
                </div>
            </div>
            
            <h5 class="mt-4">Setting Up the Cron Job</h5>
            <div class="accordion" id="cronSetupAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="cpanelHeading">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#cpanelInstructions" aria-expanded="true" aria-controls="cpanelInstructions">
                            Setting up in cPanel
                        </button>
                    </h2>
                    <div id="cpanelInstructions" class="accordion-collapse collapse show" aria-labelledby="cpanelHeading" data-bs-parent="#cronSetupAccordion">
                        <div class="accordion-body">
                            <ol>
                                <li>Log in to your cPanel account.</li>
                                <li>Scroll down to the "Advanced" section and click on "Cron Jobs".</li>
                                <li>Under "Add New Cron Job", select "Every 5 minutes" from the Common Settings dropdown.</li>
                                <li>In the "Command" field, paste the cron job command shown above.</li>
                                <li>Click "Add New Cron Job" to save.</li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="linuxHeading">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#linuxInstructions" aria-expanded="false" aria-controls="linuxInstructions">
                            Setting up in Linux/Unix
                        </button>
                    </h2>
                    <div id="linuxInstructions" class="accordion-collapse collapse" aria-labelledby="linuxHeading" data-bs-parent="#cronSetupAccordion">
                        <div class="accordion-body">
                            <ol>
                                <li>Open a terminal and type <code>crontab -e</code> to edit your cron jobs.</li>
                                <li>Add the following line to run the job every 5 minutes:</li>
                                <li><code>*/5 * * * * wget -q -O /dev/null "<?php echo getBaseUrl(); ?>/cron/process_scheduled_emails.php?cron_key=fac_2024_secure_cron_8x9q2p5m"</code></li>
                                <li>Save and exit the editor (usually by pressing Ctrl+X, then Y, then Enter in nano).</li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="windowsHeading">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#windowsInstructions" aria-expanded="false" aria-controls="windowsInstructions">
                            Setting up in Windows
                        </button>
                    </h2>
                    <div id="windowsInstructions" class="accordion-collapse collapse" aria-labelledby="windowsHeading" data-bs-parent="#cronSetupAccordion">
                        <div class="accordion-body">
                            <ol>
                                <li>Open Task Scheduler (search for it in the Start menu).</li>
                                <li>Click "Create Basic Task" in the right panel.</li>
                                <li>Enter a name (e.g., "Email Scheduler") and description, then click Next.</li>
                                <li>Select "Daily" and click Next.</li>
                                <li>Set the start time and click Next.</li>
                                <li>Select "Start a program" and click Next.</li>
                                <li>In the "Program/script" field, enter: <code>powershell</code></li>
                                <li>In the "Add arguments" field, enter: <code>-Command "Invoke-WebRequest -Uri '<?php echo getBaseUrl(); ?>/cron/process_scheduled_emails.php?cron_key=fac_2024_secure_cron_8x9q2p5m' -UseBasicParsing"</code></li>
                                <li>Click Next, then Finish.</li>
                                <li>Right-click on the newly created task and select "Properties".</li>
                                <li>Go to the "Triggers" tab, select the trigger, and click "Edit".</li>
                                <li>Check "Repeat task every" and set it to 5 minutes.</li>
                                <li>Set "for a duration of" to "Indefinitely".</li>
                                <li>Click OK to save all changes.</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            
            <h5 class="mt-4">Testing the Cron Job</h5>
            <p>You can test if the cron job is working by clicking the button below. This will manually trigger the email processing script:</p>
            
            <form method="post" action="test_cron.php" class="mb-3">
                <input type="hidden" name="cron_script" value="process_scheduled_emails">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-play-circle"></i> Test Email Processor
                </button>
            </form>
            
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> Important Notes</h5>
                <ul>
                    <li>Keep the cron key secure and do not share it publicly.</li>
                    <li>The cron job must be set up on a server that is always running.</li>
                    <li>If your hosting provider has restrictions on cron jobs, contact them for assistance.</li>
                    <li>Monitor the logs regularly to ensure the cron job is running correctly.</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-birthday-cake me-1"></i>
            Birthday Reminders Cron Job
        </div>
        <div class="card-body">
            <h5>About Birthday Reminders Cron Job</h5>
            <p>The birthday reminders system requires a cron job to automatically send birthday reminders to members. This cron job should run once daily to check for upcoming birthdays and send reminder emails.</p>
            
            <h5 class="mt-4">Cron Job Command</h5>
            <p>Use the following command to set up the birthday reminders cron job:</p>
            
            <div class="card bg-light mb-3">
                <div class="card-body">
                    <code>wget -q -O /dev/null "<?php echo getBaseUrl(); ?>/cron/process_birthday_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m"</code>
                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('birthday-command')">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                </div>
                <div class="card-footer bg-transparent">
                    <small class="text-muted">This command should be set to run once daily, preferably in the early morning.</small>
                </div>
            </div>
            
            <h5 class="mt-4">Testing the Cron Job</h5>
            <p>You can test if the birthday reminders cron job is working by clicking the button below:</p>
            
            <form method="post" action="test_cron.php" class="mb-3">
                <input type="hidden" name="cron_script" value="process_birthday_reminders">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-play-circle"></i> Test Birthday Reminders
                </button>
            </form>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-file-alt me-1"></i>
            Viewing Cron Job Logs
        </div>
        <div class="card-body">
            <h5>Email Scheduler Logs</h5>
            <p>You can view the email scheduler logs to monitor the cron job's activity and troubleshoot any issues:</p>
            
            <a href="view_logs.php?log_type=scheduled_emails" class="btn btn-info mb-3">
                <i class="fas fa-file-alt"></i> View Email Scheduler Logs
            </a>
            
            <h5 class="mt-4">Birthday Reminders Logs</h5>
            <p>You can view the birthday reminders logs to monitor the cron job's activity:</p>
            
            <a href="view_logs.php?log_type=birthday_reminders" class="btn btn-info">
                <i class="fas fa-file-alt"></i> View Birthday Reminders Logs
            </a>
        </div>
    </div>
</div>

<script>
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(function() {
        alert('Command copied to clipboard!');
    }, function(err) {
        console.error('Could not copy text: ', err);
    });
}
</script>

<?php
// Helper function to get the base URL
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    return "$protocol://$host";
}

// Include footer
include_once "admin_footer.php";
?>
