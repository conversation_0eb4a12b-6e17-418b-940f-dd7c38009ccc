Property promotion
-----
<?php

class Point
{
    public function __construct(
        public float $x = 0.0,
        protected array $y = [],
        private string $z = 'hello',
        public readonly int $a = 0,
    ) {
    }
}
-----
class Point
{
    public function __construct(public float $x = 0.0, protected array $y = [], private string $z = 'hello', public readonly int $a = 0)
    {
    }
}
-----
<?php
class Test
{
    public $z;
    public function __construct(
        public int $x,
        /** @SomeAnnotation() */
        public string $y = "123",
        string $z = "abc"
    )
    {
    }
}
-----
class Test
{
    public $z;
    public function __construct(
        public int $x,
        /** @SomeAnnotation() */
        public string $y = "123",
        string $z = "abc"
    )
    {
    }
}
