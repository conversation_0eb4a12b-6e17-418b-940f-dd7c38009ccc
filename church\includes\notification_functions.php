<?php
/**
 * Notification System Functions
 * 
 * This file contains helper functions for managing notifications
 */

/**
 * Get unread notification count for a user
 * 
 * @param PDO $pdo Database connection
 * @param int $userId User ID
 * @return int Number of unread notifications
 */
function getUnreadNotificationCount($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM notifications 
            WHERE recipient_id = ? 
            AND is_read = 0 
            AND (expires_at IS NULL OR expires_at > NOW())
        ");
        $stmt->execute([$userId]);
        return (int)$stmt->fetchColumn();
    } catch (Exception $e) {
        error_log("Error getting unread notification count: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get notifications for a user
 * 
 * @param PDO $pdo Database connection
 * @param int $userId User ID
 * @param int $limit Number of notifications to retrieve
 * @param bool $unreadOnly Whether to get only unread notifications
 * @return array Array of notifications
 */
function getUserNotifications($pdo, $userId, $limit = 10, $unreadOnly = false) {
    try {
        $whereClause = "WHERE n.recipient_id = ? AND (n.expires_at IS NULL OR n.expires_at > NOW())";
        if ($unreadOnly) {
            $whereClause .= " AND n.is_read = 0";
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                n.*,
                CASE 
                    WHEN n.sender_type = 'admin' THEN COALESCE(a.full_name, a.username, 'Administrator')
                    WHEN n.sender_type = 'member' THEN COALESCE(m.full_name, CONCAT(m.first_name, ' ', m.last_name), 'Member')
                    ELSE 'System'
                END as sender_name,
                CASE 
                    WHEN n.sender_type = 'admin' THEN a.email
                    WHEN n.sender_type = 'member' THEN m.email
                    ELSE NULL
                END as sender_email
            FROM notifications n
            LEFT JOIN admins a ON n.sender_type = 'admin' AND n.sender_id = a.id
            LEFT JOIN members m ON n.sender_type = 'member' AND n.sender_id = m.id
            $whereClause
            ORDER BY n.created_at DESC
            LIMIT ?
        ");
        
        $params = [$userId];
        if ($limit > 0) {
            $params[] = $limit;
        }
        
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting user notifications: " . $e->getMessage());
        return [];
    }
}

/**
 * Create a new notification
 * 
 * @param PDO $pdo Database connection
 * @param int $recipientId Recipient user ID
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type
 * @param int|null $senderId Sender ID (null for system notifications)
 * @param string $senderType Sender type ('admin' or 'member')
 * @param string|null $actionUrl Optional action URL
 * @param string $priority Priority level
 * @param string|null $expiresAt Expiration date
 * @return bool Success status
 */
function createNotification($pdo, $recipientId, $title, $message, $type = 'message', $senderId = null, $senderType = 'admin', $actionUrl = null, $priority = 'normal', $expiresAt = null) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO notifications 
            (recipient_id, sender_id, sender_type, notification_type, title, message, action_url, priority, expires_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $recipientId, $senderId, $senderType, $type, $title, $message, $actionUrl, $priority, $expiresAt
        ]);
    } catch (Exception $e) {
        error_log("Error creating notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark notification as read
 * 
 * @param PDO $pdo Database connection
 * @param int $notificationId Notification ID
 * @param int $userId User ID
 * @return bool Success status
 */
function markNotificationAsRead($pdo, $notificationId, $userId) {
    try {
        $stmt = $pdo->prepare("
            UPDATE notifications 
            SET is_read = 1, read_at = NOW() 
            WHERE id = ? AND recipient_id = ?
        ");
        
        return $stmt->execute([$notificationId, $userId]);
    } catch (Exception $e) {
        error_log("Error marking notification as read: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark all notifications as read for a user
 * 
 * @param PDO $pdo Database connection
 * @param int $userId User ID
 * @return bool Success status
 */
function markAllNotificationsAsRead($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("
            UPDATE notifications 
            SET is_read = 1, read_at = NOW() 
            WHERE recipient_id = ? AND is_read = 0
        ");
        
        return $stmt->execute([$userId]);
    } catch (Exception $e) {
        error_log("Error marking all notifications as read: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete old notifications
 * 
 * @param PDO $pdo Database connection
 * @param int $daysOld Number of days old to delete
 * @return int Number of deleted notifications
 */
function deleteOldNotifications($pdo, $daysOld = 30) {
    try {
        $stmt = $pdo->prepare("
            DELETE FROM notifications 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            OR (expires_at IS NOT NULL AND expires_at < NOW())
        ");
        
        $stmt->execute([$daysOld]);
        return $stmt->rowCount();
    } catch (Exception $e) {
        error_log("Error deleting old notifications: " . $e->getMessage());
        return 0;
    }
}

/**
 * Send notification to multiple users
 * 
 * @param PDO $pdo Database connection
 * @param array $recipientIds Array of recipient user IDs
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type
 * @param int|null $senderId Sender ID
 * @param string $senderType Sender type
 * @param string|null $actionUrl Optional action URL
 * @param string $priority Priority level
 * @return array Results array with success count and errors
 */
function sendBulkNotification($pdo, $recipientIds, $title, $message, $type = 'announcement', $senderId = null, $senderType = 'admin', $actionUrl = null, $priority = 'normal') {
    $successCount = 0;
    $errors = [];
    
    foreach ($recipientIds as $recipientId) {
        if (createNotification($pdo, $recipientId, $title, $message, $type, $senderId, $senderType, $actionUrl, $priority)) {
            $successCount++;
        } else {
            $errors[] = "Failed to send notification to user ID: $recipientId";
        }
    }
    
    return [
        'success_count' => $successCount,
        'total_count' => count($recipientIds),
        'errors' => $errors
    ];
}

/**
 * Get notification icon based on type
 * 
 * @param string $type Notification type
 * @return string Bootstrap icon class
 */
function getNotificationIcon($type) {
    $icons = [
        'announcement' => 'bi-megaphone',
        'message' => 'bi-chat-dots',
        'birthday' => 'bi-gift',
        'event' => 'bi-calendar-event',
        'donation' => 'bi-heart',
        'system' => 'bi-gear'
    ];
    
    return $icons[$type] ?? 'bi-bell';
}

/**
 * Get notification color class based on priority
 * 
 * @param string $priority Notification priority
 * @return string CSS color class
 */
function getNotificationColorClass($priority) {
    $colors = [
        'low' => 'text-muted',
        'normal' => 'text-primary',
        'high' => 'text-warning',
        'urgent' => 'text-danger'
    ];
    
    return $colors[$priority] ?? 'text-primary';
}

/**
 * Format notification time
 * 
 * @param string $datetime Datetime string
 * @return string Formatted time string
 */
function formatNotificationTime($datetime) {
    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;
    
    if ($diff < 60) {
        return 'Just now';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } else {
        return date('M j, Y', $time);
    }
}
?>
