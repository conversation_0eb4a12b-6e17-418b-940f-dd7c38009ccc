<?php
/**
 * Shared Functions for Admin Panel
 * Common functions used across multiple admin files to avoid code duplication
 */

/**
 * Update a setting in the settings table
 * @param PDO $pdo Database connection
 * @param string $key Setting key
 * @param string $value Setting value
 * @return bool Success status
 */
function updateSetting($pdo, $key, $value) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO settings (setting_key, setting_value, updated_at)
            VALUES (?, ?, NOW())
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
        ");
        return $stmt->execute([$key, $value]);
    } catch (PDOException $e) {
        error_log("Error updating setting $key: " . $e->getMessage());
        return false;
    }
}

/**
 * Update multiple settings at once
 * @param PDO $pdo Database connection
 * @param array $settings Array of key => value pairs
 * @return array Results array with success status and any errors
 */
function updateMultipleSettings($pdo, $settings) {
    $results = ['success' => true, 'errors' => []];
    
    try {
        $pdo->beginTransaction();
        
        foreach ($settings as $key => $value) {
            if (!updateSetting($pdo, $key, $value)) {
                $results['errors'][] = "Failed to update setting: $key";
                $results['success'] = false;
            }
        }
        
        if ($results['success']) {
            $pdo->commit();
        } else {
            $pdo->rollback();
        }
        
    } catch (Exception $e) {
        $pdo->rollback();
        $results['success'] = false;
        $results['errors'][] = $e->getMessage();
    }
    
    return $results;
}

/**
 * Update an appearance setting
 * @param PDO $pdo Database connection
 * @param string $key Setting key
 * @param string $value Setting value
 * @return bool Success status
 */
function updateAppearanceSetting($pdo, $key, $value) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO appearance_settings (setting_name, setting_value, updated_at)
            VALUES (?, ?, NOW())
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
        ");
        return $stmt->execute([$key, $value]);
    } catch (PDOException $e) {
        error_log("Error updating appearance setting $key: " . $e->getMessage());
        return false;
    }
}

/**
 * Update multiple appearance settings at once
 * @param PDO $pdo Database connection
 * @param array $settings Array of key => value pairs
 * @return array Results array with success status and any errors
 */
function updateMultipleAppearanceSettings($pdo, $settings) {
    $results = ['success' => true, 'errors' => []];
    
    try {
        $pdo->beginTransaction();
        
        foreach ($settings as $key => $value) {
            if (!updateAppearanceSetting($pdo, $key, $value)) {
                $results['errors'][] = "Failed to update appearance setting: $key";
                $results['success'] = false;
            }
        }
        
        if ($results['success']) {
            $pdo->commit();
        } else {
            $pdo->rollback();
        }
        
    } catch (Exception $e) {
        $pdo->rollback();
        $results['success'] = false;
        $results['errors'][] = $e->getMessage();
    }
    
    return $results;
}

/**
 * Get current settings from database
 * @param PDO $pdo Database connection
 * @param array $keys Array of setting keys to retrieve
 * @param array $defaults Default values for settings
 * @return array Settings array
 */
function getCurrentSettings($pdo, $keys, $defaults = []) {
    $settings = [];
    
    try {
        $placeholders = str_repeat('?,', count($keys) - 1) . '?';
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ($placeholders)");
        $stmt->execute($keys);
        
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        // Apply defaults for missing settings
        foreach ($keys as $key) {
            if (!isset($settings[$key])) {
                $settings[$key] = $defaults[$key] ?? '';
            }
        }
        
    } catch (PDOException $e) {
        error_log("Error getting current settings: " . $e->getMessage());
        // Return defaults if database error
        foreach ($keys as $key) {
            $settings[$key] = $defaults[$key] ?? '';
        }
    }
    
    return $settings;
}

/**
 * Get current appearance settings from database
 * @param PDO $pdo Database connection
 * @param array $keys Array of setting keys to retrieve
 * @param array $defaults Default values for settings
 * @return array Settings array
 */
function getCurrentAppearanceSettings($pdo, $keys, $defaults = []) {
    $settings = [];
    
    try {
        $placeholders = str_repeat('?,', count($keys) - 1) . '?';
        $stmt = $pdo->prepare("SELECT setting_name, setting_value FROM appearance_settings WHERE setting_name IN ($placeholders)");
        $stmt->execute($keys);
        
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_name']] = $row['setting_value'];
        }
        
        // Apply defaults for missing settings
        foreach ($keys as $key) {
            if (!isset($settings[$key])) {
                $settings[$key] = $defaults[$key] ?? '';
            }
        }
        
    } catch (PDOException $e) {
        error_log("Error getting current appearance settings: " . $e->getMessage());
        // Return defaults if database error
        foreach ($keys as $key) {
            $settings[$key] = $defaults[$key] ?? '';
        }
    }
    
    return $settings;
}

/**
 * Validate and sanitize form input
 * @param array $data Input data
 * @param array $rules Validation rules
 * @return array Validated data with errors if any
 */
function validateFormInput($data, $rules) {
    $validated = [];
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $value = $data[$field] ?? '';
        
        // Required field check
        if (isset($rule['required']) && $rule['required'] && empty($value)) {
            $errors[$field] = ucfirst($field) . ' is required';
            continue;
        }
        
        // Type validation
        if (!empty($value) && isset($rule['type'])) {
            switch ($rule['type']) {
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[$field] = 'Invalid email format';
                        continue 2;
                    }
                    break;
                case 'url':
                    if (!filter_var($value, FILTER_VALIDATE_URL)) {
                        $errors[$field] = 'Invalid URL format';
                        continue 2;
                    }
                    break;
                case 'int':
                    if (!filter_var($value, FILTER_VALIDATE_INT)) {
                        $errors[$field] = 'Must be a valid number';
                        continue 2;
                    }
                    $value = intval($value);
                    break;
                case 'color':
                    if (!preg_match('/^#[a-fA-F0-9]{6}$/', $value)) {
                        $errors[$field] = 'Invalid color format (use #RRGGBB)';
                        continue 2;
                    }
                    break;
            }
        }
        
        // Length validation
        if (!empty($value) && isset($rule['max_length'])) {
            if (strlen($value) > $rule['max_length']) {
                $errors[$field] = ucfirst($field) . ' must be less than ' . $rule['max_length'] . ' characters';
                continue;
            }
        }
        
        // Sanitize
        $validated[$field] = htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
    }
    
    return ['data' => $validated, 'errors' => $errors];
}

/**
 * Generate success/error messages for settings updates
 * @param array $results Results from updateMultipleSettings
 * @param string $successMessage Custom success message
 * @return array Message data for display
 */
function generateSettingsMessage($results, $successMessage = 'Settings updated successfully!') {
    if ($results['success']) {
        return ['type' => 'success', 'message' => $successMessage];
    } else {
        $errorMessage = 'Failed to update settings';
        if (!empty($results['errors'])) {
            $errorMessage .= ': ' . implode(', ', $results['errors']);
        }
        return ['type' => 'error', 'message' => $errorMessage];
    }
}

/**
 * Check if admin session is valid
 * @return bool True if valid admin session
 */
function isValidAdminSession() {
    return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
}

/**
 * Redirect to login if not authenticated
 * @param string $loginUrl Login page URL
 */
function requireAdminAuth($loginUrl = 'login.php') {
    if (!isValidAdminSession()) {
        header("Location: $loginUrl");
        exit();
    }
}
?>
