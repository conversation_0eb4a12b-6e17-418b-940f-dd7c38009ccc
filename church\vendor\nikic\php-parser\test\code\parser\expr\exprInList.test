Expressions in list()
-----
<?php

// This is legal.
list(($a), ((($b)))) = $x;
// This is illegal, but not a syntax error.
list(1 + 1) = $x;
-----
array(
    0: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_List(
                items: array(
                    0: ArrayItem(
                        key: null
                        value: Expr_Variable(
                            name: a
                        )
                        byRef: false
                        unpack: false
                    )
                    1: ArrayItem(
                        key: null
                        value: Expr_Variable(
                            name: b
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            expr: Expr_Variable(
                name: x
            )
        )
        comments: array(
            0: // This is legal.
        )
    )
    1: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_List(
                items: array(
                    0: ArrayItem(
                        key: null
                        value: Expr_BinaryOp_Plus(
                            left: Scalar_Int(
                                value: 1
                            )
                            right: Scalar_Int(
                                value: 1
                            )
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            expr: Expr_Variable(
                name: x
            )
        )
        comments: array(
            0: // This is illegal, but not a syntax error.
        )
    )
)
