<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';
require_once '../includes/notification_functions.php';

$userId = $_SESSION['user_id'];

// Get user data
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$userId]);
$userData = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Handle actions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'mark_read' && isset($_POST['notification_id'])) {
        $notificationId = (int)$_POST['notification_id'];
        if (markNotificationAsRead($pdo, $notificationId, $userId)) {
            $message = 'Notification marked as read.';
        } else {
            $error = 'Failed to mark notification as read.';
        }
    } elseif ($action === 'mark_all_read') {
        if (markAllNotificationsAsRead($pdo, $userId)) {
            $message = 'All notifications marked as read.';
        } else {
            $error = 'Failed to mark all notifications as read.';
        }
    } elseif ($action === 'delete' && isset($_POST['notification_id'])) {
        $notificationId = (int)$_POST['notification_id'];
        try {
            $stmt = $pdo->prepare("DELETE FROM notifications WHERE id = ? AND recipient_id = ?");
            if ($stmt->execute([$notificationId, $userId])) {
                $message = 'Notification deleted.';
            } else {
                $error = 'Failed to delete notification.';
            }
        } catch (Exception $e) {
            $error = 'Error deleting notification.';
        }
    }
}

// Pagination
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Get total count
$stmt = $pdo->prepare("
    SELECT COUNT(*) 
    FROM notifications 
    WHERE recipient_id = ? 
    AND (expires_at IS NULL OR expires_at > NOW())
");
$stmt->execute([$userId]);
$totalNotifications = $stmt->fetchColumn();
$totalPages = ceil($totalNotifications / $limit);

// Get notifications with pagination
$stmt = $pdo->prepare("
    SELECT 
        n.*,
        CASE 
            WHEN n.sender_type = 'admin' THEN COALESCE(a.full_name, a.username, 'Administrator')
            WHEN n.sender_type = 'member' THEN COALESCE(m.full_name, CONCAT(m.first_name, ' ', m.last_name), 'Member')
            ELSE 'System'
        END as sender_name
    FROM notifications n
    LEFT JOIN admins a ON n.sender_type = 'admin' AND n.sender_id = a.id
    LEFT JOIN members m ON n.sender_type = 'member' AND n.sender_id = m.id
    WHERE n.recipient_id = ? 
    AND (n.expires_at IS NULL OR n.expires_at > NOW())
    ORDER BY n.created_at DESC
    LIMIT ? OFFSET ?
");
$stmt->execute([$userId, $limit, $offset]);
$notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get site settings
$sitename = get_site_setting('site_name', get_organization_name() . ' - Notifications');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .navbar-nav .nav-link.active {
            color: white !important;
            font-weight: 600;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: background-color 0.15s ease-in-out;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item i {
            width: 16px;
            margin-right: 8px;
        }

        .notification-card {
            border-left: 4px solid #dee2e6;
            transition: all 0.2s;
            background-color: white;
            border-radius: var(--bs-border-radius, 0.375rem);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .notification-card.unread {
            border-left-color: var(--bs-primary, #0d6efd);
            background-color: #f8f9ff;
        }

        .notification-card.priority-high {
            border-left-color: #ffc107;
        }

        .notification-card.priority-urgent {
            border-left-color: #dc3545;
        }

        .notification-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .notification-actions {
            opacity: 0;
            transition: opacity 0.2s;
        }

        .notification-card:hover .notification-actions {
            opacity: 1;
        }

        .notification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .page-header {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .page-header h2 {
            margin: 0;
            font-weight: 600;
        }

        .btn-primary {
            background-color: var(--bs-primary, #667eea);
            border-color: var(--bs-primary, #667eea);
        }

        .btn-primary:hover {
            background-color: var(--bs-secondary, #764ba2);
            border-color: var(--bs-secondary, #764ba2);
        }

        .pagination .page-link {
            color: var(--bs-primary, #667eea);
        }

        .pagination .page-item.active .page-link {
            background-color: var(--bs-primary, #667eea);
            border-color: var(--bs-primary, #667eea);
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2><i class="bi bi-bell me-2"></i> Notifications</h2>
                        <div>
                            <?php if ($totalNotifications > 0): ?>
                                <form method="post" class="d-inline">
                                    <input type="hidden" name="action" value="mark_all_read">
                                    <button type="submit" class="btn btn-light btn-sm">
                                        <i class="bi bi-check-all"></i> Mark All as Read
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-12">
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (empty($notifications)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-bell-slash display-1 text-muted"></i>
                        <h4 class="mt-3 text-muted">No Notifications</h4>
                        <p class="text-muted">You don't have any notifications at the moment.</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($notifications as $notification): ?>
                            <div class="col-12 mb-3">
                                <div class="card notification-card <?php echo $notification['is_read'] ? '' : 'unread'; ?> <?php echo $notification['priority'] !== 'normal' ? 'priority-' . $notification['priority'] : ''; ?>">
                                    <div class="card-body">
                                        <div class="d-flex">
                                            <div class="notification-icon bg-light <?php echo getNotificationColorClass($notification['priority']); ?>">
                                                <i class="bi <?php echo getNotificationIcon($notification['notification_type']); ?> fs-4"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="card-title mb-1">
                                                            <?php echo htmlspecialchars($notification['title']); ?>
                                                            <?php if (!$notification['is_read']): ?>
                                                                <span class="badge bg-primary ms-2">New</span>
                                                            <?php endif; ?>
                                                            <?php if ($notification['priority'] === 'urgent'): ?>
                                                                <span class="badge bg-danger ms-2">Urgent</span>
                                                            <?php elseif ($notification['priority'] === 'high'): ?>
                                                                <span class="badge bg-warning ms-2">High</span>
                                                            <?php endif; ?>
                                                        </h6>
                                                        <p class="card-text text-muted mb-2"><?php echo nl2br(htmlspecialchars($notification['message'])); ?></p>
                                                        <small class="text-muted">
                                                            <i class="bi bi-clock"></i> <?php echo formatNotificationTime($notification['created_at']); ?>
                                                            <?php if ($notification['sender_name']): ?>
                                                                • From: <?php echo htmlspecialchars($notification['sender_name']); ?>
                                                            <?php endif; ?>
                                                        </small>
                                                    </div>
                                                    <div class="notification-actions">
                                                        <?php if (!$notification['is_read']): ?>
                                                            <form method="post" class="d-inline">
                                                                <input type="hidden" name="action" value="mark_read">
                                                                <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                                <button type="submit" class="btn btn-sm btn-outline-primary" title="Mark as read">
                                                                    <i class="bi bi-check"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                        <form method="post" class="d-inline">
                                                            <input type="hidden" name="action" value="delete">
                                                            <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-danger ms-1" title="Delete" onclick="return confirm('Are you sure you want to delete this notification?')">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                                <?php if ($notification['action_url']): ?>
                                                    <div class="mt-2">
                                                        <a href="<?php echo htmlspecialchars($notification['action_url']); ?>" class="btn btn-sm btn-primary">
                                                            <i class="bi bi-arrow-right"></i> View Details
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Notifications pagination">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
