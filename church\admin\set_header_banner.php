<?php
/**
 * Set Header Banner Handler
 * 
 * Handles setting a promotional material as the header banner for an event
 */

session_start();

// Include configuration
require_once '../config.php';
require_once 'includes/auth_check.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

$file_id = filter_var($input['file_id'] ?? null, FILTER_VALIDATE_INT);
$event_id = filter_var($input['event_id'] ?? null, FILTER_VALIDATE_INT);

if (!$file_id || !$event_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid file ID or event ID']);
    exit();
}

try {
    $pdo->beginTransaction();

    // Verify the file belongs to the event
    $stmt = $pdo->prepare("SELECT id, file_name FROM event_files WHERE id = ? AND event_id = ?");
    $stmt->execute([$file_id, $event_id]);
    $file = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$file) {
        echo json_encode(['success' => false, 'message' => 'File not found or does not belong to this event']);
        exit();
    }

    // Remove header banner designation from all files for this event
    $stmt = $pdo->prepare("UPDATE event_files SET is_header_banner = 0 WHERE event_id = ?");
    $stmt->execute([$event_id]);

    // Set the selected file as header banner
    $stmt = $pdo->prepare("UPDATE event_files SET is_header_banner = 1 WHERE id = ?");
    $stmt->execute([$file_id]);

    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Header banner set successfully',
        'file_name' => $file['file_name']
    ]);
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo json_encode(['success' => false, 'message' => 'Failed to set header banner: ' . $e->getMessage()]);
}
?>
