<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <defs>
    <linearGradient id="churchGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6366f1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="crossGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="95" fill="url(#churchGradient)" stroke="#e5e7eb" stroke-width="2"/>
  
  <!-- Church Building -->
  <g transform="translate(100, 100)">
    <!-- Main Building -->
    <rect x="-35" y="-10" width="70" height="50" fill="url(#crossGradient)" stroke="#4f46e5" stroke-width="1.5" rx="3"/>
    
    <!-- Church Roof -->
    <polygon points="-40,-10 0,-35 40,-10" fill="url(#crossGradient)" stroke="#4f46e5" stroke-width="1.5"/>
    
    <!-- Cross on Top -->
    <g transform="translate(0, -35)">
      <!-- Vertical part of cross -->
      <rect x="-2" y="-15" width="4" height="20" fill="#4f46e5" rx="1"/>
      <!-- Horizontal part of cross -->
      <rect x="-6" y="-10" width="12" height="3" fill="#4f46e5" rx="1"/>
    </g>
    
    <!-- Church Door -->
    <rect x="-8" y="15" width="16" height="25" fill="#4f46e5" rx="8"/>
    <circle cx="0" cy="27.5" r="1.5" fill="url(#crossGradient)"/>
    
    <!-- Windows -->
    <rect x="-25" y="5" width="8" height="12" fill="#4f46e5" rx="4"/>
    <rect x="17" y="5" width="8" height="12" fill="#4f46e5" rx="4"/>
    
    <!-- Window Details -->
    <line x1="-21" y1="5" x2="-21" y2="17" stroke="url(#crossGradient)" stroke-width="1"/>
    <line x1="-25" y1="11" x2="-17" y2="11" stroke="url(#crossGradient)" stroke-width="1"/>
    <line x1="21" y1="5" x2="21" y2="17" stroke="url(#crossGradient)" stroke-width="1"/>
    <line x1="17" y1="11" x2="25" y2="11" stroke="url(#crossGradient)" stroke-width="1"/>
  </g>
  
  <!-- Organization Name Arc -->
  <defs>
    <path id="topArc" d="M 50 100 A 50 50 0 0 1 150 100"/>
    <path id="bottomArc" d="M 150 100 A 50 50 0 0 1 50 100"/>
  </defs>
  
  <text font-family="Inter, Arial, sans-serif" font-size="14" font-weight="600" fill="#4f46e5">
    <textPath href="#topArc" startOffset="50%" text-anchor="middle">
      FREEDOM ASSEMBLY
    </textPath>
  </text>
  
  <text font-family="Inter, Arial, sans-serif" font-size="12" font-weight="500" fill="#6b7280">
    <textPath href="#bottomArc" startOffset="50%" text-anchor="middle">
      CHURCH
    </textPath>
  </text>
</svg>
