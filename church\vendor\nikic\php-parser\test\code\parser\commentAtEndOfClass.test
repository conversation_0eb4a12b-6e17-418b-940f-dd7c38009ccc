Comment at end of class (#509)
-----
<?php
class MyClass {
    protected $a;
    // my comment
}
-----
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: MyClass
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_Property(
                attrGroups: array(
                )
                flags: PROTECTED (2)
                type: null
                props: array(
                    0: PropertyItem(
                        name: VarLikeIdentifier(
                            name: a
                        )
                        default: null
                    )
                )
                hooks: array(
                )
            )
            1: Stmt_Nop(
                comments: array(
                    0: // my comment
                )
            )
        )
    )
)
