<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

$message = '';
$error = '';

// Get event ID from URL
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if ($event_id <= 0) {
    header("Location: events.php");
    exit();
}

// Get event details
try {
    $stmt = $conn->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
    $event = null;
}

// Create event_sessions table if it doesn't exist
try {
    $conn->exec("
        CREATE TABLE IF NOT EXISTS event_sessions (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            session_datetime DATETIME NOT NULL,
            duration_minutes INT(11) DEFAULT 60,
            max_attendees INT(11) DEFAULT NULL,
            location VARCHAR(255),
            instructor VARCHAR(255),
            created_by INT(11) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_event_id (event_id),
            INDEX idx_session_datetime (session_datetime),
            INDEX idx_created_by (created_by),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
} catch (PDOException $e) {
    error_log("Error creating event_sessions table: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'add_session') {
        $session_title = trim($_POST['session_title']);
        $session_description = trim($_POST['session_description']);
        $session_date = $_POST['session_date'];
        $session_time = $_POST['session_time'];
        $duration_minutes = (int)$_POST['duration_minutes'];
        $max_attendees = !empty($_POST['max_attendees']) ? (int)$_POST['max_attendees'] : null;
        $location = trim($_POST['location']);
        $instructor = trim($_POST['instructor']);

        if (empty($session_title) || empty($session_date) || empty($session_time)) {
            $error = "Please fill in all required fields.";
        } else {
            try {
                // Combine date and time
                $session_datetime = $session_date . ' ' . $session_time;

                $stmt = $conn->prepare("
                    INSERT INTO event_sessions (
                        event_id, session_title, session_description, start_datetime,
                        end_datetime, max_attendees, location, instructor_name,
                        created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");

                // Calculate end datetime
                $end_datetime = date('Y-m-d H:i:s', strtotime($session_datetime . ' +' . $duration_minutes . ' minutes'));

                $stmt->execute([
                    $event_id, $session_title, $session_description, $session_datetime,
                    $end_datetime, $max_attendees, $location, $instructor
                ]);

                $message = "Session added successfully!";

                // Clear form data by redirecting
                header("Location: event_sessions.php?event_id=" . $event_id . "&success=1");
                exit();

            } catch (PDOException $e) {
                $error = "Error adding session: " . $e->getMessage();
            }
        }
    }
}

// Check for success message from redirect
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $message = "Session added successfully!";
}

// Page title and header info
$page_title = 'Event Sessions - ' . ($event ? htmlspecialchars($event['title']) : 'Unknown Event');
$page_header = 'Event Sessions';
$page_description = 'Manage sessions for: ' . ($event ? htmlspecialchars($event['title']) : 'Unknown Event');

// Include header
include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-collection"></i> Event Sessions
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="events.php" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Events
        </a>
    </div>
</div>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($event): ?>
<!-- Event Information -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-calendar-event"></i> Event Information
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h4><?php echo htmlspecialchars($event['title']); ?></h4>
                <p class="text-muted"><?php echo htmlspecialchars($event['description']); ?></p>
            </div>
            <div class="col-md-4">
                <p><strong>Date:</strong> <?php echo date('M j, Y g:i A', strtotime($event['event_date'])); ?></p>
                <p><strong>Location:</strong> <?php echo htmlspecialchars($event['location'] ?: 'Not specified'); ?></p>
                <p><strong>Capacity:</strong> <?php echo $event['max_attendees'] ? number_format($event['max_attendees']) : 'Unlimited'; ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Sessions Management -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-collection"></i> Event Sessions
        </h5>
        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addSessionModal">
            <i class="bi bi-plus-circle"></i> Add Session
        </button>
    </div>
    <div class="card-body">
        <div class="text-center py-5">
            <i class="bi bi-collection display-1 text-muted"></i>
            <h4 class="mt-3">Session Management</h4>
            <p class="text-muted">Session management functionality is coming soon.</p>
            <p class="text-muted">This feature will allow you to create and manage multiple sessions within an event.</p>
        </div>
    </div>
</div>

<?php else: ?>
<div class="alert alert-warning">
    <i class="bi bi-exclamation-triangle"></i> Event not found or could not be loaded.
</div>
<?php endif; ?>

<!-- Add Session Modal -->
<div class="modal fade" id="addSessionModal" tabindex="-1" aria-labelledby="addSessionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addSessionModalLabel">
                    <i class="bi bi-plus-circle"></i> Add New Session
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addSessionForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_session">
                    <input type="hidden" name="event_id" value="<?php echo htmlspecialchars($event_id); ?>">

                    <div class="mb-3">
                        <label for="session_title" class="form-label">Session Title *</label>
                        <input type="text" class="form-control" id="session_title" name="session_title" required>
                    </div>

                    <div class="mb-3">
                        <label for="session_description" class="form-label">Description</label>
                        <textarea class="form-control" id="session_description" name="session_description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="session_date" class="form-label">Session Date *</label>
                                <input type="date" class="form-control" id="session_date" name="session_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="session_time" class="form-label">Session Time *</label>
                                <input type="time" class="form-control" id="session_time" name="session_time" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="duration_minutes" class="form-label">Duration (minutes)</label>
                                <input type="number" class="form-control" id="duration_minutes" name="duration_minutes" min="15" step="15" value="60">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_attendees" class="form-label">Max Attendees</label>
                                <input type="number" class="form-control" id="max_attendees" name="max_attendees" min="1">
                                <div class="form-text">Leave empty for unlimited</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="location" name="location">
                    </div>

                    <div class="mb-3">
                        <label for="instructor" class="form-label">Instructor/Speaker</label>
                        <input type="text" class="form-control" id="instructor" name="instructor">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Session
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
