{"packages": [{"name": "doctrine/instantiator", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}, "time": "2022-12-30T00:23:10+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "install-path": "../doctrine/instantiator"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "time": "2020-07-09T08:09:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "installation-source": "source", "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "install-path": "../hamcrest/hamcrest-php"}, {"name": "mockery/mockery", "version": "1.6.12", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/1f4efdd7d3beafe9807b08156dfcb176d18f1699", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "time": "2024-05-16T03:13:13+00:00", "type": "library", "installation-source": "source", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "install-path": "../mockery/mockery"}, {"name": "myclabs/deep-copy", "version": "1.13.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "024473a478be9df5fdaca2c793f2232fe788e414"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/024473a478be9df5fdaca2c793f2232fe788e414", "reference": "024473a478be9df5fdaca2c793f2232fe788e414", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "time": "2025-02-12T12:17:51+00:00", "type": "library", "installation-source": "source", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "install-path": "../myclabs/deep-copy"}, {"name": "nikic/php-parser", "version": "v5.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "447a020a1f875a434d62f2a401f53b82a396e494"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/447a020a1f875a434d62f2a401f53b82a396e494", "reference": "447a020a1f875a434d62f2a401f53b82a396e494", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "time": "2024-12-30T11:07:19+00:00", "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "installation-source": "source", "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.4.0"}, "install-path": "../nikic/php-parser"}, {"name": "paragonie/constant_time_encoding", "version": "v3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/df1e7fde177501eee2037dd159cf04f5f301a512", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512", "shasum": ""}, "require": {"php": "^8"}, "require-dev": {"phpunit/phpunit": "^9", "vimeo/psalm": "^4|^5"}, "time": "2024-05-08T12:36:18+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "install-path": "../paragonie/constant_time_encoding"}, {"name": "paypal/paypal-checkout-sdk", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/paypal/Checkout-PHP-SDK.git", "reference": "19992ce7051ff9e47e643f28abb8cc1b3e5f1812"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paypal/Checkout-PHP-SDK/zipball/19992ce7051ff9e47e643f28abb8cc1b3e5f1812", "reference": "19992ce7051ff9e47e643f28abb8cc1b3e5f1812", "shasum": ""}, "require": {"paypal/paypalhttp": "1.0.1"}, "require-dev": {"phpunit/phpunit": "^5.7"}, "time": "2021-09-21T20:57:38+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"Sample\\": "samples/", "PayPalCheckoutSdk\\": "lib/PayPalCheckoutSdk"}}, "notification-url": "https://packagist.org/downloads/", "license": ["https://github.com/paypal/Checkout-PHP-SDK/blob/master/LICENSE"], "authors": [{"name": "PayPal", "homepage": "https://github.com/paypal/Checkout-PHP-SDK/contributors"}], "description": "PayPal's PHP SDK for Checkout REST APIs", "homepage": "http://github.com/paypal/Checkout-PHP-SDK/", "keywords": ["checkout", "orders", "payments", "paypal", "rest", "sdk"], "support": {"source": "https://github.com/paypal/Checkout-PHP-SDK/tree/1.0.2"}, "abandoned": "paypal/paypal-server-sdk", "install-path": "../paypal/paypal-checkout-sdk"}, {"name": "paypal/paypalhttp", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/paypal/paypalhttp_php.git", "reference": "7b09c89c80828e842c79230e7f156b61fbb68d25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paypal/paypalhttp_php/zipball/7b09c89c80828e842c79230e7f156b61fbb68d25", "reference": "7b09c89c80828e842c79230e7f156b61fbb68d25", "shasum": ""}, "require": {"ext-curl": "*"}, "require-dev": {"phpunit/phpunit": "^5.7", "wiremock-php/wiremock-php": "1.43.2"}, "time": "2021-09-14T21:35:26+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"PayPalHttp\\": "lib/PayPalHttp"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PayPal", "homepage": "https://github.com/paypal/paypalhttp_php/contributors"}], "support": {"issues": "https://github.com/paypal/paypalhttp_php/issues", "source": "https://github.com/paypal/paypalhttp_php/tree/1.0.1"}, "install-path": "../paypal/paypalhttp"}, {"name": "phar-io/manifest", "version": "2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "time": "2024-03-03T12:33:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "install-path": "../phar-io/manifest"}, {"name": "phar-io/version", "version": "3.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2022-02-21T01:04:05+00:00", "type": "library", "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "install-path": "../phar-io/version"}, {"name": "phpmailer/phpmailer", "version": "v6.9.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "2f5c94fe7493efc213f643c23b1b1c249d40f47e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/2f5c94fe7493efc213f643c23b1b1c249d40f47e", "reference": "2f5c94fe7493efc213f643c23b1b1c249d40f47e", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "doctrine/annotations": "^1.2.6 || ^1.13.3", "php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.7.2", "yoast/phpunit-polyfills": "^1.0.4"}, "suggest": {"decomplexity/SendOauth2": "Adapter for using XOAUTH2 authentication", "ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "ext-openssl": "Needed for secure SMTP sending and DKIM signing", "greew/oauth2-azure-provider": "Needed for Microsoft Azure XOAUTH2 authentication", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)", "thenetworg/oauth2-azure": "Needed for Microsoft XOAUTH2 authentication"}, "time": "2024-11-24T18:04:13+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v6.9.3"}, "funding": [{"url": "https://github.com/Synchro", "type": "github"}], "install-path": "../phpmailer/phpmailer"}, {"name": "phpunit/php-code-coverage", "version": "9.2.32", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/85402a822d1ecf1db1096959413d35e1c37cf1a5", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-text-template": "^2.0.4", "sebastian/code-unit-reverse-lookup": "^2.0.3", "sebastian/complexity": "^2.0.3", "sebastian/environment": "^5.1.5", "sebastian/lines-of-code": "^1.0.4", "sebastian/version": "^3.0.2", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "time": "2024-08-22T04:23:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "9.2.x-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.32"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-code-coverage"}, {"name": "phpunit/php-file-iterator", "version": "3.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2021-12-02T12:48:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-file-iterator"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "time": "2020-09-28T05:58:55+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-invoker"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-10-26T05:33:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-text-template"}, {"name": "phpunit/php-timer", "version": "5.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-10-26T13:16:10+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-timer"}, {"name": "phpunit/phpunit", "version": "9.6.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "f80235cb4d3caa59ae09be3adf1ded27521d1a9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit/zipball/f80235cb4d3caa59ae09be3adf1ded27521d1a9c", "reference": "f80235cb4d3caa59ae09be3adf1ded27521d1a9c", "shasum": ""}, "require": {"doctrine/instantiator": "^1.5.0 || ^2", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.12.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=7.3", "phpunit/php-code-coverage": "^9.2.32", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.4", "phpunit/php-timer": "^5.0.3", "sebastian/cli-parser": "^1.0.2", "sebastian/code-unit": "^1.0.8", "sebastian/comparator": "^4.0.8", "sebastian/diff": "^4.0.6", "sebastian/environment": "^5.1.5", "sebastian/exporter": "^4.0.6", "sebastian/global-state": "^5.0.7", "sebastian/object-enumerator": "^4.0.4", "sebastian/resource-operations": "^3.0.4", "sebastian/type": "^3.2.1", "sebastian/version": "^3.0.2"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "time": "2024-12-05T13:48:26+00:00", "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.6-dev"}}, "installation-source": "source", "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/9.6.22"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "install-path": "../phpunit/phpunit"}, {"name": "pragmarx/google2fa", "version": "v8.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/antonioribeiro/google2fa.git", "reference": "6f8d87ebd5afbf7790bde1ffc7579c7c705e0fad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/google2fa/zipball/6f8d87ebd5afbf7790bde1ffc7579c7c705e0fad", "reference": "6f8d87ebd5afbf7790bde1ffc7579c7c705e0fad", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1.0|^2.0|^3.0", "php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^1.9", "phpunit/phpunit": "^7.5.15|^8.5|^9.0"}, "time": "2024-09-05T11:56:40+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"PragmaRX\\Google2FA\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "A One Time Password Authentication package, compatible with Google Authenticator.", "keywords": ["2fa", "Authentication", "Two Factor Authentication", "google2fa"], "support": {"issues": "https://github.com/antonioribeiro/google2fa/issues", "source": "https://github.com/antonioribeiro/google2fa/tree/v8.0.3"}, "install-path": "../pragmarx/google2fa"}, {"name": "sebastian/cli-parser", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2024-03-02T06:27:43+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/cli-parser"}, {"name": "sebastian/code-unit", "version": "1.0.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-10-26T13:08:54+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/code-unit"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-09-28T05:30:19+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/code-unit-reverse-lookup"}, {"name": "sebastian/comparator", "version": "4.0.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "fa0f136dd2334583309d32b62544682ee972b51a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/fa0f136dd2334583309d32b62544682ee972b51a", "reference": "fa0f136dd2334583309d32b62544682ee972b51a", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2022-09-14T12:41:17+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/comparator"}, {"name": "sebastian/complexity", "version": "2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/25f207c40d62b8b7aa32f5ab026c53561964053a", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2023-12-22T06:19:30+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/complexity"}, {"name": "sebastian/diff", "version": "4.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/ba01945089c3a293b01ba9badc29ad55b106b0bc", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "time": "2024-03-02T06:30:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/diff"}, {"name": "sebastian/environment", "version": "5.1.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "time": "2023-02-03T06:03:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/5.1.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/environment"}, {"name": "sebastian/exporter", "version": "4.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/78c00df8f170e02473b682df15bfcdacc3d32d72", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "time": "2024-03-02T06:33:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/exporter"}, {"name": "sebastian/global-state", "version": "5.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "time": "2024-03-02T06:35:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.7"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/global-state"}, {"name": "sebastian/lines-of-code", "version": "1.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/e1e4a170560925c26d424b6a03aed157e7dcc5c5", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2023-12-22T06:20:34+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/lines-of-code"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-10-26T13:12:34+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/object-enumerator"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2020-10-26T13:14:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/object-reflector"}, {"name": "sebastian/recursion-context", "version": "4.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "time": "2023-02-03T06:07:39+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/recursion-context"}, {"name": "sebastian/resource-operations", "version": "3.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "time": "2024-03-14T16:00:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/resource-operations"}, {"name": "sebastian/type", "version": "3.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/type/zipball/75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "time": "2023-02-03T06:13:03+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/3.2.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/type"}, {"name": "sebastian/version", "version": "3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": ""}, "require": {"php": ">=7.3"}, "time": "2020-09-28T06:39:44+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/version"}, {"name": "stripe/stripe-php", "version": "v16.6.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/stripe/stripe-php.git", "reference": "d6de0a536f00b5c5c74f36b8f4d0d93b035499ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stripe/stripe-php/zipball/d6de0a536f00b5c5c74f36b8f4d0d93b035499ff", "reference": "d6de0a536f00b5c5c74f36b8f4d0d93b035499ff", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.6.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.5.0", "phpstan/phpstan": "^1.2", "phpunit/phpunit": "^5.7 || ^9.0"}, "time": "2025-02-24T22:35:29+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "source", "autoload": {"psr-4": {"Stripe\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Stripe and contributors", "homepage": "https://github.com/stripe/stripe-php/contributors"}], "description": "Stripe PHP Library", "homepage": "https://stripe.com/", "keywords": ["api", "payment processing", "stripe"], "support": {"issues": "https://github.com/stripe/stripe-php/issues", "source": "https://github.com/stripe/stripe-php/tree/v16.6.0"}, "install-path": "../stripe/stripe-php"}, {"name": "theseer/tokenizer", "version": "1.2.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "time": "2024-03-03T12:36:25+00:00", "type": "library", "installation-source": "source", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "install-path": "../theseer/tokenizer"}], "dev": true, "dev-package-names": ["doctrine/instantiator", "hamcrest/hamcrest-php", "mockery/mockery", "myclabs/deep-copy", "nikic/php-parser", "phar-io/manifest", "phar-io/version", "phpunit/php-code-coverage", "phpunit/php-file-iterator", "phpunit/php-invoker", "phpunit/php-text-template", "phpunit/php-timer", "phpunit/phpunit", "sebastian/cli-parser", "sebastian/code-unit", "sebastian/code-unit-reverse-lookup", "sebastian/comparator", "sebastian/complexity", "sebastian/diff", "sebastian/environment", "sebastian/exporter", "sebastian/global-state", "sebastian/lines-of-code", "sebastian/object-enumerator", "sebastian/object-reflector", "sebastian/recursion-context", "sebastian/resource-operations", "sebastian/type", "sebastian/version", "theseer/tokenizer"]}