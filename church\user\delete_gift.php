<?php
/**
 * Delete Gift Handler
 * 
 * Handles deletion of gifts for authenticated users
 */

// Include configuration first
require_once '../config.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: my_gifts.php");
    exit();
}

// Get parameters
$gift_id = isset($_POST['gift_id']) ? (int)$_POST['gift_id'] : 0;
$gift_type = isset($_POST['gift_type']) ? $_POST['gift_type'] : '';

if (!$gift_id) {
    $_SESSION['error'] = 'Invalid gift ID';
    header("Location: my_gifts.php");
    exit();
}

try {
    // Verify the gift exists and user has permission to delete it
    $stmt = $pdo->prepare("
        SELECT mg.*, 
               sender.full_name as sender_name, 
               recipient.full_name as recipient_name
        FROM member_gifts mg
        LEFT JOIN members sender ON mg.sender_id = sender.id
        LEFT JOIN members recipient ON mg.recipient_id = recipient.id
        WHERE mg.id = ? AND (mg.sender_id = ? OR mg.recipient_id = ?)
    ");
    $stmt->execute([$gift_id, $userId, $userId]);
    $gift = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$gift) {
        $_SESSION['error'] = 'Gift not found or you do not have permission to delete it';
        header("Location: my_gifts.php");
        exit();
    }
    
    // Determine if user can delete this gift
    $can_delete = false;
    $is_sender = ($gift['sender_id'] == $userId);
    $is_recipient = ($gift['recipient_id'] == $userId);
    
    // Allow deletion based on gift type and user role
    if ($gift_type === 'sent' && $is_sender) {
        $can_delete = true;
    } elseif ($gift_type === 'received' && $is_recipient) {
        $can_delete = true;
    }
    
    if (!$can_delete) {
        $_SESSION['error'] = 'You do not have permission to delete this gift';
        header("Location: my_gifts.php");
        exit();
    }
    
    // Begin transaction
    $pdo->beginTransaction();
    
    // Delete any associated files if they exist
    if (!empty($gift['gift_file_path']) && file_exists('../' . $gift['gift_file_path'])) {
        unlink('../' . $gift['gift_file_path']);
    }
    
    // Delete the gift from database
    $stmt = $pdo->prepare("DELETE FROM member_gifts WHERE id = ?");
    $stmt->execute([$gift_id]);
    
    // Commit transaction
    $pdo->commit();
    
    $_SESSION['success'] = 'Gift deleted successfully';
    
} catch (PDOException $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Error deleting gift: " . $e->getMessage());
    $_SESSION['error'] = 'An error occurred while deleting the gift. Please try again.';
}

// Redirect back to my gifts page
header("Location: my_gifts.php");
exit();
?>
