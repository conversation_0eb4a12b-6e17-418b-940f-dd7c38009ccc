Property promotion
-----
<?php

class Point {
    public function __construct(
        public float $x = 0.0,
        protected array $y = [],
        private string $z = 'hello',
        public readonly int $a = 0,
        public $h { set => $value; },
        public $g = 1 { get => 2; },
    ) {}
}
-----
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: Point
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_ClassMethod(
                attrGroups: array(
                )
                flags: PUBLIC (1)
                byRef: false
                name: Identifier(
                    name: __construct
                )
                params: array(
                    0: Param(
                        attrGroups: array(
                        )
                        flags: PUBLIC (1)
                        type: Identifier(
                            name: float
                        )
                        byRef: false
                        variadic: false
                        var: Expr_Variable(
                            name: x
                        )
                        default: Scalar_Float(
                            value: 0
                        )
                        hooks: array(
                        )
                    )
                    1: Param(
                        attrGroups: array(
                        )
                        flags: PROTECTED (2)
                        type: Identifier(
                            name: array
                        )
                        byRef: false
                        variadic: false
                        var: Expr_Variable(
                            name: y
                        )
                        default: Expr_Array(
                            items: array(
                            )
                        )
                        hooks: array(
                        )
                    )
                    2: Param(
                        attrGroups: array(
                        )
                        flags: PRIVATE (4)
                        type: Identifier(
                            name: string
                        )
                        byRef: false
                        variadic: false
                        var: Expr_Variable(
                            name: z
                        )
                        default: Scalar_String(
                            value: hello
                        )
                        hooks: array(
                        )
                    )
                    3: Param(
                        attrGroups: array(
                        )
                        flags: PUBLIC | READONLY (65)
                        type: Identifier(
                            name: int
                        )
                        byRef: false
                        variadic: false
                        var: Expr_Variable(
                            name: a
                        )
                        default: Scalar_Int(
                            value: 0
                        )
                        hooks: array(
                        )
                    )
                    4: Param(
                        attrGroups: array(
                        )
                        flags: PUBLIC (1)
                        type: null
                        byRef: false
                        variadic: false
                        var: Expr_Variable(
                            name: h
                        )
                        default: null
                        hooks: array(
                            0: PropertyHook(
                                attrGroups: array(
                                )
                                flags: 0
                                byRef: false
                                name: Identifier(
                                    name: set
                                )
                                params: array(
                                )
                                body: Expr_Variable(
                                    name: value
                                )
                            )
                        )
                    )
                    5: Param(
                        attrGroups: array(
                        )
                        flags: PUBLIC (1)
                        type: null
                        byRef: false
                        variadic: false
                        var: Expr_Variable(
                            name: g
                        )
                        default: Scalar_Int(
                            value: 1
                        )
                        hooks: array(
                            0: PropertyHook(
                                attrGroups: array(
                                )
                                flags: 0
                                byRef: false
                                name: Identifier(
                                    name: get
                                )
                                params: array(
                                )
                                body: Scalar_Int(
                                    value: 2
                                )
                            )
                        )
                    )
                )
                returnType: null
                stmts: array(
                )
            )
        )
    )
)
