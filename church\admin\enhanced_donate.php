<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include language system
require_once 'includes/language.php';

// Set page variables
$page_title = __('enhanced_donation_system');
$page_header = __('enhanced_donation_system');
$page_description = __('support_freedom_assembly_church_with_flexible_giving_options');

// Include header
include 'includes/header.php';
?>

<!-- Enhanced Donation System Admin Interface -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-heart-fill text-primary"></i> Enhanced Donation System
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    Access the donation system to create donations, send birthday gifts, or view the public donation form.
                </p>
                
                <div class="row">
                    <!-- Quick Actions -->
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-primary">
                            <div class="card-body text-center">
                                <i class="bi bi-cash-coin fs-1 text-primary mb-3"></i>
                                <h5 class="card-title">Manage Donations</h5>
                                <p class="card-text text-muted">View and manage all donations, update statuses, and track giving.</p>
                                <a href="donations.php" class="btn btn-primary">
                                    <i class="bi bi-arrow-right"></i> Go to Donations
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-warning">
                            <div class="card-body text-center">
                                <i class="bi bi-gift-fill fs-1 text-warning mb-3"></i>
                                <h5 class="card-title">Gift Management</h5>
                                <p class="card-text text-muted">Send birthday gifts, manage gift delivery, and coordinate special occasions.</p>
                                <a href="gift_management.php" class="btn btn-warning">
                                    <i class="bi bi-arrow-right"></i> Manage Gifts
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-success">
                            <div class="card-body text-center">
                                <i class="bi bi-gear-fill fs-1 text-success mb-3"></i>
                                <h5 class="card-title">System Setup</h5>
                                <p class="card-text text-muted">Configure donation settings, payment methods, and system preferences.</p>
                                <a href="setup_enhanced_donations.php" class="btn btn-success">
                                    <i class="bi bi-arrow-right"></i> Setup System
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-info">
                            <div class="card-body text-center">
                                <i class="bi bi-box-arrow-up-right fs-1 text-info mb-3"></i>
                                <h5 class="card-title">Public Donation Form</h5>
                                <p class="card-text text-muted">View the public-facing donation form that members and visitors use.</p>
                                <a href="../enhanced_donate.php" target="_blank" class="btn btn-info">
                                    <i class="bi bi-external-link"></i> View Public Form
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Donations</h6>
                        <?php
                        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM donations WHERE payment_status != 'cancelled'");
                        $stmt->execute();
                        $total_donations = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <h3 class="mb-0"><?php echo number_format($total_donations); ?></h3>
                    </div>
                    <i class="bi bi-cash-coin fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">This Month</h6>
                        <?php
                        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM donations WHERE MONTH(created_at) = MONTH(CURRENT_DATE) AND YEAR(created_at) = YEAR(CURRENT_DATE) AND payment_status != 'cancelled'");
                        $stmt->execute();
                        $monthly_donations = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <h3 class="mb-0"><?php echo number_format($monthly_donations); ?></h3>
                    </div>
                    <i class="bi bi-calendar-month fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Pending Gifts</h6>
                        <?php
                        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM donations WHERE donation_type = 'birthday_gift' AND delivery_status = 'pending'");
                        $stmt->execute();
                        $pending_gifts = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <h3 class="mb-0"><?php echo number_format($pending_gifts); ?></h3>
                    </div>
                    <i class="bi bi-gift fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Amount</h6>
                        <?php
                        $stmt = $pdo->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM donations WHERE payment_status = 'completed'");
                        $stmt->execute();
                        $total_amount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
                        ?>
                        <h3 class="mb-0">$<?php echo number_format($total_amount, 2); ?></h3>
                    </div>
                    <i class="bi bi-currency-dollar fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Recent Donation Activity</h5>
                <a href="donations.php" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php
                $stmt = $pdo->prepare("
                    SELECT d.*, m.full_name as recipient_name
                    FROM donations d
                    LEFT JOIN members m ON d.recipient_id = m.id
                    ORDER BY d.created_at DESC
                    LIMIT 5
                ");
                $stmt->execute();
                $recent_donations = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (empty($recent_donations)): ?>
                    <p class="text-muted text-center py-4">No recent donation activity.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Donor</th>
                                    <th>Type</th>
                                    <th>Recipient</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_donations as $donation): ?>
                                <tr>
                                    <td>
                                        <?php echo htmlspecialchars($donation['donor_name'] ?: 'Anonymous'); ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $donation['donation_type'] === 'birthday_gift' ? 'warning' : 'primary'; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $donation['donation_type'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        if ($donation['donation_type'] === 'birthday_gift' && $donation['recipient_name']) {
                                            echo htmlspecialchars($donation['recipient_name']);
                                        } else {
                                            echo '<span class="text-muted">-</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>$<?php echo number_format($donation['amount'], 2); ?></td>
                                    <td>
                                        <span class="badge bg-<?php
                                            echo $donation['payment_status'] === 'completed' ? 'success' :
                                                ($donation['payment_status'] === 'pending' ? 'warning' : 'danger');
                                        ?>">
                                            <?php echo ucfirst($donation['payment_status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('M j, Y', strtotime($donation['created_at'])); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
