Abstract class
-----
<?php

abstract class A {
    public function a() {}
    abstract public function b();
}
-----
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: ABSTRACT (16)
        name: Identifier(
            name: A
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_ClassMethod(
                attrGroups: array(
                )
                flags: PUBLIC (1)
                byRef: false
                name: Identifier(
                    name: a
                )
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
            1: Stmt_ClassMethod(
                attrGroups: array(
                )
                flags: PUBLIC | ABSTRACT (17)
                byRef: false
                name: Identifier(
                    name: b
                )
                params: array(
                )
                returnType: null
                stmts: null
            )
        )
    )
)
