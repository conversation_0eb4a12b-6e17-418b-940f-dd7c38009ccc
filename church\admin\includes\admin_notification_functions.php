<?php
/**
 * Admin Notification Functions
 * Adapted from user notification system for admin use
 */

/**
 * Get unread notification count for admin
 */
function getAdminUnreadNotificationCount($pdo, $adminId) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM admin_notifications 
            WHERE recipient_id = ? 
            AND is_read = 0 
            AND (expires_at IS NULL OR expires_at > NOW())
        ");
        $stmt->execute([$adminId]);
        return (int)$stmt->fetchColumn();
    } catch (Exception $e) {
        error_log("Error getting admin unread notification count: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get admin notifications with pagination
 */
function getAdminNotifications($pdo, $adminId, $limit = 20, $offset = 0, $unreadOnly = false) {
    try {
        $whereClause = "WHERE an.recipient_id = ? AND (an.expires_at IS NULL OR an.expires_at > NOW())";
        if ($unreadOnly) {
            $whereClause .= " AND an.is_read = 0";
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                an.*,
                CASE 
                    WHEN an.sender_type = 'admin' THEN COALESCE(a.full_name, a.username, 'Administrator')
                    WHEN an.sender_type = 'member' THEN COALESCE(m.full_name, CONCAT(m.first_name, ' ', m.last_name), 'Member')
                    ELSE 'System'
                END as sender_name
            FROM admin_notifications an
            LEFT JOIN admins a ON an.sender_type = 'admin' AND an.sender_id = a.id
            LEFT JOIN members m ON an.sender_type = 'member' AND an.sender_id = m.id
            $whereClause
            ORDER BY an.created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $params = [$adminId, $limit, $offset];
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting admin notifications: " . $e->getMessage());
        return [];
    }
}

/**
 * Create a new admin notification
 */
function createAdminNotification($pdo, $recipientId, $title, $message, $type = 'system', $senderId = null, $senderType = 'admin', $actionUrl = null, $priority = 'normal', $expiresAt = null) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO admin_notifications 
            (recipient_id, sender_id, sender_type, notification_type, title, message, action_url, priority, expires_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $recipientId, $senderId, $senderType, $type, $title, $message, $actionUrl, $priority, $expiresAt
        ]);
    } catch (Exception $e) {
        error_log("Error creating admin notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark admin notification as read
 */
function markAdminNotificationAsRead($pdo, $notificationId, $adminId) {
    try {
        $stmt = $pdo->prepare("
            UPDATE admin_notifications 
            SET is_read = 1, read_at = NOW() 
            WHERE id = ? AND recipient_id = ?
        ");
        return $stmt->execute([$notificationId, $adminId]);
    } catch (Exception $e) {
        error_log("Error marking admin notification as read: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark all admin notifications as read
 */
function markAllAdminNotificationsAsRead($pdo, $adminId) {
    try {
        $stmt = $pdo->prepare("
            UPDATE admin_notifications 
            SET is_read = 1, read_at = NOW() 
            WHERE recipient_id = ? AND is_read = 0
        ");
        return $stmt->execute([$adminId]);
    } catch (Exception $e) {
        error_log("Error marking all admin notifications as read: " . $e->getMessage());
        return false;
    }
}

/**
 * Send notification to multiple admins
 */
function sendBulkAdminNotification($pdo, $recipientIds, $title, $message, $type = 'announcement', $senderId = null, $senderType = 'admin', $actionUrl = null, $priority = 'normal') {
    $successCount = 0;
    $errors = [];
    
    foreach ($recipientIds as $recipientId) {
        if (createAdminNotification($pdo, $recipientId, $title, $message, $type, $senderId, $senderType, $actionUrl, $priority)) {
            $successCount++;
        } else {
            $errors[] = "Failed to send notification to admin ID: $recipientId";
        }
    }
    
    return [
        'success_count' => $successCount,
        'total_count' => count($recipientIds),
        'errors' => $errors
    ];
}

/**
 * Get admin notification icon based on type
 */
function getAdminNotificationIcon($type) {
    $icons = [
        'announcement' => 'bi-megaphone',
        'message' => 'bi-chat-dots',
        'member_activity' => 'bi-person-check',
        'system' => 'bi-gear',
        'security' => 'bi-shield-exclamation',
        'donation' => 'bi-heart',
        'event' => 'bi-calendar-event',
        'email' => 'bi-envelope',
        'error' => 'bi-exclamation-triangle'
    ];
    
    return $icons[$type] ?? 'bi-bell';
}

/**
 * Get admin notification color class based on priority
 */
function getAdminNotificationColorClass($priority) {
    $colors = [
        'low' => 'text-muted',
        'normal' => 'text-primary',
        'high' => 'text-warning',
        'urgent' => 'text-danger'
    ];
    
    return $colors[$priority] ?? 'text-primary';
}

/**
 * Format notification time for display
 */
function formatAdminNotificationTime($timestamp) {
    $time = strtotime($timestamp);
    $now = time();
    $diff = $now - $time;
    
    if ($diff < 60) {
        return 'Just now';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } else {
        return date('M j, Y g:i A', $time);
    }
}

/**
 * Delete old admin notifications
 */
function deleteOldAdminNotifications($pdo, $daysOld = 30) {
    try {
        $stmt = $pdo->prepare("
            DELETE FROM admin_notifications
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            OR (expires_at IS NOT NULL AND expires_at < NOW())
        ");
        $stmt->execute([$daysOld]);
        return $stmt->rowCount();
    } catch (Exception $e) {
        error_log("Error deleting old admin notifications: " . $e->getMessage());
        return 0;
    }
}

/**
 * Create notification for new member registration
 */
function notifyAdminsNewMember($pdo, $memberName, $memberEmail, $memberId) {
    try {
        // Get all admin IDs
        $stmt = $pdo->query("SELECT id FROM admins");
        $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $title = "New Member Registration";
        $message = "A new member has registered: $memberName ($memberEmail)";
        $actionUrl = "view_member.php?id=$memberId";

        return sendBulkAdminNotification($pdo, $adminIds, $title, $message, 'member_activity', null, 'system', $actionUrl, 'normal');
    } catch (Exception $e) {
        error_log("Error notifying admins of new member: " . $e->getMessage());
        return false;
    }
}

/**
 * Create notification for member deletion
 */
function notifyAdminsMemberDeleted($pdo, $memberName, $deletedBy) {
    try {
        // Get all admin IDs except the one who deleted
        $stmt = $pdo->prepare("SELECT id FROM admins WHERE id != ?");
        $stmt->execute([$deletedBy]);
        $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (!empty($adminIds)) {
            $title = "Member Deleted";
            $message = "Member '$memberName' has been deleted from the system.";

            return sendBulkAdminNotification($pdo, $adminIds, $title, $message, 'member_activity', $deletedBy, 'admin', null, 'normal');
        }
        return true;
    } catch (Exception $e) {
        error_log("Error notifying admins of member deletion: " . $e->getMessage());
        return false;
    }
}

/**
 * Create notification for system errors
 */
function notifyAdminsSystemError($pdo, $errorMessage, $context = '') {
    try {
        // Get all admin IDs
        $stmt = $pdo->query("SELECT id FROM admins");
        $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $title = "System Error Detected";
        $message = "An error occurred in the system" . ($context ? " ($context)" : '') . ": $errorMessage";

        return sendBulkAdminNotification($pdo, $adminIds, $title, $message, 'error', null, 'system', null, 'high');
    } catch (Exception $e) {
        error_log("Error notifying admins of system error: " . $e->getMessage());
        return false;
    }
}

/**
 * Create notification for bulk email completion
 */
function notifyAdminsBulkEmailComplete($pdo, $adminId, $successCount, $totalCount, $emailType) {
    try {
        $title = "Bulk Email Campaign Completed";
        $message = "Your $emailType email campaign has completed. $successCount out of $totalCount emails were sent successfully.";
        $actionUrl = "email_analytics.php";

        return createAdminNotification($pdo, $adminId, $title, $message, 'email', null, 'system', $actionUrl, 'normal');
    } catch (Exception $e) {
        error_log("Error notifying admin of bulk email completion: " . $e->getMessage());
        return false;
    }
}
?>
