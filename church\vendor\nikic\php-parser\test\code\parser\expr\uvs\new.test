UVS new expressions
-----
<?php
new $className;
new $array['className'];
new $obj->className;
new Test::$className;
new $test::$className;
new $weird[0]->foo::$className;
-----
array(
    0: Stmt_Expression(
        expr: Expr_New(
            class: Expr_Variable(
                name: className
            )
            args: array(
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_New(
            class: Expr_ArrayDimFetch(
                var: Expr_Variable(
                    name: array
                )
                dim: Scalar_String(
                    value: className
                )
            )
            args: array(
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_New(
            class: Expr_PropertyFetch(
                var: Expr_Variable(
                    name: obj
                )
                name: Identifier(
                    name: className
                )
            )
            args: array(
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_New(
            class: Expr_StaticPropertyFetch(
                class: Name(
                    name: Test
                )
                name: VarLikeIdentifier(
                    name: className
                )
            )
            args: array(
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_New(
            class: Expr_StaticPropertyFetch(
                class: Expr_Variable(
                    name: test
                )
                name: VarLikeIdentifier(
                    name: className
                )
            )
            args: array(
            )
        )
    )
    5: Stmt_Expression(
        expr: Expr_New(
            class: Expr_StaticPropertyFetch(
                class: Expr_PropertyFetch(
                    var: Expr_ArrayDimFetch(
                        var: Expr_Variable(
                            name: weird
                        )
                        dim: Scalar_Int(
                            value: 0
                        )
                    )
                    name: Identifier(
                        name: foo
                    )
                )
                name: VarLikeIdentifier(
                    name: className
                )
            )
            args: array(
            )
        )
    )
)
