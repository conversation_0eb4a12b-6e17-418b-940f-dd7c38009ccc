<?php
/**
 * Change Password Page
 * 
 * Allows authenticated users to change their password
 * Also handles forced password changes for users with temporary passwords
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

$mustChangePassword = $userData['must_change_password'] ?? false;
$hasTempPassword = $userData['temp_password'] ?? false;

// Process password change form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validate passwords
        if (!$hasTempPassword && empty($currentPassword)) {
            $error = "Please enter your current password.";
        } elseif (empty($newPassword) || empty($confirmPassword)) {
            $error = "Please fill in all password fields.";
        } elseif ($newPassword !== $confirmPassword) {
            $error = "New passwords do not match.";
        } elseif (!$security->validateInput($newPassword, 'password')) {
            $error = "New password does not meet security requirements.";
        } else {
            // Change password
            $result = $userAuth->changePassword($userId, $currentPassword, $newPassword);
            
            if ($result['success']) {
                $success = "Your password has been changed successfully.";
                
                // If this was a forced password change, redirect to dashboard
                if ($mustChangePassword) {
                    $_SESSION['password_changed'] = true;
                    header("Location: dashboard.php?password_changed=1");
                    exit();
                }
            } else {
                $error = $result['message'];
            }
        }
    }
}

// Get site settings for branding
$sitename = get_organization_name() . ' - Change Password';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Password - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }
        
        .change-password-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 2.5rem;
            width: 100%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .change-password-logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .change-password-logo h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .change-password-logo p {
            color: #7f8c8d;
            font-size: 1rem;
            margin: 0;
        }
        
        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background-color: #fee;
            color: #c33;
        }
        
        .alert-success {
            background-color: #efe;
            color: #363;
        }
        
        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .change-password-links {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .change-password-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .change-password-links a:hover {
            color: #764ba2;
        }
        
        .input-group-text {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-right: none;
            border-radius: 12px 0 0 12px;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }
        
        .input-group:focus-within .input-group-text {
            border-color: #667eea;
        }
        
        .password-requirements {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }
        
        .password-requirements ul {
            margin: 0;
            padding-left: 1.2rem;
        }
        
        .password-requirements li {
            margin-bottom: 0.25rem;
        }
        
        .user-info {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .user-info h6 {
            color: #1976d2;
            margin-bottom: 0.5rem;
        }
        
        .user-info p {
            color: #1565c0;
            margin: 0;
            font-size: 0.9rem;
        }
        
        .forced-change-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .forced-change-notice h6 {
            color: #856404;
            margin-bottom: 0.5rem;
        }
        
        .forced-change-notice p {
            color: #856404;
            margin: 0;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="change-password-container">
        <div class="change-password-logo">
            <h1><i class="bi bi-shield-lock"></i> Change Password</h1>
            <p><?php echo htmlspecialchars($sitename); ?></p>
        </div>
        
        <?php if ($mustChangePassword): ?>
            <div class="forced-change-notice">
                <h6><i class="bi bi-exclamation-triangle"></i> Password Change Required</h6>
                <p>For security reasons, you must change your password before continuing. This is a one-time requirement.</p>
            </div>
        <?php endif; ?>
        
        <div class="user-info">
            <h6><i class="bi bi-person-circle"></i> Account Information</h6>
            <p><strong><?php echo htmlspecialchars($userData['full_name']); ?></strong></p>
            <p><?php echo htmlspecialchars($userData['email']); ?></p>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                <?php if (!$mustChangePassword): ?>
                    <div class="mt-3">
                        <a href="dashboard.php" class="btn btn-success btn-sm">
                            <i class="bi bi-house"></i> Go to Dashboard
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                <?php echo $security->generateCSRFInput(); ?>
                
                <?php if (!$hasTempPassword): ?>
                    <div class="mb-3">
                        <label for="current_password" class="form-label">
                            <i class="bi bi-lock"></i> Current Password
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-key"></i>
                            </span>
                            <input type="password" 
                                   class="form-control" 
                                   id="current_password" 
                                   name="current_password" 
                                   placeholder="Enter your current password"
                                   required 
                                   autocomplete="current-password">
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="mb-3">
                    <label for="new_password" class="form-label">
                        <i class="bi bi-shield-lock"></i> New Password
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-key-fill"></i>
                        </span>
                        <input type="password" 
                               class="form-control" 
                               id="new_password" 
                               name="new_password" 
                               placeholder="Enter your new password"
                               required 
                               autocomplete="new-password">
                    </div>
                    <div class="password-requirements">
                        <strong>Password Requirements:</strong>
                        <ul>
                            <li>At least 8 characters long</li>
                            <li>Contains uppercase and lowercase letters</li>
                            <li>Contains at least one number</li>
                            <li>Contains at least one special character</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="confirm_password" class="form-label">
                        <i class="bi bi-shield-check"></i> Confirm New Password
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-key-fill"></i>
                        </span>
                        <input type="password" 
                               class="form-control" 
                               id="confirm_password" 
                               name="confirm_password" 
                               placeholder="Confirm your new password"
                               required 
                               autocomplete="new-password">
                    </div>
                </div>
                
                <div class="d-grid gap-2 mb-3">
                    <button type="submit" name="change_password" class="btn btn-primary">
                        <i class="bi bi-shield-check"></i> Change Password
                    </button>
                </div>
            </form>
        <?php endif; ?>
        
        <?php if (!$mustChangePassword): ?>
            <div class="change-password-links">
                <div class="mb-2">
                    <a href="dashboard.php">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
                <div>
                    <a href="profile.php">
                        <i class="bi bi-person"></i> Edit Profile
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password confirmation validation
        const newPasswordField = document.getElementById('new_password');
        const confirmPasswordField = document.getElementById('confirm_password');
        
        function validatePasswordMatch() {
            if (newPasswordField.value !== confirmPasswordField.value) {
                confirmPasswordField.setCustomValidity('Passwords do not match');
            } else {
                confirmPasswordField.setCustomValidity('');
            }
        }
        
        if (newPasswordField && confirmPasswordField) {
            newPasswordField.addEventListener('input', validatePasswordMatch);
            confirmPasswordField.addEventListener('input', validatePasswordMatch);
        }
        
        // Form submission validation
        const changeForm = document.querySelector('form');
        if (changeForm) {
            changeForm.addEventListener('submit', function(event) {
                if (newPasswordField.value !== confirmPasswordField.value) {
                    event.preventDefault();
                    alert('Passwords do not match. Please check and try again.');
                    return false;
                }
            });
        }
        
        // Auto-focus on appropriate field
        <?php if ($hasTempPassword): ?>
            document.getElementById('new_password').focus();
        <?php else: ?>
            document.getElementById('current_password').focus();
        <?php endif; ?>
    </script>
</body>
</html>
