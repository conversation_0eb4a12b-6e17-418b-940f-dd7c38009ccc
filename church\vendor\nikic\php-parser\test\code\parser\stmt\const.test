Global constants
-----
<?php

const A = 0, B = 1.0, C = 'A', D = E;
-----
array(
    0: Stmt_Const(
        consts: array(
            0: Const(
                name: Identifier(
                    name: A
                )
                value: <PERSON>alar_Int(
                    value: 0
                )
            )
            1: Const(
                name: Identifier(
                    name: B
                )
                value: Scalar_Float(
                    value: 1
                )
            )
            2: Const(
                name: Identifier(
                    name: C
                )
                value: Scalar_String(
                    value: A
                )
            )
            3: Const(
                name: Identifier(
                    name: D
                )
                value: Expr_ConstFetch(
                    name: Name(
                        name: E
                    )
                )
            )
        )
    )
)
