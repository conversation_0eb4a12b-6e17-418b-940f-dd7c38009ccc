<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['delete_relationship'])) {
            // Delete family relationship
            $stmt = $pdo->prepare("DELETE FROM family_relationships WHERE id = ?");
            $stmt->execute([$_POST['relationship_id']]);
            
            $message = "Family relationship deleted successfully!";
        }
        
        if (isset($_POST['update_permissions'])) {
            // Update relationship permissions
            $stmt = $pdo->prepare("
                UPDATE family_relationships 
                SET can_manage_profile = ?, can_rsvp_for_member = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                isset($_POST['can_manage_profile']) ? 1 : 0,
                isset($_POST['can_rsvp_for_member']) ? 1 : 0,
                $_POST['relationship_id']
            ]);
            
            $message = "Relationship permissions updated successfully!";
        }
        
    } catch (PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get all family relationships
try {
    $stmt = $pdo->prepare("
        SELECT fr.*, 
               m1.full_name as primary_member_name, m1.email as primary_member_email,
               m2.full_name as related_member_name, m2.email as related_member_email
        FROM family_relationships fr
        JOIN members m1 ON fr.member_id = m1.id
        JOIN members m2 ON fr.related_member_id = m2.id
        ORDER BY m1.full_name, fr.relationship_type
    ");
    $stmt->execute();
    $relationships = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $relationships = [];
    $error = "Error loading family relationships: " . $e->getMessage();
}

// Get statistics
try {
    $stats = [];
    
    // Total relationships
    $stmt = $pdo->query("SELECT COUNT(*) FROM family_relationships");
    $stats['total'] = $stmt->fetchColumn();
    
    // Unique families (count of distinct primary members)
    $stmt = $pdo->query("SELECT COUNT(DISTINCT member_id) FROM family_relationships");
    $stats['families'] = $stmt->fetchColumn();
    
    // Members with family links
    $stmt = $pdo->query("
        SELECT COUNT(DISTINCT m.id) 
        FROM members m 
        WHERE m.id IN (
            SELECT member_id FROM family_relationships 
            UNION 
            SELECT related_member_id FROM family_relationships
        )
    ");
    $stats['linked_members'] = $stmt->fetchColumn();
    
    // Total members
    $stmt = $pdo->query("SELECT COUNT(*) FROM members WHERE status = 'active'");
    $stats['total_members'] = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $stats = ['total' => 0, 'families' => 0, 'linked_members' => 0, 'total_members' => 0];
}

// Set page variables
$page_title = 'Family Management';
$page_header = 'Family Relationships Management';
$page_description = 'View and manage member family relationships';

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-people"></i> Family Relationships Management</h2>
                    <p class="text-muted">View and manage member family relationships and permissions</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['families']; ?></h3>
                                    <small>Family Units</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-person-hearts fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['total']; ?></h3>
                                    <small>Total Relationships</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-person-check fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['linked_members']; ?></h3>
                                    <small>Linked Members</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-person-plus fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo $stats['total_members'] - $stats['linked_members']; ?></h3>
                                    <small>Unlinked Members</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Family Relationships List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-list"></i> Family Relationships (<?php echo count($relationships); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($relationships)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                            <h4 class="mt-3 text-muted">No Family Relationships Found</h4>
                            <p class="text-muted">Members haven't created any family relationships yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Primary Member</th>
                                        <th>Relationship</th>
                                        <th>Related Member</th>
                                        <th>Permissions</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($relationships as $rel): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($rel['primary_member_name']); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($rel['primary_member_email']); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo ucfirst($rel['relationship_type']); ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($rel['related_member_name']); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($rel['related_member_email']); ?></small>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column gap-1">
                                                <?php if ($rel['can_manage_profile']): ?>
                                                    <span class="badge bg-success">Can Manage Profile</span>
                                                <?php endif; ?>
                                                <?php if ($rel['can_rsvp_for_member']): ?>
                                                    <span class="badge bg-info">Can RSVP</span>
                                                <?php endif; ?>
                                                <?php if (!$rel['can_manage_profile'] && !$rel['can_rsvp_for_member']): ?>
                                                    <span class="badge bg-secondary">No Permissions</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php echo date('M j, Y', strtotime($rel['created_at'])); ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editPermissions(<?php echo $rel['id']; ?>, <?php echo $rel['can_manage_profile']; ?>, <?php echo $rel['can_rsvp_for_member']; ?>)">
                                                    <i class="bi bi-gear"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteRelationship(<?php echo $rel['id']; ?>, '<?php echo htmlspecialchars($rel['primary_member_name']); ?>', '<?php echo htmlspecialchars($rel['related_member_name']); ?>')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Permissions Modal -->
<div class="modal fade" id="editPermissionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Relationship Permissions</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="relationship_id" id="edit_relationship_id">

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" name="can_manage_profile" id="edit_can_manage_profile">
                        <label class="form-check-label" for="edit_can_manage_profile">
                            Can Manage Profile
                        </label>
                        <small class="form-text text-muted d-block">Allow this person to edit the related member's profile</small>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" name="can_rsvp_for_member" id="edit_can_rsvp_for_member">
                        <label class="form-check-label" for="edit_can_rsvp_for_member">
                            Can RSVP for Member
                        </label>
                        <small class="form-text text-muted d-block">Allow this person to RSVP to events on behalf of the related member</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_permissions" class="btn btn-primary">Update Permissions</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editPermissions(relationshipId, canManageProfile, canRsvp) {
    document.getElementById('edit_relationship_id').value = relationshipId;
    document.getElementById('edit_can_manage_profile').checked = canManageProfile == 1;
    document.getElementById('edit_can_rsvp_for_member').checked = canRsvp == 1;

    const modal = new bootstrap.Modal(document.getElementById('editPermissionsModal'));
    modal.show();
}

function deleteRelationship(relationshipId, primaryName, relatedName) {
    if (confirm(`Are you sure you want to delete the family relationship between "${primaryName}" and "${relatedName}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="relationship_id" value="${relationshipId}">
            <input type="hidden" name="delete_relationship" value="1">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
