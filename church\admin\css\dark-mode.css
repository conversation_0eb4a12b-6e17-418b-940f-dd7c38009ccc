/* Dark Mode Theme System */
/* Extends existing theme system with dark mode support */

/* Dark Mode CSS Variables */
[data-theme="dark"] {
  /* Bootstrap Dark Mode Variables */
  --bs-body-bg: #1a1a1a;
  --bs-body-color: #e9ecef;
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-danger: #dc3545;
  --bs-warning: #ffc107;
  --bs-info: #0dcaf0;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  
  /* Custom Dark Mode Variables */
  --bs-link-color: #6ea8fe;
  --bs-link-hover-color: #9ec5fe;
  --bs-border-color: #495057;
  --bs-gray-100: #2d3748;
  --bs-gray-200: #4a5568;
  --bs-gray-300: #718096;
  --bs-gray-400: #a0aec0;
  --bs-gray-500: #cbd5e0;
  --bs-gray-600: #e2e8f0;
  --bs-gray-700: #edf2f7;
  --bs-gray-800: #f7fafc;
  --bs-gray-900: #ffffff;
  
  /* Sidebar Dark Mode */
  --sidebar-bg-color: #2d3748;
  --sidebar-text-color: #e2e8f0;
  --sidebar-hover-color: #4299e1;
  --sidebar-active-color: #3182ce;
  --sidebar-border-color: #4a5568;
  
  /* Card and Component Dark Mode */
  --card-bg: #2d3748;
  --card-border: #4a5568;
  --input-bg: #4a5568;
  --input-border: #718096;
  --input-color: #e2e8f0;
  --input-placeholder: #a0aec0;
  
  /* Table Dark Mode */
  --table-bg: #2d3748;
  --table-border: #4a5568;
  --table-hover-bg: #4a5568;
  --table-stripe-bg: #374151;
  
  /* Modal Dark Mode */
  --modal-bg: #2d3748;
  --modal-header-bg: #374151;
  --modal-border: #4a5568;
  
  /* Button Dark Mode */
  --btn-outline-border: #4a5568;
  --btn-outline-color: #e2e8f0;
  --btn-outline-hover-bg: #4a5568;
  
  /* Alert Dark Mode */
  --alert-primary-bg: rgba(13, 110, 253, 0.1);
  --alert-primary-border: rgba(13, 110, 253, 0.2);
  --alert-success-bg: rgba(25, 135, 84, 0.1);
  --alert-success-border: rgba(25, 135, 84, 0.2);
  --alert-warning-bg: rgba(255, 193, 7, 0.1);
  --alert-warning-border: rgba(255, 193, 7, 0.2);
  --alert-danger-bg: rgba(220, 53, 69, 0.1);
  --alert-danger-border: rgba(220, 53, 69, 0.2);
}

/* Dark Mode Component Styles */
[data-theme="dark"] body {
  background-color: var(--bs-body-bg) !important;
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .card {
  background-color: var(--card-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .card-header {
  background-color: var(--modal-header-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .card-body {
  background-color: var(--card-bg) !important;
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .card-footer {
  background-color: var(--modal-header-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-color);
}

[data-theme="dark"] .form-control::placeholder {
  color: var(--input-placeholder);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
  background-color: var(--input-bg);
  border-color: var(--bs-primary);
  color: var(--input-color);
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

[data-theme="dark"] .table {
  --bs-table-bg: var(--table-bg);
  --bs-table-border-color: var(--table-border);
  --bs-table-hover-bg: var(--table-hover-bg);
  --bs-table-striped-bg: var(--table-stripe-bg);
  color: var(--bs-body-color);
}

[data-theme="dark"] .table th {
  border-color: var(--table-border);
  background-color: var(--table-stripe-bg);
}

[data-theme="dark"] .modal-content {
  background-color: var(--modal-bg);
  border-color: var(--modal-border);
}

[data-theme="dark"] .modal-header {
  background-color: var(--modal-header-bg);
  border-color: var(--modal-border);
}

[data-theme="dark"] .modal-footer {
  border-color: var(--modal-border);
}

[data-theme="dark"] .btn-outline-secondary {
  color: var(--btn-outline-color);
  border-color: var(--btn-outline-border);
}

[data-theme="dark"] .btn-outline-secondary:hover {
  background-color: var(--btn-outline-hover-bg);
  border-color: var(--btn-outline-border);
}

[data-theme="dark"] .alert-primary {
  background-color: var(--alert-primary-bg);
  border-color: var(--alert-primary-border);
  color: var(--bs-primary);
}

[data-theme="dark"] .alert-success {
  background-color: var(--alert-success-bg);
  border-color: var(--alert-success-border);
  color: var(--bs-success);
}

[data-theme="dark"] .alert-warning {
  background-color: var(--alert-warning-bg);
  border-color: var(--alert-warning-border);
  color: #856404;
}

[data-theme="dark"] .alert-danger {
  background-color: var(--alert-danger-bg);
  border-color: var(--alert-danger-border);
  color: var(--bs-danger);
}

/* Sidebar Dark Mode Specific */
[data-theme="dark"] .sidebar {
  background-color: var(--sidebar-bg-color) !important;
  border-color: var(--sidebar-border-color);
}

[data-theme="dark"] .sidebar .nav-link {
  color: var(--sidebar-text-color) !important;
}

[data-theme="dark"] .sidebar .nav-link:hover {
  background-color: var(--sidebar-hover-color) !important;
  color: white !important;
}

[data-theme="dark"] .sidebar .nav-link.active {
  background-color: var(--sidebar-active-color) !important;
  color: white !important;
}

[data-theme="dark"] .sidebar-heading {
  color: var(--sidebar-text-color) !important;
}

/* Dropdown Dark Mode */
[data-theme="dark"] .dropdown-menu {
  background-color: var(--card-bg);
  border-color: var(--card-border);
}

[data-theme="dark"] .dropdown-item {
  color: var(--bs-body-color);
}

[data-theme="dark"] .dropdown-item:hover {
  background-color: var(--table-hover-bg);
  color: var(--bs-body-color);
}

/* Text and Typography Dark Mode */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] p,
[data-theme="dark"] span,
[data-theme="dark"] div,
[data-theme="dark"] label,
[data-theme="dark"] small,
[data-theme="dark"] .text-muted {
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .text-muted {
  color: var(--bs-gray-400) !important;
}

/* Badge Dark Mode */
[data-theme="dark"] .badge {
  color: white !important;
}

[data-theme="dark"] .badge.bg-secondary {
  background-color: var(--bs-gray-600) !important;
}

/* List Group Dark Mode */
[data-theme="dark"] .list-group-item {
  background-color: var(--card-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .list-group-item:hover {
  background-color: var(--table-hover-bg) !important;
}

/* Breadcrumb Dark Mode */
[data-theme="dark"] .breadcrumb {
  background-color: var(--card-bg) !important;
}

[data-theme="dark"] .breadcrumb-item a {
  color: var(--bs-link-color) !important;
}

[data-theme="dark"] .breadcrumb-item.active {
  color: var(--bs-body-color) !important;
}

/* Statistics Cards Dark Mode */
[data-theme="dark"] .stat-card {
  background-color: var(--card-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .stat-card .stat-number {
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .stat-card .stat-label {
  color: var(--bs-gray-400) !important;
}

/* Dashboard Quick Links Dark Mode */
[data-theme="dark"] .quick-link-card {
  background-color: var(--card-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .quick-link-card:hover {
  background-color: var(--table-hover-bg) !important;
  transform: translateY(-2px);
}

/* Progress Bar Dark Mode */
[data-theme="dark"] .progress {
  background-color: var(--bs-gray-200) !important;
}

/* Pagination Dark Mode */
[data-theme="dark"] .page-link {
  background-color: var(--card-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .page-link:hover {
  background-color: var(--table-hover-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .page-item.active .page-link {
  background-color: var(--bs-primary) !important;
  border-color: var(--bs-primary) !important;
}

/* Navbar Dark Mode */
[data-theme="dark"] .navbar {
  background-color: var(--sidebar-bg-color) !important;
}

[data-theme="dark"] .navbar-brand,
[data-theme="dark"] .navbar-nav .nav-link {
  color: var(--sidebar-text-color) !important;
}

/* Tab Dark Mode */
[data-theme="dark"] .nav-tabs .nav-link {
  background-color: var(--card-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--bs-body-color) !important;
}

[data-theme="dark"] .nav-tabs .nav-link.active {
  background-color: var(--bs-body-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--bs-body-color) !important;
}

/* Pagination Dark Mode */
[data-theme="dark"] .page-link {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  color: var(--bs-link-color);
}

[data-theme="dark"] .page-link:hover {
  background-color: var(--table-hover-bg);
  border-color: var(--card-border);
  color: var(--bs-link-hover-color);
}

[data-theme="dark"] .page-item.active .page-link {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

/* Badge Dark Mode */
[data-theme="dark"] .badge {
  color: white;
}

/* Breadcrumb Dark Mode */
[data-theme="dark"] .breadcrumb {
  background-color: var(--card-bg);
}

[data-theme="dark"] .breadcrumb-item a {
  color: var(--bs-link-color);
}

/* List Group Dark Mode */
[data-theme="dark"] .list-group-item {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  color: var(--bs-body-color);
}

[data-theme="dark"] .list-group-item:hover {
  background-color: var(--table-hover-bg);
}

/* Progress Dark Mode */
[data-theme="dark"] .progress {
  background-color: var(--table-stripe-bg);
}

/* Navbar Dark Mode */
[data-theme="dark"] .navbar-light {
  background-color: var(--card-bg) !important;
  border-color: var(--card-border);
}

[data-theme="dark"] .navbar-light .navbar-brand,
[data-theme="dark"] .navbar-light .navbar-nav .nav-link {
  color: var(--bs-body-color) !important;
}

/* Dark Mode Toggle Button Styles */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1050;
  background: var(--bs-primary);
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.theme-toggle i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

[data-theme="dark"] .theme-toggle {
  background: #ffc107;
  color: #212529;
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  [data-theme="dark"] {
    --bs-body-bg: #000000;
    --bs-body-color: #ffffff;
    --card-bg: #1a1a1a;
    --sidebar-bg-color: #000000;
    --sidebar-text-color: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
  }
  
  .theme-toggle:hover {
    transform: none;
  }
}
