Uniform variable syntax in PHP 7 (misc)
-----
<?php

"string"->length();
"foo$bar"[0];
"foo$bar"->length();
(clone $obj)->b[0](1);
[0, 1][0] = 1;
-----
array(
    0: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Scalar_String(
                value: string
            )
            name: Identifier(
                name: length
            )
            args: array(
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_ArrayDimFetch(
            var: Scalar_InterpolatedString(
                parts: array(
                    0: InterpolatedStringPart(
                        value: foo
                    )
                    1: Expr_Variable(
                        name: bar
                    )
                )
            )
            dim: Scalar_Int(
                value: 0
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Scalar_InterpolatedString(
                parts: array(
                    0: InterpolatedStringPart(
                        value: foo
                    )
                    1: Expr_Variable(
                        name: bar
                    )
                )
            )
            name: Identifier(
                name: length
            )
            args: array(
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_ArrayDimFetch(
                var: Expr_PropertyFetch(
                    var: Expr_Clone(
                        expr: Expr_Variable(
                            name: obj
                        )
                    )
                    name: Identifier(
                        name: b
                    )
                )
                dim: Scalar_Int(
                    value: 0
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Scalar_Int(
                        value: 1
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_ArrayDimFetch(
                var: Expr_Array(
                    items: array(
                        0: ArrayItem(
                            key: null
                            value: Scalar_Int(
                                value: 0
                            )
                            byRef: false
                            unpack: false
                        )
                        1: ArrayItem(
                            key: null
                            value: Scalar_Int(
                                value: 1
                            )
                            byRef: false
                            unpack: false
                        )
                    )
                )
                dim: Scalar_Int(
                    value: 0
                )
            )
            expr: Scalar_Int(
                value: 1
            )
        )
    )
)
