__halt_compiler
-----
<?php

$a;
__halt_compiler()
?>
Hallo World!
-----
array(
    0: Stmt_Expression(
        expr: Expr_Variable(
            name: a
        )
    )
    1: Stmt_HaltCompiler(
        remaining: Hallo World!
    )
)
-----
<?php

$a;
__halt_compiler();Hallo World!
-----
array(
    0: Stmt_Expression(
        expr: Expr_Variable(
            name: a
        )
    )
    1: Stmt_HaltCompiler(
        remaining: Hallo World!
    )
)
-----
<?php

namespace A;
$a;
__halt_compiler();
-----
array(
    0: Stmt_Namespace(
        name: Name(
            name: A
        )
        stmts: array(
            0: Stmt_Expression(
                expr: Expr_Variable(
                    name: a
                )
            )
        )
    )
    1: Stmt_HaltCompiler(
        remaining:
    )
)
