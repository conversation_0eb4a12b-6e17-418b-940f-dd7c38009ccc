Array definitions
-----
<?php

array();
array('a');
array('a', );
array('a', 'b');
array('a', &$b, 'c' => 'd', 'e' => &$f);

// short array syntax
[];
[1, 2, 3];
['a' => 'b'];
-----
array(
    0: Stmt_Expression(
        expr: Expr_Array(
            items: array(
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_Array(
            items: array(
                0: ArrayItem(
                    key: null
                    value: Scalar_String(
                        value: a
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_Array(
            items: array(
                0: ArrayItem(
                    key: null
                    value: Scalar_String(
                        value: a
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_Array(
            items: array(
                0: ArrayItem(
                    key: null
                    value: Scalar_String(
                        value: a
                    )
                    byRef: false
                    unpack: false
                )
                1: ArrayItem(
                    key: null
                    value: Scalar_String(
                        value: b
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_Array(
            items: array(
                0: ArrayItem(
                    key: null
                    value: Scalar_String(
                        value: a
                    )
                    byRef: false
                    unpack: false
                )
                1: ArrayItem(
                    key: null
                    value: Expr_Variable(
                        name: b
                    )
                    byRef: true
                    unpack: false
                )
                2: ArrayItem(
                    key: Scalar_String(
                        value: c
                    )
                    value: Scalar_String(
                        value: d
                    )
                    byRef: false
                    unpack: false
                )
                3: ArrayItem(
                    key: Scalar_String(
                        value: e
                    )
                    value: Expr_Variable(
                        name: f
                    )
                    byRef: true
                    unpack: false
                )
            )
        )
    )
    5: Stmt_Expression(
        expr: Expr_Array(
            items: array(
            )
        )
        comments: array(
            0: // short array syntax
        )
    )
    6: Stmt_Expression(
        expr: Expr_Array(
            items: array(
                0: ArrayItem(
                    key: null
                    value: Scalar_Int(
                        value: 1
                    )
                    byRef: false
                    unpack: false
                )
                1: ArrayItem(
                    key: null
                    value: Scalar_Int(
                        value: 2
                    )
                    byRef: false
                    unpack: false
                )
                2: ArrayItem(
                    key: null
                    value: Scalar_Int(
                        value: 3
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    7: Stmt_Expression(
        expr: Expr_Array(
            items: array(
                0: ArrayItem(
                    key: Scalar_String(
                        value: a
                    )
                    value: Scalar_String(
                        value: b
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
)
