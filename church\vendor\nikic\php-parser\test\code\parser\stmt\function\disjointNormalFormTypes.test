DNF types
-----
<?php

class Test {
    public (A&B)|(X&Y) $prop;
    public readonly (A&B)|C $prop2;
}

function test((A&B)|(X&Y) $a): (A&B)|(X&Y) {}
-----
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: Test
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_Property(
                attrGroups: array(
                )
                flags: PUBLIC (1)
                type: UnionType(
                    types: array(
                        0: IntersectionType(
                            types: array(
                                0: Name(
                                    name: A
                                )
                                1: Name(
                                    name: B
                                )
                            )
                        )
                        1: IntersectionType(
                            types: array(
                                0: Name(
                                    name: X
                                )
                                1: Name(
                                    name: Y
                                )
                            )
                        )
                    )
                )
                props: array(
                    0: PropertyItem(
                        name: VarLikeIdentifier(
                            name: prop
                        )
                        default: null
                    )
                )
                hooks: array(
                )
            )
            1: Stmt_Property(
                attrGroups: array(
                )
                flags: PUBLIC | READONLY (65)
                type: UnionType(
                    types: array(
                        0: IntersectionType(
                            types: array(
                                0: Name(
                                    name: A
                                )
                                1: Name(
                                    name: B
                                )
                            )
                        )
                        1: Name(
                            name: C
                        )
                    )
                )
                props: array(
                    0: PropertyItem(
                        name: VarLikeIdentifier(
                            name: prop2
                        )
                        default: null
                    )
                )
                hooks: array(
                )
            )
        )
    )
    1: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: test
        )
        params: array(
            0: Param(
                attrGroups: array(
                )
                flags: 0
                type: UnionType(
                    types: array(
                        0: IntersectionType(
                            types: array(
                                0: Name(
                                    name: A
                                )
                                1: Name(
                                    name: B
                                )
                            )
                        )
                        1: IntersectionType(
                            types: array(
                                0: Name(
                                    name: X
                                )
                                1: Name(
                                    name: Y
                                )
                            )
                        )
                    )
                )
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: a
                )
                default: null
                hooks: array(
                )
            )
        )
        returnType: UnionType(
            types: array(
                0: IntersectionType(
                    types: array(
                        0: Name(
                            name: A
                        )
                        1: Name(
                            name: B
                        )
                    )
                )
                1: IntersectionType(
                    types: array(
                        0: Name(
                            name: X
                        )
                        1: Name(
                            name: Y
                        )
                    )
                )
            )
        )
        stmts: array(
        )
    )
)
