Magic constants
-----
<?php

__CLASS__;
__DIR__;
__FILE__;
__FUNCTION__;
__LINE__;
__METHOD__;
__NAMESPACE__;
__TRAIT__;
__PROPERTY__;
-----
array(
    0: Stmt_Expression(
        expr: Scalar_MagicConst_Class(
        )
    )
    1: Stmt_Expression(
        expr: Scalar_MagicConst_Dir(
        )
    )
    2: Stmt_Expression(
        expr: Scalar_MagicConst_File(
        )
    )
    3: Stmt_Expression(
        expr: Scalar_MagicConst_Function(
        )
    )
    4: Stmt_Expression(
        expr: Scalar_MagicConst_Line(
        )
    )
    5: Stmt_Expression(
        expr: Scalar_MagicConst_Method(
        )
    )
    6: Stmt_Expression(
        expr: Scalar_MagicConst_Namespace(
        )
    )
    7: Stmt_Expression(
        expr: Scalar_MagicConst_Trait(
        )
    )
    8: Stmt_Expression(
        expr: Scalar_MagicConst_Property(
        )
    )
)
