For loop
-----
<?php

// "classical" loop
for ($i = 0; $i < $c; ++$i) {}

// multiple expressions
for ($a, $b; $c, $d; $e, $f) {}

// infinite loop
for (;;) {}

// alternative syntax
for (;;):
endfor;
-----
array(
    0: Stmt_For(
        init: array(
            0: Expr_Assign(
                var: Expr_Variable(
                    name: i
                )
                expr: Scalar_Int(
                    value: 0
                )
            )
        )
        cond: array(
            0: Expr_BinaryOp_Smaller(
                left: Expr_Variable(
                    name: i
                )
                right: Expr_Variable(
                    name: c
                )
            )
        )
        loop: array(
            0: Expr_PreInc(
                var: Expr_Variable(
                    name: i
                )
            )
        )
        stmts: array(
        )
        comments: array(
            0: // "classical" loop
        )
    )
    1: Stmt_For(
        init: array(
            0: Expr_Variable(
                name: a
            )
            1: Expr_Variable(
                name: b
            )
        )
        cond: array(
            0: Expr_Variable(
                name: c
            )
            1: Expr_Variable(
                name: d
            )
        )
        loop: array(
            0: Expr_Variable(
                name: e
            )
            1: Expr_Variable(
                name: f
            )
        )
        stmts: array(
        )
        comments: array(
            0: // multiple expressions
        )
    )
    2: Stmt_For(
        init: array(
        )
        cond: array(
        )
        loop: array(
        )
        stmts: array(
        )
        comments: array(
            0: // infinite loop
        )
    )
    3: Stmt_For(
        init: array(
        )
        cond: array(
        )
        loop: array(
        )
        stmts: array(
        )
        comments: array(
            0: // alternative syntax
        )
    )
)
