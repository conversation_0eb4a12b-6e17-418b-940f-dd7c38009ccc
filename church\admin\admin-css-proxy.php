<?php
// Set the content type to CSS
header('Content-Type: text/css');
// Add cache control headers to prevent caching issues
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: 0');

// Ensure we have a timestamp parameter for cache busting
$timestamp = isset($_GET['t']) ? $_GET['t'] : time();
// Add a version note for debugging
echo "/* CSS Loaded via proxy - v1.0.2 - {$timestamp} */\n";

// Include the admin CSS file
readfile(__DIR__ . '/css/admin-style.css');

// Include the custom theme CSS file if it exists
$customThemeFile = __DIR__ . '/css/custom-theme.css';
if (file_exists($customThemeFile)) {
    echo "\n/* Custom Theme CSS */\n";
    readfile($customThemeFile);
}
?>