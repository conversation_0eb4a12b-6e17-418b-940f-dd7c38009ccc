Declare
-----
<?php

declare (X='Y');

declare (A='B', C='D') {
    echo "foo";
}

declare (A='B', C='D'):
enddeclare;
-----
array(
    0: Stmt_Declare(
        declares: array(
            0: DeclareItem(
                key: Identifier(
                    name: X
                )
                value: Scalar_String(
                    value: Y
                )
            )
        )
        stmts: null
    )
    1: Stmt_Declare(
        declares: array(
            0: DeclareItem(
                key: Identifier(
                    name: A
                )
                value: Scalar_String(
                    value: B
                )
            )
            1: DeclareItem(
                key: Identifier(
                    name: C
                )
                value: Scalar_String(
                    value: D
                )
            )
        )
        stmts: array(
            0: Stmt_Echo(
                exprs: array(
                    0: Scalar_String(
                        value: foo
                    )
                )
            )
        )
    )
    2: Stmt_Declare(
        declares: array(
            0: DeclareItem(
                key: Identifier(
                    name: A
                )
                value: Scalar_String(
                    value: B
                )
            )
            1: DeclareItem(
                key: Identifier(
                    name: C
                )
                value: <PERSON><PERSON><PERSON>_String(
                    value: D
                )
            )
        )
        stmts: array(
        )
    )
)
