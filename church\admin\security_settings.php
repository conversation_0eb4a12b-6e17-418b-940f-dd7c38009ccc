<?php
/**
 * Security Settings
 * 
 * This page allows administrators to configure security settings for the application.
 */

// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include language system
require_once 'includes/language.php';

// Include the SecurityManager class
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize SecurityManager
$security = new SecurityManager($conn);

// Set security headers
$security->setSecurityHeaders();

// Log this access
$security->logSecurityEvent('Security settings page accessed', [
    'admin_id' => $_SESSION['admin_id']
]);

$message = '';
$error = '';

// Default settings
$settings = [
    'password_min_length' => 8,
    'password_require_uppercase' => 1,
    'password_require_lowercase' => 1,
    'password_require_number' => 1,
    'password_require_special' => 1,
    'password_expiry_days' => 90,
    'login_max_attempts' => 5,
    'login_lockout_minutes' => 30,
    'session_timeout_minutes' => 45,
    'require_2fa_for_admins' => 0,
    'audit_log_retention_days' => 90,
    'csrf_token_expiry_minutes' => 60
];

// Get current settings from database
try {
    $stmt = $conn->prepare("SELECT * FROM security_settings LIMIT 1");
    $stmt->execute();
    $dbSettings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($dbSettings) {
        $settings = array_merge($settings, $dbSettings);
    }
} catch (PDOException $e) {
    // If table doesn't exist yet, we'll create it
    $error = "Security settings table not found. It will be created when you save settings.";
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        // Validate and sanitize inputs
        $newSettings = [
            'password_min_length' => filter_input(INPUT_POST, 'password_min_length', FILTER_VALIDATE_INT, ['options' => ['min_range' => 8, 'max_range' => 32, 'default' => 8]]),
            'password_require_uppercase' => isset($_POST['password_require_uppercase']) ? 1 : 0,
            'password_require_lowercase' => isset($_POST['password_require_lowercase']) ? 1 : 0,
            'password_require_number' => isset($_POST['password_require_number']) ? 1 : 0,
            'password_require_special' => isset($_POST['password_require_special']) ? 1 : 0,
            'password_expiry_days' => filter_input(INPUT_POST, 'password_expiry_days', FILTER_VALIDATE_INT, ['options' => ['min_range' => 0, 'max_range' => 365, 'default' => 90]]),
            'login_max_attempts' => filter_input(INPUT_POST, 'login_max_attempts', FILTER_VALIDATE_INT, ['options' => ['min_range' => 3, 'max_range' => 10, 'default' => 5]]),
            'login_lockout_minutes' => filter_input(INPUT_POST, 'login_lockout_minutes', FILTER_VALIDATE_INT, ['options' => ['min_range' => 5, 'max_range' => 1440, 'default' => 30]]),
            'session_timeout_minutes' => filter_input(INPUT_POST, 'session_timeout_minutes', FILTER_VALIDATE_INT, ['options' => ['min_range' => 5, 'max_range' => 240, 'default' => 45]]),
            'require_2fa_for_admins' => isset($_POST['require_2fa_for_admins']) ? 1 : 0,
            'audit_log_retention_days' => filter_input(INPUT_POST, 'audit_log_retention_days', FILTER_VALIDATE_INT, ['options' => ['min_range' => 30, 'max_range' => 365, 'default' => 90]]),
            'csrf_token_expiry_minutes' => filter_input(INPUT_POST, 'csrf_token_expiry_minutes', FILTER_VALIDATE_INT, ['options' => ['min_range' => 15, 'max_range' => 240, 'default' => 60]])
        ];
        
        try {
            // Check if table exists
            $tableExists = false;
            $stmt = $conn->prepare("SHOW TABLES LIKE 'security_settings'");
            $stmt->execute();
            if ($stmt->rowCount() > 0) {
                $tableExists = true;
            }
            
            if (!$tableExists) {
                // Create table if it doesn't exist
                $conn->exec("
                    CREATE TABLE security_settings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        password_min_length INT NOT NULL DEFAULT 8,
                        password_require_uppercase TINYINT(1) NOT NULL DEFAULT 1,
                        password_require_lowercase TINYINT(1) NOT NULL DEFAULT 1,
                        password_require_number TINYINT(1) NOT NULL DEFAULT 1,
                        password_require_special TINYINT(1) NOT NULL DEFAULT 1,
                        password_expiry_days INT NOT NULL DEFAULT 90,
                        login_max_attempts INT NOT NULL DEFAULT 5,
                        login_lockout_minutes INT NOT NULL DEFAULT 30,
                        session_timeout_minutes INT NOT NULL DEFAULT 30,
                        require_2fa_for_admins TINYINT(1) NOT NULL DEFAULT 0,
                        audit_log_retention_days INT NOT NULL DEFAULT 90,
                        csrf_token_expiry_minutes INT NOT NULL DEFAULT 60,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )
                ");
            }
            
            // Check if settings exist
            $stmt = $conn->prepare("SELECT COUNT(*) FROM security_settings");
            $stmt->execute();
            $count = $stmt->fetchColumn();
            
            if ($count > 0) {
                // Update existing settings
                $sql = "UPDATE security_settings SET 
                    password_min_length = :password_min_length,
                    password_require_uppercase = :password_require_uppercase,
                    password_require_lowercase = :password_require_lowercase,
                    password_require_number = :password_require_number,
                    password_require_special = :password_require_special,
                    password_expiry_days = :password_expiry_days,
                    login_max_attempts = :login_max_attempts,
                    login_lockout_minutes = :login_lockout_minutes,
                    session_timeout_minutes = :session_timeout_minutes,
                    require_2fa_for_admins = :require_2fa_for_admins,
                    audit_log_retention_days = :audit_log_retention_days,
                    csrf_token_expiry_minutes = :csrf_token_expiry_minutes
                ";
            } else {
                // Insert new settings
                $sql = "INSERT INTO security_settings (
                    password_min_length,
                    password_require_uppercase,
                    password_require_lowercase,
                    password_require_number,
                    password_require_special,
                    password_expiry_days,
                    login_max_attempts,
                    login_lockout_minutes,
                    session_timeout_minutes,
                    require_2fa_for_admins,
                    audit_log_retention_days,
                    csrf_token_expiry_minutes
                ) VALUES (
                    :password_min_length,
                    :password_require_uppercase,
                    :password_require_lowercase,
                    :password_require_number,
                    :password_require_special,
                    :password_expiry_days,
                    :login_max_attempts,
                    :login_lockout_minutes,
                    :session_timeout_minutes,
                    :require_2fa_for_admins,
                    :audit_log_retention_days,
                    :csrf_token_expiry_minutes
                )";
            }
            
            $stmt = $conn->prepare($sql);
            $stmt->execute($newSettings);
            
            // Update settings variable with new values
            $settings = $newSettings;
            
            // Log the settings update
            $security->logSecurityEvent('Security settings updated', [
                'admin_id' => $_SESSION['admin_id'],
                'username' => $_SESSION['admin_username']
            ]);
            
            $message = "Security settings have been updated successfully.";
        } catch (PDOException $e) {
            $error = "Error saving settings: " . $e->getMessage();
        }
    }
}

// Page title and header info
$page_title = __('security_settings');
$page_header = __('security_settings');
$page_description = __('configure_security_settings_description');

include 'includes/header.php';
?>

            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                <strong>Note:</strong> This page manages comprehensive security settings including password policies, login security, and audit logging.
                For general system settings, visit <a href="settings.php" class="alert-link">Settings</a>.
            </div>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i><?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i><?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-shield-lock"></i> <?php _e('security_configuration'); ?></h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                        <?php echo $security->generateCSRFInput(); ?>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5><?php _e('password_policy'); ?></h5>
                                <div class="mb-3">
                                    <label for="password_min_length" class="form-label">Minimum Password Length</label>
                                    <input type="number" class="form-control" id="password_min_length" name="password_min_length" 
                                           value="<?php echo $settings['password_min_length']; ?>" min="8" max="32" required>
                                    <div class="form-text">Minimum recommended length is 8 characters.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="password_require_uppercase" name="password_require_uppercase" 
                                               <?php echo $settings['password_require_uppercase'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="password_require_uppercase">
                                            Require uppercase letters
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="password_require_lowercase" name="password_require_lowercase" 
                                               <?php echo $settings['password_require_lowercase'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="password_require_lowercase">
                                            Require lowercase letters
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="password_require_number" name="password_require_number" 
                                               <?php echo $settings['password_require_number'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="password_require_number">
                                            Require numbers
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="password_require_special" name="password_require_special" 
                                               <?php echo $settings['password_require_special'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="password_require_special">
                                            Require special characters
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password_expiry_days" class="form-label">Password Expiry (Days)</label>
                                    <input type="number" class="form-control" id="password_expiry_days" name="password_expiry_days" 
                                           value="<?php echo $settings['password_expiry_days']; ?>" min="0" max="365">
                                    <div class="form-text">Set to 0 to disable password expiry.</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5><?php _e('login_security'); ?></h5>
                                <div class="mb-3">
                                    <label for="login_max_attempts" class="form-label">Maximum Login Attempts</label>
                                    <input type="number" class="form-control" id="login_max_attempts" name="login_max_attempts" 
                                           value="<?php echo $settings['login_max_attempts']; ?>" min="3" max="10" required>
                                    <div class="form-text">Number of failed attempts before account lockout.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="login_lockout_minutes" class="form-label">Account Lockout Duration (Minutes)</label>
                                    <input type="number" class="form-control" id="login_lockout_minutes" name="login_lockout_minutes" 
                                           value="<?php echo $settings['login_lockout_minutes']; ?>" min="5" max="1440" required>
                                    <div class="form-text">How long accounts remain locked after too many failed attempts.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="session_timeout_minutes" class="form-label">Session Timeout (Minutes)</label>
                                    <input type="number" class="form-control" id="session_timeout_minutes" name="session_timeout_minutes" 
                                           value="<?php echo $settings['session_timeout_minutes']; ?>" min="5" max="240" required>
                                    <div class="form-text">How long until inactive sessions are automatically logged out.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="require_2fa_for_admins" name="require_2fa_for_admins" 
                                               <?php echo $settings['require_2fa_for_admins'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="require_2fa_for_admins">
                                            Require Two-Factor Authentication for all administrators
                                        </label>
                                    </div>
                                    <div class="form-text">When enabled, all administrators must set up 2FA before they can access the admin area.</div>
                                </div>
                                
                                <h5 class="mt-4"><?php _e('system_security'); ?></h5>
                                <div class="mb-3">
                                    <label for="audit_log_retention_days" class="form-label">Audit Log Retention (Days)</label>
                                    <input type="number" class="form-control" id="audit_log_retention_days" name="audit_log_retention_days" 
                                           value="<?php echo $settings['audit_log_retention_days']; ?>" min="30" max="365" required>
                                    <div class="form-text">How long to keep security audit logs before automatic deletion.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="csrf_token_expiry_minutes" class="form-label">CSRF Token Expiry (Minutes)</label>
                                    <input type="number" class="form-control" id="csrf_token_expiry_minutes" name="csrf_token_expiry_minutes" 
                                           value="<?php echo $settings['csrf_token_expiry_minutes']; ?>" min="15" max="240" required>
                                    <div class="form-text">How long CSRF tokens remain valid.</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> Save Security Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> <?php _e('security_information'); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Security Features</h5>
                            <ul class="list-group mb-3">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Two-Factor Authentication
                                    <span class="badge bg-success rounded-pill">Enabled</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    CSRF Protection
                                    <span class="badge bg-success rounded-pill">Enabled</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Security Audit Logging
                                    <span class="badge bg-success rounded-pill">Enabled</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Account Lockout Protection
                                    <span class="badge bg-success rounded-pill">Enabled</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Password Strength Enforcement
                                    <span class="badge bg-success rounded-pill">Enabled</span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Related Security Pages</h5>
                            <div class="list-group">
                                <a href="security_audit.php" class="list-group-item list-group-item-action">
                                    <i class="bi bi-shield-lock"></i> Security Audit Logs
                                </a>
                                <a href="profile.php" class="list-group-item list-group-item-action">
                                    <i class="bi bi-person-badge"></i> Configure Two-Factor Authentication
                                </a>
                                <a href="setup_security.php" class="list-group-item list-group-item-action">
                                    <i class="bi bi-shield-plus"></i> Security Setup
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


<?php include 'includes/footer.php'; ?> 