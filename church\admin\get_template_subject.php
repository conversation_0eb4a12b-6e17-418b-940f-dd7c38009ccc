<?php
require_once '../config.php';

header('Content-Type: application/json');

if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Template ID is required']);
    exit;
}

$template_id = intval($_GET['id']);

try {
    $stmt = $pdo->prepare("SELECT subject FROM email_templates WHERE id = ?");
    $stmt->execute([$template_id]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($template) {
        echo json_encode(['subject' => $template['subject']]);
    } else {
        http_response_code(404);
        echo json_encode(['error' => 'Template not found']);
    }

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?>
