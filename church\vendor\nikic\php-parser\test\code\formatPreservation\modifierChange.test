Modifier change
-----
<?php
class Foo {}
abstract class Bar {
    const
    FOO = 42;

    var $foo
    = 24;

    public function
    foo() {}
}
-----
$stmts[0]->flags = Stmt\Class_::MODIFIER_ABSTRACT;
$stmts[1]->flags = 0;
$stmts[1]->stmts[0]->flags = Stmt\Class_::MODIFIER_PRIVATE;
$stmts[1]->stmts[1]->flags = Stmt\Class_::MODIFIER_PROTECTED;
$stmts[1]->stmts[2]->flags |= Stmt\Class_::MODIFIER_FINAL;
-----
<?php
abstract class Foo {}
class Bar {
    private const
    FOO = 42;

    protected $foo
    = 24;

    final public function
    foo() {}
}
-----
<?php
function test(
    public T1 $x
        = 'y',
    private T2 $y
        = 'z',
    T3 $z
        = 'x',
) {}
-----
$stmts[0]->params[0]->flags = Stmt\Class_::MODIFIER_PRIVATE;
$stmts[0]->params[1]->flags = 0;
$stmts[0]->params[2]->flags = Stmt\Class_::MODIFIER_PUBLIC;
-----
<?php
function test(
    private T1 $x
        = 'y',
    T2 $y
        = 'z',
    public T3 $z
        = 'x',
) {}
-----
<?php
new class {};
new readonly class {};
-----
$stmts[0]->expr->class->flags = Modifiers::READONLY;
$stmts[1]->expr->class->flags = 0;
-----
<?php
readonly class {};
class {};
