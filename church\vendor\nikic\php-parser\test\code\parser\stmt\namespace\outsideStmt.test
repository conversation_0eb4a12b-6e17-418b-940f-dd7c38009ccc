Some statements may occur outside of namespaces
-----
<?php
declare(A='B');
namespace B {

}
__halt_compiler()
?>
Hi!
-----
array(
    0: Stmt_Declare(
        declares: array(
            0: DeclareItem(
                key: Identifier(
                    name: A
                )
                value: Scalar_String(
                    value: B
                )
            )
        )
        stmts: null
    )
    1: Stmt_Namespace(
        name: Name(
            name: B
        )
        stmts: array(
        )
    )
    2: Stmt_HaltCompiler(
        remaining: Hi!
    )
)
-----
<?php
/* Comment */
;
namespace Foo;
-----
array(
    0: Stmt_Nop(
        comments: array(
            0: /* Comment */
        )
    )
    1: Stmt_Namespace(
        name: Name(
            name: Foo
        )
        stmts: array(
        )
    )
)
