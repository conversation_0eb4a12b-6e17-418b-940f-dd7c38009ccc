<?php
session_start();

require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo->beginTransaction();

        // Color scheme settings
        $colorSettings = [
            'primary_color' => $_POST['primary_color'] ?? '#007bff',
            'secondary_color' => $_POST['secondary_color'] ?? '#6c757d',
            'success_color' => $_POST['success_color'] ?? '#28a745',
            'danger_color' => $_POST['danger_color'] ?? '#dc3545',
            'warning_color' => $_POST['warning_color'] ?? '#ffc107',
            'info_color' => $_POST['info_color'] ?? '#17a2b8',
            'light_color' => $_POST['light_color'] ?? '#f8f9fa',
            'dark_color' => $_POST['dark_color'] ?? '#343a40',
            'background_color' => $_POST['background_color'] ?? '#ffffff',
            'text_color' => $_POST['text_color'] ?? '#212529',
            'link_color' => $_POST['link_color'] ?? '#007bff',
            'link_hover_color' => $_POST['link_hover_color'] ?? '#0056b3'
        ];

        // Typography settings
        $typographySettings = [
            'primary_font' => $_POST['primary_font'] ?? 'Inter',
            'secondary_font' => $_POST['secondary_font'] ?? 'Inter',
            'font_size_base' => $_POST['font_size_base'] ?? '16',
            'font_weight_normal' => $_POST['font_weight_normal'] ?? '400',
            'font_weight_bold' => $_POST['font_weight_bold'] ?? '600',
            'line_height_base' => $_POST['line_height_base'] ?? '1.5',
            'heading_font_weight' => $_POST['heading_font_weight'] ?? '600'
        ];

        // Layout settings
        $layoutSettings = [
            'sidebar_style' => $_POST['sidebar_style'] ?? 'default',
            'navbar_style' => $_POST['navbar_style'] ?? 'default',
            'card_style' => $_POST['card_style'] ?? 'default',
            'border_radius' => $_POST['border_radius'] ?? '0.375',
            'box_shadow' => $_POST['box_shadow'] ?? 'default',
            'container_width' => $_POST['container_width'] ?? 'fluid',
            'sidebar_width' => $_POST['sidebar_width'] ?? '250',
            'header_height' => $_POST['header_height'] ?? '60',
            'content_padding' => $_POST['content_padding'] ?? '1.5',
            'card_spacing' => $_POST['card_spacing'] ?? '1.5',
            'container_max_width' => $_POST['container_max_width'] ?? '1200',
            'sidebar_width' => $_POST['sidebar_width'] ?? '280',
            'content_spacing' => $_POST['content_spacing'] ?? '30',
            'sidebar_bg_color' => $_POST['sidebar_bg_color'] ?? '#343a40',
            'sidebar_text_color' => $_POST['sidebar_text_color'] ?? '#ffffff',
            'sidebar_hover_color' => $_POST['sidebar_hover_color'] ?? '#007bff'
        ];

        // Theme settings
        $themeSettings = [
            'theme_mode' => $_POST['theme_mode'] ?? 'light',
            'admin_theme' => $_POST['admin_theme'] ?? 'default',
            'user_theme' => $_POST['user_theme'] ?? 'default',
            'custom_css' => $_POST['custom_css'] ?? '',
            'enable_dark_mode' => isset($_POST['enable_dark_mode']) ? '1' : '0',
            'enable_theme_switcher' => isset($_POST['enable_theme_switcher']) ? '1' : '0'
        ];

        // Save all settings
        $allSettings = array_merge($colorSettings, $typographySettings, $layoutSettings, $themeSettings);

        foreach ($allSettings as $key => $value) {
            update_site_setting($key, $value);
        }

        $pdo->commit();
        $success_message = "Appearance settings updated successfully!";

        // Generate CSS file
        generateCustomCSS();

    } catch (Exception $e) {
        $pdo->rollback();
        $error_message = "Error updating appearance settings: " . $e->getMessage();
    }
}

// Include shared settings functions
require_once 'includes/settings_functions.php';

// Get current appearance settings
$currentSettings = getAppearanceSettings();

// Function to generate custom CSS
function generateCustomCSS() {
    $css = ":root {\n";

    // Color variables
    $colorVars = [
        'primary_color' => '--bs-primary',
        'secondary_color' => '--bs-secondary',
        'success_color' => '--bs-success',
        'danger_color' => '--bs-danger',
        'warning_color' => '--bs-warning',
        'info_color' => '--bs-info',
        'light_color' => '--bs-light',
        'dark_color' => '--bs-dark',
        'background_color' => '--bs-body-bg',
        'text_color' => '--bs-body-color',
        'link_color' => '--bs-link-color',
        'link_hover_color' => '--bs-link-hover-color'
    ];

    foreach ($colorVars as $setting => $cssVar) {
        $value = get_site_setting($setting, '');
        if ($value) {
            $css .= "  $cssVar: $value;\n";
        }
    }

    // Add sidebar-specific color variables
    $sidebarBg = get_site_setting('sidebar_bg_color', '#343a40');
    $sidebarText = get_site_setting('sidebar_text_color', '#ffffff');
    $sidebarHover = get_site_setting('sidebar_hover_color', '#007bff');

    $css .= "  --sidebar-bg-color: $sidebarBg;\n";
    $css .= "  --sidebar-text-color: $sidebarText;\n";
    $css .= "  --sidebar-hover-color: $sidebarHover;\n";

    // Typography variables
    $fontFamily = get_site_setting('primary_font', 'Inter');
    $fontSize = get_site_setting('font_size_base', '16');
    $lineHeight = get_site_setting('line_height_base', '1.5');

    $css .= "  --bs-font-sans-serif: '$fontFamily', system-ui, -apple-system, sans-serif;\n";
    $css .= "  --bs-body-font-size: {$fontSize}px;\n";
    $css .= "  --bs-body-line-height: $lineHeight;\n";

    // Layout variables
    $borderRadius = get_site_setting('border_radius', '0.375');
    $sidebarWidth = get_site_setting('sidebar_width', '280');
    $contentSpacing = get_site_setting('content_spacing', '30');

    $css .= "  --bs-border-radius: {$borderRadius}rem;\n";
    $css .= "  --sidebar-width: {$sidebarWidth}px;\n";
    $css .= "  --content-spacing: {$contentSpacing}px;\n";

    $css .= "}\n\n";

    // Add sidebar theme CSS
    $css .= "/* Sidebar Theme Styles */\n";
    $css .= ".sidebar {\n";
    $css .= "  background-color: var(--sidebar-bg-color) !important;\n";
    $css .= "  color: var(--sidebar-text-color) !important;\n";
    $css .= "  width: var(--sidebar-width) !important;\n";
    $css .= "}\n\n";

    $css .= ".sidebar .nav-link, .sidebar a {\n";
    $css .= "  color: var(--sidebar-text-color) !important;\n";
    $css .= "}\n\n";

    $css .= ".sidebar .nav-link:hover, .sidebar a:hover {\n";
    $css .= "  background-color: var(--sidebar-hover-color) !important;\n";
    $css .= "  color: #ffffff !important;\n";
    $css .= "}\n\n";

    $css .= ".main-content {\n";
    $css .= "  margin-left: calc(var(--sidebar-width) + var(--content-spacing)) !important;\n";
    $css .= "  padding: var(--content-spacing) !important;\n";
    $css .= "}\n\n";

    // Custom CSS
    $customCSS = get_site_setting('custom_css', '');
    if ($customCSS) {
        $css .= "/* Custom CSS */\n" . $customCSS . "\n";
    }

    // Save CSS file to correct location
    $cssFile = __DIR__ . '/css/custom-theme.css';
    if (!is_dir(dirname($cssFile))) {
        mkdir(dirname($cssFile), 0755, true);
    }
    file_put_contents($cssFile, $css);

    // Also save to cache for faster loading
    $cacheFile = __DIR__ . '/../cache/theme-cache.css';
    if (!is_dir(dirname($cacheFile))) {
        mkdir(dirname($cacheFile), 0755, true);
    }
    file_put_contents($cacheFile, $css);
}
// Generate CSS on page load and after form submission
$cssFile = __DIR__ . '/css/custom-theme.css';
if (!file_exists($cssFile) || isset($_POST) && !empty($_POST)) {
    generateCustomCSS();
}

// Include header first to load language system
include 'includes/header.php';

// Page title and header info (after language system is loaded)
$page_title = __('appearance_settings');
$page_header = __('appearance_settings');
$page_description = __('customize_appearance_description');
?>

<!-- Page Header -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><?php echo $page_header; ?></h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-secondary" id="preview-changes">
            <i class="bi bi-eye"></i> Preview Changes
        </button>
        <button type="button" class="btn btn-outline-secondary ms-2" id="reset-defaults">
            <i class="bi bi-arrow-clockwise"></i> Reset to Defaults
        </button>
    </div>
</div>

            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST" id="appearance-form" class="appearance-settings">

    <!-- Advanced Theme Customizer -->
    <div class="theme-customizer">
        <h3>Advanced Theme Customizer</h3>

        <!-- Preset Selector -->
        <div class="preset-selector">
            <h4>Theme Presets</h4>
            <div class="preset-grid">
                <div class="preset-card" data-preset="default">
                    <h5>Default Blue</h5>
                    <div class="preset-colors">
                        <div class="preset-color" style="background: #007bff;"></div>
                        <div class="preset-color" style="background: #6c757d;"></div>
                        <div class="preset-color" style="background: #28a745;"></div>
                        <div class="preset-color" style="background: #dc3545;"></div>
                    </div>
                    <small>Classic and professional</small>
                </div>
                <div class="preset-card" data-preset="modern">
                    <h5>Modern Purple</h5>
                    <div class="preset-colors">
                        <div class="preset-color" style="background: #6f42c1;"></div>
                        <div class="preset-color" style="background: #20c997;"></div>
                        <div class="preset-color" style="background: #e74c3c;"></div>
                        <div class="preset-color" style="background: #f39c12;"></div>
                    </div>
                    <small>Modern and vibrant</small>
                </div>
                <div class="preset-card" data-preset="minimal">
                    <h5>Minimal Gray</h5>
                    <div class="preset-colors">
                        <div class="preset-color" style="background: #495057;"></div>
                        <div class="preset-color" style="background: #51cf66;"></div>
                        <div class="preset-color" style="background: #ff6b6b;"></div>
                        <div class="preset-color" style="background: #ffd43b;"></div>
                    </div>
                    <small>Clean and minimal</small>
                </div>
                <div class="preset-card" data-preset="vibrant">
                    <h5>Vibrant Orange</h5>
                    <div class="preset-colors">
                        <div class="preset-color" style="background: #fd7e14;"></div>
                        <div class="preset-color" style="background: #20c997;"></div>
                        <div class="preset-color" style="background: #dc3545;"></div>
                        <div class="preset-color" style="background: #ffc107;"></div>
                    </div>
                    <small>Energetic and warm</small>
                </div>
                <div class="preset-card" data-preset="corporate">
                    <h5>Corporate Blue</h5>
                    <div class="preset-colors">
                        <div class="preset-color" style="background: #0d6efd;"></div>
                        <div class="preset-color" style="background: #198754;"></div>
                        <div class="preset-color" style="background: #dc3545;"></div>
                        <div class="preset-color" style="background: #ffc107;"></div>
                    </div>
                    <small>Professional and trustworthy</small>
                </div>
            </div>

            <!-- Theme Controls -->
            <div class="theme-controls">
                <div class="preview-toggle">
                    <input type="checkbox" id="previewToggle" class="form-check-input">
                    <label for="previewToggle" class="form-check-label">Live Preview</label>
                </div>

                <div class="theme-actions">
                    <button type="button" id="resetTheme" class="btn-theme btn-theme-secondary">
                        <i class="bi bi-arrow-clockwise"></i> Reset
                    </button>
                    <div class="import-export">
                        <button type="button" id="exportTheme" class="btn-theme btn-theme-secondary">
                            <i class="bi bi-download"></i> Export
                        </button>
                        <button type="button" id="importTheme" class="btn-theme btn-theme-secondary">
                            <i class="bi bi-upload"></i> Import
                        </button>
                        <input type="file" id="importFile" accept=".json">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Color Scheme Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-palette-fill"></i> Color Scheme
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="primary_color" class="form-label">Primary Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" value="<?php echo htmlspecialchars($currentSettings['primary_color'] ?: '#007bff'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['primary_color'] ?: '#007bff'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="secondary_color" class="form-label">Secondary Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color" value="<?php echo htmlspecialchars($currentSettings['secondary_color'] ?: '#6c757d'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['secondary_color'] ?: '#6c757d'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="success_color" class="form-label">Success Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="success_color" name="success_color" value="<?php echo htmlspecialchars($currentSettings['success_color'] ?: '#28a745'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['success_color'] ?: '#28a745'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="danger_color" class="form-label">Danger Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="danger_color" name="danger_color" value="<?php echo htmlspecialchars($currentSettings['danger_color'] ?: '#dc3545'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['danger_color'] ?: '#dc3545'); ?>" readonly>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="background_color" class="form-label">Background Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="background_color" name="background_color" value="<?php echo htmlspecialchars($currentSettings['background_color'] ?: '#ffffff'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['background_color'] ?: '#ffffff'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="text_color" class="form-label">Text Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="text_color" name="text_color" value="<?php echo htmlspecialchars($currentSettings['text_color'] ?: '#212529'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['text_color'] ?: '#212529'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="link_color" class="form-label">Link Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="link_color" name="link_color" value="<?php echo htmlspecialchars($currentSettings['link_color'] ?: '#007bff'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['link_color'] ?: '#007bff'); ?>" readonly>
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <h6>Color Presets</h6>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="default">Default</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="blue">Blue</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="green">Green</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="purple">Purple</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm color-preset" data-preset="orange">Orange</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Typography Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-fonts"></i> Typography
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-6 mb-3">
                    <label for="primary_font" class="form-label">Primary Font</label>
                    <select class="form-select" id="primary_font" name="primary_font">
                        <option value="Inter" <?php echo ($currentSettings['primary_font'] === 'Inter') ? 'selected' : ''; ?>>Inter</option>
                        <option value="Roboto" <?php echo ($currentSettings['primary_font'] === 'Roboto') ? 'selected' : ''; ?>>Roboto</option>
                        <option value="Open Sans" <?php echo ($currentSettings['primary_font'] === 'Open Sans') ? 'selected' : ''; ?>>Open Sans</option>
                        <option value="Lato" <?php echo ($currentSettings['primary_font'] === 'Lato') ? 'selected' : ''; ?>>Lato</option>
                        <option value="Montserrat" <?php echo ($currentSettings['primary_font'] === 'Montserrat') ? 'selected' : ''; ?>>Montserrat</option>
                        <option value="Poppins" <?php echo ($currentSettings['primary_font'] === 'Poppins') ? 'selected' : ''; ?>>Poppins</option>
                        <option value="Source Sans Pro" <?php echo ($currentSettings['primary_font'] === 'Source Sans Pro') ? 'selected' : ''; ?>>Source Sans Pro</option>
                    </select>
                </div>
                <div class="col-lg-6 mb-3">
                    <label for="font_size_base" class="form-label">Base Font Size (px)</label>
                    <input type="number" class="form-control" id="font_size_base" name="font_size_base" value="<?php echo htmlspecialchars($currentSettings['font_size_base'] ?: '16'); ?>" min="12" max="24">
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4 mb-3">
                    <label for="font_weight_normal" class="form-label">Normal Weight</label>
                    <select class="form-select" id="font_weight_normal" name="font_weight_normal">
                        <option value="300" <?php echo ($currentSettings['font_weight_normal'] === '300') ? 'selected' : ''; ?>>Light (300)</option>
                        <option value="400" <?php echo ($currentSettings['font_weight_normal'] === '400') ? 'selected' : ''; ?>>Normal (400)</option>
                        <option value="500" <?php echo ($currentSettings['font_weight_normal'] === '500') ? 'selected' : ''; ?>>Medium (500)</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="font_weight_bold" class="form-label">Bold Weight</label>
                    <select class="form-select" id="font_weight_bold" name="font_weight_bold">
                        <option value="600" <?php echo ($currentSettings['font_weight_bold'] === '600') ? 'selected' : ''; ?>>Semi-Bold (600)</option>
                        <option value="700" <?php echo ($currentSettings['font_weight_bold'] === '700') ? 'selected' : ''; ?>>Bold (700)</option>
                        <option value="800" <?php echo ($currentSettings['font_weight_bold'] === '800') ? 'selected' : ''; ?>>Extra-Bold (800)</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="line_height_base" class="form-label">Line Height</label>
                    <input type="number" class="form-control" id="line_height_base" name="line_height_base" value="<?php echo htmlspecialchars($currentSettings['line_height_base'] ?: '1.5'); ?>" min="1" max="2" step="0.1">
                </div>
            </div>

            <div class="mt-3">
                <h6>Typography Preview</h6>
                <div class="p-3 border rounded" id="typography-preview">
                    <h1>Heading 1</h1>
                    <h2>Heading 2</h2>
                    <h3>Heading 3</h3>
                    <p>This is a paragraph of text to demonstrate the typography settings. It shows how the font family, size, weight, and line height work together.</p>
                    <p><strong>Bold text</strong> and <a href="#">link text</a> examples.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Layout Settings Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-layout-sidebar"></i> Layout Settings
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-4 mb-3">
                    <label for="sidebar_style" class="form-label">Sidebar Style</label>
                    <select class="form-select" id="sidebar_style" name="sidebar_style">
                        <option value="default" <?php echo ($currentSettings['sidebar_style'] === 'default') ? 'selected' : ''; ?>>Default</option>
                        <option value="compact" <?php echo ($currentSettings['sidebar_style'] === 'compact') ? 'selected' : ''; ?>>Compact</option>
                        <option value="minimal" <?php echo ($currentSettings['sidebar_style'] === 'minimal') ? 'selected' : ''; ?>>Minimal</option>
                        <option value="dark" <?php echo ($currentSettings['sidebar_style'] === 'dark') ? 'selected' : ''; ?>>Dark</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="navbar_style" class="form-label">Navigation Style</label>
                    <select class="form-select" id="navbar_style" name="navbar_style">
                        <option value="default" <?php echo ($currentSettings['navbar_style'] === 'default') ? 'selected' : ''; ?>>Default</option>
                        <option value="fixed" <?php echo ($currentSettings['navbar_style'] === 'fixed') ? 'selected' : ''; ?>>Fixed Top</option>
                        <option value="sticky" <?php echo ($currentSettings['navbar_style'] === 'sticky') ? 'selected' : ''; ?>>Sticky</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="card_style" class="form-label">Card Style</label>
                    <select class="form-select" id="card_style" name="card_style">
                        <option value="default" <?php echo ($currentSettings['card_style'] === 'default') ? 'selected' : ''; ?>>Default</option>
                        <option value="elevated" <?php echo ($currentSettings['card_style'] === 'elevated') ? 'selected' : ''; ?>>Elevated</option>
                        <option value="flat" <?php echo ($currentSettings['card_style'] === 'flat') ? 'selected' : ''; ?>>Flat</option>
                        <option value="outlined" <?php echo ($currentSettings['card_style'] === 'outlined') ? 'selected' : ''; ?>>Outlined</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4 mb-3">
                    <label for="border_radius" class="form-label">Border Radius (rem)</label>
                    <input type="number" class="form-control" id="border_radius" name="border_radius" value="<?php echo htmlspecialchars($currentSettings['border_radius'] ?: '0.375'); ?>" min="0" max="2" step="0.125">
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="container_max_width" class="form-label">Container Max Width (px)</label>
                    <input type="number" class="form-control" id="container_max_width" name="container_max_width" value="<?php echo htmlspecialchars($currentSettings['container_max_width'] ?: '1200'); ?>" min="800" max="1920" step="50">
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="sidebar_width" class="form-label">Sidebar Width (px)</label>
                    <input type="number" class="form-control" id="sidebar_width" name="sidebar_width" value="<?php echo htmlspecialchars($currentSettings['sidebar_width'] ?: '280'); ?>" min="200" max="400" step="10">
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6 mb-3">
                    <label for="content_spacing" class="form-label">Content Spacing (px)</label>
                    <input type="number" class="form-control" id="content_spacing" name="content_spacing" value="<?php echo htmlspecialchars($currentSettings['content_spacing'] ?: '30'); ?>" min="10" max="50" step="5">
                    <div class="form-text">Space between sidebar and main content</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Colors Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-layout-sidebar"></i> Sidebar Colors
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="sidebar_bg_color" class="form-label">Sidebar Background</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="sidebar_bg_color" name="sidebar_bg_color" value="<?php echo htmlspecialchars($currentSettings['sidebar_bg_color'] ?: '#343a40'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['sidebar_bg_color'] ?: '#343a40'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="sidebar_text_color" class="form-label">Sidebar Text Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="sidebar_text_color" name="sidebar_text_color" value="<?php echo htmlspecialchars($currentSettings['sidebar_text_color'] ?: '#ffffff'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['sidebar_text_color'] ?: '#ffffff'); ?>" readonly>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <label for="sidebar_hover_color" class="form-label">Sidebar Hover Color</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="sidebar_hover_color" name="sidebar_hover_color" value="<?php echo htmlspecialchars($currentSettings['sidebar_hover_color'] ?: '#007bff'); ?>">
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($currentSettings['sidebar_hover_color'] ?: '#007bff'); ?>" readonly>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logo and Branding Link -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-image"></i> Logo and Branding
            </h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                <strong>Logo and favicon management is handled separately.</strong>
                <br>
                Use the <a href="logo_management.php" class="alert-link">Logo Management</a> page to upload and manage your logos and favicons.
            </div>
        </div>
    </div>

    <!-- Theme Settings Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-moon-stars"></i> Theme Settings
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-4 mb-3">
                    <label for="theme_mode" class="form-label">Default Theme Mode</label>
                    <select class="form-select" id="theme_mode" name="theme_mode">
                        <option value="light" <?php echo ($currentSettings['theme_mode'] === 'light') ? 'selected' : ''; ?>>Light</option>
                        <option value="dark" <?php echo ($currentSettings['theme_mode'] === 'dark') ? 'selected' : ''; ?>>Dark</option>
                        <option value="auto" <?php echo ($currentSettings['theme_mode'] === 'auto') ? 'selected' : ''; ?>>Auto (System)</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="admin_theme" class="form-label">Admin Theme</label>
                    <select class="form-select" id="admin_theme" name="admin_theme">
                        <option value="default" <?php echo ($currentSettings['admin_theme'] === 'default') ? 'selected' : ''; ?>>Default</option>
                        <option value="modern" <?php echo ($currentSettings['admin_theme'] === 'modern') ? 'selected' : ''; ?>>Modern</option>
                        <option value="classic" <?php echo ($currentSettings['admin_theme'] === 'classic') ? 'selected' : ''; ?>>Classic</option>
                        <option value="minimal" <?php echo ($currentSettings['admin_theme'] === 'minimal') ? 'selected' : ''; ?>>Minimal</option>
                    </select>
                </div>
                <div class="col-lg-4 mb-3">
                    <label for="user_theme" class="form-label">User Theme</label>
                    <select class="form-select" id="user_theme" name="user_theme">
                        <option value="default" <?php echo ($currentSettings['user_theme'] === 'default') ? 'selected' : ''; ?>>Default</option>
                        <option value="modern" <?php echo ($currentSettings['user_theme'] === 'modern') ? 'selected' : ''; ?>>Modern</option>
                        <option value="classic" <?php echo ($currentSettings['user_theme'] === 'classic') ? 'selected' : ''; ?>>Classic</option>
                        <option value="minimal" <?php echo ($currentSettings['user_theme'] === 'minimal') ? 'selected' : ''; ?>>Minimal</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="enable_dark_mode" name="enable_dark_mode" <?php echo ($currentSettings['enable_dark_mode'] === '1') ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="enable_dark_mode">
                            Enable Dark Mode Support
                        </label>
                    </div>
                </div>
                <div class="col-lg-6 mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="enable_theme_switcher" name="enable_theme_switcher" <?php echo ($currentSettings['enable_theme_switcher'] === '1') ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="enable_theme_switcher">
                            Show Theme Switcher to Users
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom CSS Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-code-slash"></i> Custom CSS
            </h5>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label for="custom_css" class="form-label">Additional CSS</label>
                <textarea class="form-control font-monospace" id="custom_css" name="custom_css" rows="10" placeholder="/* Add your custom CSS here */"><?php echo htmlspecialchars($currentSettings['custom_css']); ?></textarea>
                <div class="form-text">
                    Add custom CSS to override default styles. Use CSS custom properties (variables) for better theme integration.
                </div>
            </div>

            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> Available CSS Variables</h6>
                <div class="row">
                    <div class="col-md-6">
                        <small>
                            <strong>Colors:</strong><br>
                            --bs-primary, --bs-secondary<br>
                            --bs-success, --bs-danger<br>
                            --bs-warning, --bs-info<br>
                            --bs-body-bg, --bs-body-color
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small>
                            <strong>Layout:</strong><br>
                            --sidebar-width<br>
                            --bs-border-radius<br>
                            --bs-font-sans-serif<br>
                            --bs-body-font-size
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-save"></i> Save Appearance Settings
            </button>
            <button type="button" class="btn btn-outline-secondary ms-2" onclick="window.location.reload()">
                <i class="bi bi-arrow-clockwise"></i> Cancel
            </button>
        </div>
    </div>
</form>

<link href="<?php echo ADMIN_URL; ?>/css/custom-theme.css" rel="stylesheet">
<link href="<?php echo ADMIN_URL; ?>/css/theme-customizer.css" rel="stylesheet">
<script src="<?php echo ADMIN_URL; ?>/js/theme-customizer.js"></script>
<script src="<?php echo ADMIN_URL; ?>/assets/js/appearance-customizer.js"></script>

<?php include 'includes/footer.php'; ?>

               