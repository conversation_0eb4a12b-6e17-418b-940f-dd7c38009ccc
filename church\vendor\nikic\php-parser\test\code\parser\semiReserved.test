Valid usages of reserved keywords as identifiers
-----
<?php

class Test {
    function array() {}
    function public() {}

    static function list() {}
    static function protected() {}

    public $class;
    public $private;

    const TRAIT = 3, FINAL = 4;

    const __CLASS__ = 1, __TRAIT__ = 2, __FUNCTION__ = 3, __METHOD__ = 4, __LINE__ = 5,
          __FILE__ = 6, __DIR__ = 7, __NAMESPACE__ = 8;
    // __halt_compiler does not work
}

$t = new Test;
$t->array();
$t->public();

Test::list();
Test::protected();

$t->class;
$t->private;

Test::TRAIT;
Test::FINAL;

class Foo {
    use TraitA, TraitB {
        TraitA::catch insteadof namespace\TraitB;
        TraitA::list as foreach;
        TraitB::throw as protected public;
        TraitB::self as protected;
        exit as die;
        \TraitC::exit as bye;
        namespace\TraitC::exit as byebye;
        TraitA::
            //
            /** doc comment */
            #
        catch /* comment */
            // comment
            # comment
        insteadof TraitB;
    }
}
-----
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: Test
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_ClassMethod(
                attrGroups: array(
                )
                flags: 0
                byRef: false
                name: Identifier(
                    name: array
                )
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
            1: Stmt_ClassMethod(
                attrGroups: array(
                )
                flags: 0
                byRef: false
                name: Identifier(
                    name: public
                )
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
            2: Stmt_ClassMethod(
                attrGroups: array(
                )
                flags: STATIC (8)
                byRef: false
                name: Identifier(
                    name: list
                )
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
            3: Stmt_ClassMethod(
                attrGroups: array(
                )
                flags: STATIC (8)
                byRef: false
                name: Identifier(
                    name: protected
                )
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
            4: Stmt_Property(
                attrGroups: array(
                )
                flags: PUBLIC (1)
                type: null
                props: array(
                    0: PropertyItem(
                        name: VarLikeIdentifier(
                            name: class
                        )
                        default: null
                    )
                )
                hooks: array(
                )
            )
            5: Stmt_Property(
                attrGroups: array(
                )
                flags: PUBLIC (1)
                type: null
                props: array(
                    0: PropertyItem(
                        name: VarLikeIdentifier(
                            name: private
                        )
                        default: null
                    )
                )
                hooks: array(
                )
            )
            6: Stmt_ClassConst(
                attrGroups: array(
                )
                flags: 0
                type: null
                consts: array(
                    0: Const(
                        name: Identifier(
                            name: TRAIT
                        )
                        value: Scalar_Int(
                            value: 3
                        )
                    )
                    1: Const(
                        name: Identifier(
                            name: FINAL
                        )
                        value: Scalar_Int(
                            value: 4
                        )
                    )
                )
            )
            7: Stmt_ClassConst(
                attrGroups: array(
                )
                flags: 0
                type: null
                consts: array(
                    0: Const(
                        name: Identifier(
                            name: __CLASS__
                        )
                        value: Scalar_Int(
                            value: 1
                        )
                    )
                    1: Const(
                        name: Identifier(
                            name: __TRAIT__
                        )
                        value: Scalar_Int(
                            value: 2
                        )
                    )
                    2: Const(
                        name: Identifier(
                            name: __FUNCTION__
                        )
                        value: Scalar_Int(
                            value: 3
                        )
                    )
                    3: Const(
                        name: Identifier(
                            name: __METHOD__
                        )
                        value: Scalar_Int(
                            value: 4
                        )
                    )
                    4: Const(
                        name: Identifier(
                            name: __LINE__
                        )
                        value: Scalar_Int(
                            value: 5
                        )
                    )
                    5: Const(
                        name: Identifier(
                            name: __FILE__
                        )
                        value: Scalar_Int(
                            value: 6
                        )
                    )
                    6: Const(
                        name: Identifier(
                            name: __DIR__
                        )
                        value: Scalar_Int(
                            value: 7
                        )
                    )
                    7: Const(
                        name: Identifier(
                            name: __NAMESPACE__
                        )
                        value: Scalar_Int(
                            value: 8
                        )
                    )
                )
            )
            8: Stmt_Nop(
                comments: array(
                    0: // __halt_compiler does not work
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_Variable(
                name: t
            )
            expr: Expr_New(
                class: Name(
                    name: Test
                )
                args: array(
                )
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Expr_Variable(
                name: t
            )
            name: Identifier(
                name: array
            )
            args: array(
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Expr_Variable(
                name: t
            )
            name: Identifier(
                name: public
            )
            args: array(
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_StaticCall(
            class: Name(
                name: Test
            )
            name: Identifier(
                name: list
            )
            args: array(
            )
        )
    )
    5: Stmt_Expression(
        expr: Expr_StaticCall(
            class: Name(
                name: Test
            )
            name: Identifier(
                name: protected
            )
            args: array(
            )
        )
    )
    6: Stmt_Expression(
        expr: Expr_PropertyFetch(
            var: Expr_Variable(
                name: t
            )
            name: Identifier(
                name: class
            )
        )
    )
    7: Stmt_Expression(
        expr: Expr_PropertyFetch(
            var: Expr_Variable(
                name: t
            )
            name: Identifier(
                name: private
            )
        )
    )
    8: Stmt_Expression(
        expr: Expr_ClassConstFetch(
            class: Name(
                name: Test
            )
            name: Identifier(
                name: TRAIT
            )
        )
    )
    9: Stmt_Expression(
        expr: Expr_ClassConstFetch(
            class: Name(
                name: Test
            )
            name: Identifier(
                name: FINAL
            )
        )
    )
    10: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: Foo
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_TraitUse(
                traits: array(
                    0: Name(
                        name: TraitA
                    )
                    1: Name(
                        name: TraitB
                    )
                )
                adaptations: array(
                    0: Stmt_TraitUseAdaptation_Precedence(
                        trait: Name(
                            name: TraitA
                        )
                        method: Identifier(
                            name: catch
                        )
                        insteadof: array(
                            0: Name_Relative(
                                name: TraitB
                            )
                        )
                    )
                    1: Stmt_TraitUseAdaptation_Alias(
                        trait: Name(
                            name: TraitA
                        )
                        method: Identifier(
                            name: list
                        )
                        newModifier: null
                        newName: Identifier(
                            name: foreach
                        )
                    )
                    2: Stmt_TraitUseAdaptation_Alias(
                        trait: Name(
                            name: TraitB
                        )
                        method: Identifier(
                            name: throw
                        )
                        newModifier: PROTECTED (2)
                        newName: Identifier(
                            name: public
                        )
                    )
                    3: Stmt_TraitUseAdaptation_Alias(
                        trait: Name(
                            name: TraitB
                        )
                        method: Identifier(
                            name: self
                        )
                        newModifier: PROTECTED (2)
                        newName: null
                    )
                    4: Stmt_TraitUseAdaptation_Alias(
                        trait: null
                        method: Identifier(
                            name: exit
                        )
                        newModifier: null
                        newName: Identifier(
                            name: die
                        )
                    )
                    5: Stmt_TraitUseAdaptation_Alias(
                        trait: Name_FullyQualified(
                            name: TraitC
                        )
                        method: Identifier(
                            name: exit
                        )
                        newModifier: null
                        newName: Identifier(
                            name: bye
                        )
                    )
                    6: Stmt_TraitUseAdaptation_Alias(
                        trait: Name_Relative(
                            name: TraitC
                        )
                        method: Identifier(
                            name: exit
                        )
                        newModifier: null
                        newName: Identifier(
                            name: byebye
                        )
                    )
                    7: Stmt_TraitUseAdaptation_Precedence(
                        trait: Name(
                            name: TraitA
                        )
                        method: Identifier(
                            name: catch
                            comments: array(
                                0: //
                                1: /** doc comment */
                                2: #
                            )
                        )
                        insteadof: array(
                            0: Name(
                                name: TraitB
                            )
                        )
                    )
                )
            )
        )
    )
)
