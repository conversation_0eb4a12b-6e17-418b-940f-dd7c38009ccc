<?php
/**
 * Shared Settings Functions
 * Contains common functions used across multiple settings files
 */

/**
 * Get current settings from database
 * @return array Array of setting key-value pairs
 */
function getCurrentSettings() {
    $settingKeys = [
        // General
        'site_title', 'admin_title', 'site_description', 'site_keywords', 'organization_type',
        'organization_name', 'organization_mission', 'organization_vision', 'organization_values',
        'member_term', 'leader_term', 'group_term', 'event_term', 'donation_term', 'footer_text',
        // Contact
        'contact_phone', 'contact_email', 'contact_address', 'contact_city', 'contact_state',
        'contact_zip', 'contact_country', 'office_hours', 'emergency_contact',
        // Social Media
        'facebook_url', 'twitter_url', 'instagram_url', 'youtube_url', 'linkedin_url',
        'tiktok_url', 'website_url', 'blog_url',
        // Email
        'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption',
        'from_email', 'from_name', 'reply_to_email', 'email_signature', 'enable_email_queue',
        // System
        'timezone', 'date_format', 'time_format', 'currency_symbol', 'currency_code',
        'language', 'items_per_page', 'session_timeout', 'max_upload_size', 'backup_retention_days',
        // Notifications
        'enable_event_reminders', 'event_reminder_days', 'enable_membership_expiry_alerts',
        'membership_expiry_days', 'enable_admin_notifications', 'admin_notification_email',
        'notification_frequency', 'birthday_notification_days',
        // Integrations
        'google_analytics_id', 'facebook_pixel_id', 'google_maps_api_key', 'whatsapp_api_token',
        'sms_api_key', 'payment_gateway', 'stripe_public_key', 'stripe_secret_key',
        'paypal_client_id', 'enable_api_access'
    ];

    global $pdo;
    $settings = [];
    
    try {
        // Get all settings in one query for efficiency
        $placeholders = str_repeat('?,', count($settingKeys) - 1) . '?';
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ($placeholders)");
        $stmt->execute($settingKeys);
        
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        // Set defaults for missing settings
        foreach ($settingKeys as $key) {
            if (!isset($settings[$key])) {
                $settings[$key] = '';
            }
        }
        
    } catch (PDOException $e) {
        error_log("Error getting current settings: " . $e->getMessage());
        // Return empty array with defaults
        foreach ($settingKeys as $key) {
            $settings[$key] = '';
        }
    }
    
    return $settings;
}

/**
 * Update multiple settings in database
 * @param array $settings Array of setting key-value pairs
 * @return bool Success status
 */
function updateSettings($settings) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        $stmt = $pdo->prepare("
            INSERT INTO settings (setting_key, setting_value, updated_at) 
            VALUES (?, ?, NOW()) 
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value), 
            updated_at = VALUES(updated_at)
        ");
        
        foreach ($settings as $key => $value) {
            $stmt->execute([$key, $value]);
        }
        
        $pdo->commit();
        return true;
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Error updating settings: " . $e->getMessage());
        return false;
    }
}

/**
 * Get appearance settings with defaults
 * @return array Array of appearance setting key-value pairs
 */
function getAppearanceSettings() {
    $settingKeys = [
        'primary_color', 'secondary_color', 'success_color', 'danger_color', 'warning_color',
        'info_color', 'light_color', 'dark_color', 'background_color', 'text_color',
        'link_color', 'link_hover_color', 'primary_font', 'secondary_font', 'font_size_base',
        'font_weight_normal', 'font_weight_bold', 'line_height_base', 'heading_font_weight',
        'sidebar_style', 'navbar_style', 'card_style', 'border_radius', 'box_shadow',
        'container_max_width', 'sidebar_width', 'theme_mode', 'admin_theme', 'user_theme',
        'custom_css', 'enable_dark_mode', 'enable_theme_switcher',
        'sidebar_bg_color', 'sidebar_text_color', 'sidebar_hover_color', 'content_spacing'
    ];
    
    // Default values
    $defaults = [
        'sidebar_bg_color' => '#343a40',
        'sidebar_text_color' => '#ffffff',
        'sidebar_hover_color' => '#007bff',
        'content_spacing' => '30',
        'primary_color' => '#007bff',
        'secondary_color' => '#6c757d',
        'success_color' => '#28a745',
        'danger_color' => '#dc3545',
        'warning_color' => '#ffc107',
        'info_color' => '#17a2b8',
        'light_color' => '#f8f9fa',
        'dark_color' => '#343a40',
        'background_color' => '#ffffff',
        'text_color' => '#212529',
        'link_color' => '#007bff',
        'link_hover_color' => '#0056b3',
        'primary_font' => 'Inter',
        'font_size_base' => '16',
        'line_height_base' => '1.5',
        'border_radius' => '0.375',
        'container_max_width' => '1200',
        'sidebar_width' => '280'
    ];
    
    global $pdo;
    $settings = [];
    
    try {
        $placeholders = str_repeat('?,', count($settingKeys) - 1) . '?';
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ($placeholders)");
        $stmt->execute($settingKeys);
        
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        // Apply defaults for missing settings
        foreach ($settingKeys as $key) {
            if (!isset($settings[$key])) {
                $settings[$key] = $defaults[$key] ?? '';
            }
        }
        
    } catch (PDOException $e) {
        error_log("Error getting appearance settings: " . $e->getMessage());
        // Return defaults
        foreach ($settingKeys as $key) {
            $settings[$key] = $defaults[$key] ?? '';
        }
    }
    
    return $settings;
}
?>
