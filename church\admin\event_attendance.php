<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

$message = '';
$error = '';

// Handle attendance updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_attendance') {
    try {
        $event_id = (int)$_POST['event_id'];
        $attendance_data = $_POST['attendance'] ?? [];
        
        $conn->beginTransaction();
        
        // Update attendance for each user
        foreach ($attendance_data as $rsvp_id => $attended) {
            $actually_attended = ($attended === '1') ? 1 : 0;
            
            $stmt = $conn->prepare("
                UPDATE event_rsvps 
                SET actually_attended = ? 
                WHERE id = ? AND event_id = ?
            ");
            $stmt->execute([$actually_attended, $rsvp_id, $event_id]);
        }
        
        $conn->commit();
        $message = "Attendance records updated successfully!";
        
    } catch (Exception $e) {
        $conn->rollBack();
        $error = "Error updating attendance: " . $e->getMessage();
    }
}

// Include pagination component
require_once 'includes/pagination.php';

// Get pagination parameters
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = isset($_GET['limit']) ? max(10, min(100, intval($_GET['limit']))) : 20;

// Get total count of events
$count_query = "SELECT COUNT(*) FROM events";
$total_events = $conn->query($count_query)->fetchColumn();

// Calculate pagination
$pagination = calculate_pagination($total_events, $page, $limit);

// Ensure guest RSVP table has actually_attended column
try {
    $conn->exec("ALTER TABLE event_rsvps_guests ADD COLUMN actually_attended TINYINT(1) DEFAULT NULL");
} catch (PDOException $e) {
    // Column might already exist, ignore error
}

// Get events with RSVP counts (including guests) and pagination
$events_query = "
    SELECT
        e.id,
        e.title,
        e.event_date,
        e.location,
        e.is_active,
        (
            COALESCE(member_counts.attending_count, 0) +
            COALESCE(guest_counts.attending_count, 0)
        ) as attending_count,
        (
            COALESCE(member_counts.maybe_count, 0) +
            COALESCE(guest_counts.maybe_count, 0)
        ) as maybe_count,
        (
            COALESCE(member_counts.not_attending_count, 0) +
            COALESCE(guest_counts.not_attending_count, 0)
        ) as not_attending_count,
        (
            COALESCE(member_counts.actually_attended_count, 0) +
            COALESCE(guest_counts.actually_attended_count, 0)
        ) as actually_attended_count,
        (
            COALESCE(member_counts.attendance_marked_count, 0) +
            COALESCE(guest_counts.attendance_marked_count, 0)
        ) as attendance_marked_count
    FROM events e
    LEFT JOIN (
        SELECT
            event_id,
            COUNT(CASE WHEN status = 'attending' THEN 1 END) as attending_count,
            COUNT(CASE WHEN status = 'maybe' THEN 1 END) as maybe_count,
            COUNT(CASE WHEN status = 'not_attending' THEN 1 END) as not_attending_count,
            COUNT(CASE WHEN actually_attended = 1 THEN 1 END) as actually_attended_count,
            COUNT(CASE WHEN status = 'attending' AND actually_attended IS NOT NULL THEN 1 END) as attendance_marked_count
        FROM event_rsvps
        GROUP BY event_id
    ) member_counts ON e.id = member_counts.event_id
    LEFT JOIN (
        SELECT
            event_id,
            COUNT(CASE WHEN status = 'attending' THEN 1 END) as attending_count,
            COUNT(CASE WHEN status = 'maybe' THEN 1 END) as maybe_count,
            COUNT(CASE WHEN status = 'not_attending' THEN 1 END) as not_attending_count,
            COUNT(CASE WHEN actually_attended = 1 THEN 1 END) as actually_attended_count,
            COUNT(CASE WHEN status = 'attending' AND actually_attended IS NOT NULL THEN 1 END) as attendance_marked_count
        FROM event_rsvps_guests
        GROUP BY event_id
    ) guest_counts ON e.id = guest_counts.event_id
    ORDER BY e.event_date DESC
    LIMIT ? OFFSET ?
";

$stmt = $conn->prepare($events_query);
$stmt->bindParam(1, $limit, PDO::PARAM_INT);
$stmt->bindParam(2, $pagination['offset'], PDO::PARAM_INT);
$stmt->execute();
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Set page variables
$page_title = "Event Attendance Tracking";
$page_header = "Event Attendance Tracking";
$page_description = "Track actual attendance for events and manage RSVP data.";

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-check"></i> Events & Attendance Overview
                    </h5>
                    <div>
                        <a href="events.php" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-calendar-plus"></i> Manage Events
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Event</th>
                                    <th>Date</th>
                                    <th>Location</th>
                                    <th>RSVP Summary</th>
                                    <th>Attendance Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($events as $event): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($event['title']); ?></strong>
                                            <?php if (!$event['is_active']): ?>
                                                <span class="badge bg-secondary ms-2">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php 
                                            $event_date = new DateTime($event['event_date']);
                                            echo $event_date->format('M j, Y g:i A');
                                            ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($event['location'] ?? 'TBD'); ?></td>
                                        <td>
                                            <div class="d-flex gap-2">
                                                <span class="badge bg-success">
                                                    <?php echo $event['attending_count']; ?> Attending
                                                </span>
                                                <span class="badge bg-warning">
                                                    <?php echo $event['maybe_count']; ?> Maybe
                                                </span>
                                                <span class="badge bg-danger">
                                                    <?php echo $event['not_attending_count']; ?> Not Attending
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($event['attending_count'] > 0): ?>
                                                <div class="progress" style="height: 20px;">
                                                    <?php 
                                                    $attendance_percentage = $event['attending_count'] > 0 
                                                        ? ($event['attendance_marked_count'] / $event['attending_count']) * 100 
                                                        : 0;
                                                    ?>
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: <?php echo $attendance_percentage; ?>%">
                                                        <?php echo $event['attendance_marked_count']; ?>/<?php echo $event['attending_count']; ?> marked
                                                    </div>
                                                </div>
                                                <small class="text-muted">
                                                    <?php echo $event['actually_attended_count']; ?> actually attended
                                                </small>
                                            <?php else: ?>
                                                <span class="text-muted">No attendees</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($event['attending_count'] > 0): ?>
                                                <a href="event_attendance_detail.php?event_id=<?php echo $event['id']; ?>" 
                                                   class="btn btn-primary btn-sm">
                                                    <i class="bi bi-check-square"></i> Mark Attendance
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">No attendees to track</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                
                                <?php if (empty($events)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            <i class="bi bi-calendar-x fs-1 d-block mb-2"></i>
                                            No events found. <a href="events.php">Create your first event</a>.
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Enhanced Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                        <div class="card-footer">
                            <?php
                            // Set global variable for JavaScript
                            $GLOBALS['total_pages'] = $pagination['total_pages'];

                            echo generate_pagination(
                                $pagination['current_page'],
                                $pagination['total_pages'],
                                $pagination['total_records'],
                                $pagination['records_per_page'],
                                'event_attendance.php',
                                [], // No additional URL parameters to preserve
                                'page'
                            );
                            ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Pagination JavaScript
window.changePaginationLimit = function(newLimit) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('limit', newLimit);
    urlParams.set('page', '1'); // Reset to first page
    window.location.href = 'event_attendance.php?' + urlParams.toString();
};

window.goToPage = function(pageNumber) {
    const totalPages = <?php echo isset($pagination['total_pages']) ? $pagination['total_pages'] : 1; ?>;
    pageNumber = parseInt(pageNumber);

    if (pageNumber < 1 || pageNumber > totalPages || isNaN(pageNumber)) {
        alert('Please enter a valid page number between 1 and ' + totalPages);
        return;
    }

    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('page', pageNumber);
    window.location.href = 'event_attendance.php?' + urlParams.toString();
};
</script>

<?php include 'includes/footer.php'; ?>
