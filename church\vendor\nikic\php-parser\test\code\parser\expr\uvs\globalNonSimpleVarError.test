Non-simple variables are forbidden in PHP 7
-----
<?php
global $$foo->bar;
-----
Syntax error, unexpected T_OBJECT_OPERATOR, expecting ';' from 2:13 to 2:14
array(
    0: Stmt_Global(
        vars: array(
            0: Expr_Variable(
                name: Expr_Variable(
                    name: foo
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_ConstFetch(
            name: Name(
                name: bar
            )
        )
    )
)
