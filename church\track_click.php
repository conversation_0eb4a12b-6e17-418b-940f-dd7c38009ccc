<?php
/**
 * Email Click Tracking Script
 * 
 * This script tracks when a link in an email is clicked.
 * The tracking ID and target URL are passed as query parameters.
 */

// Check if tracking ID and URL are provided
if (!isset($_GET['id']) || empty($_GET['id']) || !isset($_GET['url']) || empty($_GET['url'])) {
    header('Location: register.php');
    exit;
}

// Include database connection
require_once 'config.php';

// Get tracking ID and URL
$trackingId = $_GET['id'];
$targetUrl = $_GET['url'];

try {
    // Update the tracking record
    $stmt = $pdo->prepare("
        UPDATE email_tracking 
        SET opened_count = opened_count + 1,
            opened_at = COALESCE(opened_at, NOW()),
            user_agent = ?, 
            ip_address = ?
        WHERE tracking_id = ?
    ");
    $stmt->execute([
        $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown', 
        $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0',
        $trackingId
    ]);
    
} catch (Exception $e) {
    // Log error but continue to redirect
    error_log("Error tracking email click: " . $e->getMessage());
}

// Redirect to the target URL
header('Location: ' . $targetUrl);
exit;
