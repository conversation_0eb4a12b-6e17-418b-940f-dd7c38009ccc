<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Include the configuration file
require_once '../../config.php';

// Get parameters
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 50;
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'name';
$order = isset($_GET['order']) ? $_GET['order'] : 'ASC';
$group_id = isset($_GET['group_id']) ? intval($_GET['group_id']) : null;

// Validate sort field
$allowed_sort_fields = ['name', 'email', 'created_at'];
if (!in_array($sort, $allowed_sort_fields)) {
    $sort = 'name';
}

// Validate order
$order = strtoupper($order) === 'DESC' ? 'DESC' : 'ASC';

try {
    // Calculate offset
    $offset = ($page - 1) * $limit;
    
    // Base query for paginated results
    $query = "SELECT DISTINCT c.*, GROUP_CONCAT(DISTINCT g.name ORDER BY g.name) as group_names 
              FROM contacts c 
              LEFT JOIN contact_group_members m ON c.id = m.contact_id 
              LEFT JOIN contact_groups g ON m.group_id = g.id";
    
    // Base query for total count
    $countQuery = "SELECT COUNT(DISTINCT c.id) as total FROM contacts c";
    
    // Add group filter if specified
    if ($group_id) {
        $query .= " WHERE EXISTS (
            SELECT 1 FROM contact_group_members gm 
            WHERE gm.contact_id = c.id AND gm.group_id = :group_id
        )";
        $countQuery .= " WHERE EXISTS (
            SELECT 1 FROM contact_group_members gm 
            WHERE gm.contact_id = c.id AND gm.group_id = :group_id
        )";
    }
    
    // Add grouping and sorting
    $query .= " GROUP BY c.id ORDER BY c." . $sort . " " . $order;
    
    // Get total count
    $stmt = $pdo->prepare($countQuery);
    if ($group_id) {
        $stmt->bindParam(':group_id', $group_id);
    }
    $stmt->execute();
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Get paginated results
    $stmt = $pdo->prepare($query . " LIMIT :offset, :limit");
    if ($group_id) {
        $stmt->bindParam(':group_id', $group_id);
    }
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    $contacts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get all contacts for bulk operations if requested
    $all_contacts = [];
    if (isset($_GET['get_all']) && $_GET['get_all'] === 'true') {
        // Limit the number of contacts to prevent memory exhaustion
        $maxAllowedContacts = 2000; // Set a reasonable limit
        
        $allQuery = "SELECT c.id, c.name, c.email FROM contacts c";
        if ($group_id) {
            $allQuery .= " WHERE EXISTS (
                SELECT 1 FROM contact_group_members gm 
                WHERE gm.contact_id = c.id AND gm.group_id = :group_id
            )";
        }
        $allQuery .= " ORDER BY c.name LIMIT :max_contacts";
        
        $stmt = $pdo->prepare($allQuery);
        if ($group_id) {
            $stmt->bindParam(':group_id', $group_id);
        }
        $stmt->bindParam(':max_contacts', $maxAllowedContacts, PDO::PARAM_INT);
        $stmt->execute();
        $all_contacts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Add a flag if we hit the maximum limit
        $limited = count($all_contacts) >= $maxAllowedContacts;
    }
    
    // Process contacts to include groups as array
    foreach ($contacts as &$contact) {
        $contact['groups'] = $contact['group_names'] ? explode(',', $contact['group_names']) : [];
        unset($contact['group_names']);
    }
    
    // Calculate pagination info
    $total_pages = ceil($total / $limit);
    $pagination = [
        'current' => $page,
        'pages' => $total_pages,
        'start' => $offset + 1,
        'end' => min($offset + $limit, $total),
        'total' => $total
    ];
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'contacts' => $contacts,
        'all_contacts' => $all_contacts,
        'all_contacts_limited' => $limited ?? false,
        'pagination' => $pagination
    ]);
    
} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} 