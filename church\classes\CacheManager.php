<?php

class CacheManager {
    private $cacheDir;
    private $defaultTTL = 3600; // 1 hour default TTL
    
    public function __construct($cacheDir = null) {
        $this->cacheDir = $cacheDir ?? dirname(__DIR__) . '/cache';
        $this->ensureCacheDirectory();
    }
    
    private function ensureCacheDirectory() {
        if (!file_exists($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Ensure assets cache directory exists
        if (!file_exists($this->cacheDir . '/assets')) {
            mkdir($this->cacheDir . '/assets', 0755, true);
        }
    }
    
    public function set($key, $data, $ttl = null) {
        $ttl = $ttl ?? $this->defaultTTL;
        $cacheFile = $this->getCacheFilePath($key);
        
        $cacheData = [
            'data' => $data,
            'expires' => time() + $ttl
        ];
        
        return file_put_contents($cacheFile, serialize($cacheData)) !== false;
    }
    
    public function get($key) {
        $cacheFile = $this->getCacheFilePath($key);
        
        if (!file_exists($cacheFile)) {
            return null;
        }
        
        $cacheData = unserialize(file_get_contents($cacheFile));
        
        if ($cacheData['expires'] < time()) {
            unlink($cacheFile);
            return null;
        }
        
        return $cacheData['data'];
    }
    
    public function delete($key) {
        $cacheFile = $this->getCacheFilePath($key);
        if (file_exists($cacheFile)) {
            return unlink($cacheFile);
        }
        return true;
    }
    
    public function clear() {
        $files = glob($this->cacheDir . '/*.cache');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        return true;
    }
    
    private function getCacheFilePath($key) {
        return $this->cacheDir . '/' . preg_replace('/[^a-zA-Z0-9_]/', '_', $key) . '.cache';
    }
    
    // Template specific caching methods
    public function getTemplate($templateId) {
        return $this->get('template_' . $templateId);
    }
    
    public function setTemplate($templateId, $content, $ttl = 86400) { // 24 hours for templates
        return $this->set('template_' . $templateId, $content, $ttl);
    }
    
    // Settings specific caching methods
    public function getSettings($type = 'notification') {
        return $this->get('settings_' . $type);
    }
    
    public function setSettings($settings, $type = 'notification', $ttl = 3600) {
        return $this->set('settings_' . $type, $settings, $ttl);
    }
    
    // Asset caching methods
    public function cacheAsset($assetPath, $content, $ttl = 604800) { // 1 week for assets
        $assetKey = 'assets_' . md5($assetPath);
        return $this->set($assetKey, $content, $ttl);
    }
    
    public function getCachedAsset($assetPath) {
        $assetKey = 'assets_' . md5($assetPath);
        return $this->get($assetKey);
    }
} 