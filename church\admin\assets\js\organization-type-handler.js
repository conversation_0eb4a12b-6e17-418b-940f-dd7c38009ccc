/**
 * Organization Type Handler
 * 
 * Automatically updates terminology fields when organization type changes
 * Provides smart defaults based on the selected organization type
 */

document.addEventListener('DOMContentLoaded', function() {
    const organizationTypeSelect = document.getElementById('organization_type');
    
    if (!organizationTypeSelect) {
        return; // Not on the settings page
    }
    
    // Define terminology presets for different organization types
    const terminologyPresets = {
        church: {
            member_term: 'Member',
            leader_term: 'Pastor',
            group_term: 'Ministry',
            event_term: 'Service',
            donation_term: 'Offering'
        },
        school: {
            member_term: 'Student',
            leader_term: 'Principal',
            group_term: 'Class',
            event_term: 'Assembly',
            donation_term: 'Fee'
        },
        business: {
            member_term: 'Employee',
            leader_term: 'Manager',
            group_term: 'Department',
            event_term: 'Meeting',
            donation_term: 'Contribution'
        },
        nonprofit: {
            member_term: 'Member',
            leader_term: 'Director',
            group_term: 'Committee',
            event_term: 'Event',
            donation_term: 'Donation'
        },
        association: {
            member_term: 'Member',
            leader_term: 'President',
            group_term: 'Committee',
            event_term: 'Meeting',
            donation_term: 'Dues'
        },
        club: {
            member_term: 'Member',
            leader_term: 'President',
            group_term: 'Group',
            event_term: 'Meeting',
            donation_term: 'Fee'
        },
        community: {
            member_term: 'Participant',
            leader_term: 'Coordinator',
            group_term: 'Group',
            event_term: 'Gathering',
            donation_term: 'Contribution'
        },
        other: {
            member_term: 'Member',
            leader_term: 'Leader',
            group_term: 'Group',
            event_term: 'Event',
            donation_term: 'Donation'
        }
    };
    
    // Get all terminology input fields
    const terminologyFields = {
        member_term: document.getElementById('member_term'),
        leader_term: document.getElementById('leader_term'),
        group_term: document.getElementById('group_term'),
        event_term: document.getElementById('event_term'),
        donation_term: document.getElementById('donation_term')
    };
    
    // Function to update terminology fields
    function updateTerminology(organizationType, forceUpdate = false) {
        if (!terminologyPresets[organizationType]) {
            return;
        }
        
        const preset = terminologyPresets[organizationType];
        
        Object.keys(terminologyFields).forEach(fieldName => {
            const field = terminologyFields[fieldName];
            if (field && (forceUpdate || field.value.trim() === '')) {
                field.value = preset[fieldName];
                
                // Add visual feedback
                field.style.backgroundColor = '#e8f5e8';
                setTimeout(() => {
                    field.style.backgroundColor = '';
                }, 1000);
            }
        });
    }
    
    // Function to show confirmation dialog for terminology update
    function confirmTerminologyUpdate(organizationType) {
        const preset = terminologyPresets[organizationType];
        if (!preset) return;
        
        // Check if any fields have existing values
        const hasExistingValues = Object.keys(terminologyFields).some(fieldName => {
            const field = terminologyFields[fieldName];
            return field && field.value.trim() !== '';
        });
        
        if (hasExistingValues) {
            const message = `Would you like to update the terminology fields with ${organizationType} defaults?\n\n` +
                          `This will set:\n` +
                          `• Member Term: ${preset.member_term}\n` +
                          `• Leader Term: ${preset.leader_term}\n` +
                          `• Group Term: ${preset.group_term}\n` +
                          `• Event Term: ${preset.event_term}\n` +
                          `• Donation Term: ${preset.donation_term}`;
            
            if (confirm(message)) {
                updateTerminology(organizationType, true);
            }
        } else {
            // No existing values, update automatically
            updateTerminology(organizationType, true);
        }
    }
    
    // Handle organization type change
    organizationTypeSelect.addEventListener('change', function() {
        const selectedType = this.value;
        
        if (selectedType && terminologyPresets[selectedType]) {
            confirmTerminologyUpdate(selectedType);
        }
    });
    
    // Add helpful tooltips
    const tooltips = {
        organization_type: 'This affects the default terminology and features throughout the system',
        member_term: 'What do you call your members? (e.g., Members, Students, Employees, Participants)',
        leader_term: 'What do you call your leaders? (e.g., Pastor, Principal, Manager, President)',
        group_term: 'What do you call your groups? (e.g., Ministry, Department, Team, Committee)',
        event_term: 'What do you call your events? (e.g., Service, Class, Meeting, Assembly)',
        donation_term: 'What do you call donations/payments? (e.g., Offering, Donation, Fee, Dues)'
    };
    
    // Add tooltips to fields
    Object.keys(tooltips).forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.setAttribute('title', tooltips[fieldId]);
        }
    });
    
    // Add preview functionality
    function createPreviewSection() {
        const previewContainer = document.createElement('div');
        previewContainer.className = 'mt-4 p-3 bg-light border rounded';
        previewContainer.innerHTML = `
            <h6 class="mb-2">Preview</h6>
            <p class="small text-muted mb-2">See how your terminology will appear:</p>
            <div id="terminology-preview" class="small">
                <div><strong>Sample text:</strong> <span id="preview-text">Configure your organization settings above to see a preview.</span></div>
            </div>
        `;
        
        // Insert after the terminology section
        const donationTermField = document.getElementById('donation_term');
        if (donationTermField && donationTermField.closest('.col-lg-6')) {
            donationTermField.closest('.col-lg-6').appendChild(previewContainer);
        }
        
        return previewContainer;
    }
    
    // Update preview text
    function updatePreview() {
        const previewText = document.getElementById('preview-text');
        if (!previewText) return;
        
        const orgName = document.getElementById('organization_name').value || '[Organization Name]';
        const memberTerm = terminologyFields.member_term.value || 'Member';
        const leaderTerm = terminologyFields.leader_term.value || 'Leader';
        const groupTerm = terminologyFields.group_term.value || 'Group';
        const eventTerm = terminologyFields.event_term.value || 'Event';
        const donationTerm = terminologyFields.donation_term.value || 'Donation';
        
        const sampleText = `Welcome to ${orgName}! As a new ${memberTerm}, you'll work with our ${leaderTerm} and ${groupTerm} leaders to participate in ${eventTerm}s. Your ${donationTerm}s help support our mission.`;
        
        previewText.textContent = sampleText;
    }
    
    // Create preview section
    createPreviewSection();
    
    // Add event listeners for preview updates
    Object.values(terminologyFields).forEach(field => {
        if (field) {
            field.addEventListener('input', updatePreview);
        }
    });
    
    const orgNameField = document.getElementById('organization_name');
    if (orgNameField) {
        orgNameField.addEventListener('input', updatePreview);
    }
    
    // Initial preview update
    updatePreview();
    
    // Add reset button for terminology
    function addResetButton() {
        const resetButton = document.createElement('button');
        resetButton.type = 'button';
        resetButton.className = 'btn btn-outline-secondary btn-sm mt-2';
        resetButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Reset to Defaults';
        resetButton.title = 'Reset terminology to defaults for the selected organization type';
        
        resetButton.addEventListener('click', function() {
            const selectedType = organizationTypeSelect.value;
            if (selectedType && terminologyPresets[selectedType]) {
                if (confirm('Reset all terminology fields to defaults for ' + selectedType + '?')) {
                    updateTerminology(selectedType, true);
                    updatePreview();
                }
            } else {
                alert('Please select an organization type first.');
            }
        });
        
        // Add after the donation term field
        const donationTermField = document.getElementById('donation_term');
        if (donationTermField) {
            donationTermField.parentNode.appendChild(resetButton);
        }
    }
    
    addResetButton();
});
