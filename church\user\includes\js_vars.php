<?php
/**
 * JavaScript Variables Include for User Pages
 * 
 * This file provides dynamic JavaScript variables for user pages
 * Include this file in user pages that need dynamic URLs for AJAX calls
 */

// Ensure config is loaded
if (!function_exists('get_base_url')) {
    require_once '../config.php';
}
?>

<!-- Dynamic JavaScript Variables for User Pages -->
<script>
    // Base URLs for JavaScript use
    var BASE_URL = '<?php echo get_base_url(); ?>';
    var SITE_URL = '<?php echo SITE_URL; ?>';
    var USER_URL = '<?php echo get_base_url(); ?>/user';
    var ADMIN_URL = '<?php echo get_admin_url(); ?>';
    var ASSETS_URL = '<?php echo ASSETS_URL; ?>';
    var UPLOADS_URL = '<?php echo UPLOADS_URL; ?>';
    
    // Helper functions for building URLs
    function buildUrl(path) {
        return BASE_URL + '/' + path.replace(/^\/+/, '');
    }
    
    function buildUserUrl(path) {
        return USER_URL + '/' + path.replace(/^\/+/, '');
    }
    
    function buildAjaxUrl(path) {
        return buildUserUrl('ajax/' + path.replace(/^\/+/, ''));
    }
    
    function buildAssetUrl(path) {
        return ASSETS_URL + '/' + path.replace(/^\/+/, '');
    }
    
    function buildUploadUrl(path) {
        return UPLOADS_URL + '/' + path.replace(/^\/+/, '');
    }
    
    // Debug information (only in development)
    <?php if (defined('ENVIRONMENT') && ENVIRONMENT === 'development'): ?>
    console.log('User Page JavaScript Variables Loaded:');
    console.log('BASE_URL:', BASE_URL);
    console.log('SITE_URL:', SITE_URL);
    console.log('USER_URL:', USER_URL);
    console.log('ADMIN_URL:', ADMIN_URL);
    console.log('ASSETS_URL:', ASSETS_URL);
    console.log('UPLOADS_URL:', UPLOADS_URL);
    <?php endif; ?>
</script>
