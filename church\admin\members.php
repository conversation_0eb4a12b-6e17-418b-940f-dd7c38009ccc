<?php
session_start();

// Include language system
require_once 'includes/language.php';

// Language manager is automatically initialized in language.php as global $lang

/**
 * Format and validate birth date
 * @param string|null $birthDate The birth date to format
 * @return string Formatted date or 'N/A'
 */
function formatBirthDate($birthDate) {
    global $lang;
    if (empty($birthDate)) {
        return __('no_data_available');
    }

    try {
        $date = new DateTime($birthDate);
        return $date->format('M d, Y');
    } catch (Exception $e) {
        error_log("Error formatting birth date: " . $e->getMessage());
        return __('no_data_available');
    }
}

/**
 * Fix member term setting if it's empty
 */
function ensureMemberTermSetting() {
    global $pdo;

    try {
        // Check if member_term exists and has a value
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'member_term'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$result || empty(trim($result['setting_value']))) {
            // Insert or update member_term with default value
            $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('member_term', 'Member')
                                  ON DUPLICATE KEY UPDATE setting_value = 'Member'");
            $stmt->execute();
            error_log("Fixed empty member_term setting - set to 'Member'");
        }
    } catch (PDOException $e) {
        error_log("Error fixing member_term setting: " . $e->getMessage());
    }
}

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Generate or retrieve CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Fix member term setting if it's empty (causing sidebar to show "s" instead of "Members")
ensureMemberTermSetting();

// Delete member
if (isset($_GET['delete_id']) && !empty($_GET['delete_id'])) {
    if (!isset($_GET['csrf_token']) || $_GET['csrf_token'] !== $_SESSION['csrf_token']) {
        $error_message = __('invalid_security_token');
    } else {
        $delete_id = intval($_GET['delete_id']);

        try {
            $conn->beginTransaction();

            // Helper function to check if table exists
            $checkTable = function($tableName) use ($conn) {
                try {
                    $stmt = $conn->query("SHOW TABLES LIKE '$tableName'");
                    return $stmt->rowCount() > 0;
                } catch (PDOException $e) {
                    return false;
                }
            };

            // Helper function to check if column exists in table
            $checkColumn = function($tableName, $columnName) use ($conn) {
                try {
                    $stmt = $conn->query("SHOW COLUMNS FROM $tableName LIKE '$columnName'");
                    return $stmt->rowCount() > 0;
                } catch (PDOException $e) {
                    return false;
                }
            };

            // Delete from email_logs (if exists)
            if ($checkTable('email_logs') && $checkColumn('email_logs', 'member_id')) {
                try {
                    $stmt = $conn->prepare("DELETE FROM email_logs WHERE member_id = ?");
                    $stmt->execute([$delete_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from email_tracking if it exists
            if ($checkTable('email_tracking') && $checkColumn('email_tracking', 'member_id')) {
                try {
                    $stmt = $conn->prepare("DELETE FROM email_tracking WHERE member_id = ?");
                    $stmt->execute([$delete_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Handle volunteer opportunities - set contact_person_id to NULL instead of deleting
            if ($checkTable('volunteer_opportunities') && $checkColumn('volunteer_opportunities', 'contact_person_id')) {
                try {
                    $stmt = $conn->prepare("UPDATE volunteer_opportunities SET contact_person_id = NULL WHERE contact_person_id = ?");
                    $stmt->execute([$delete_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from volunteer_applications if member applied for opportunities
            if ($checkTable('volunteer_applications') && $checkColumn('volunteer_applications', 'member_id')) {
                try {
                    $stmt = $conn->prepare("DELETE FROM volunteer_applications WHERE member_id = ?");
                    $stmt->execute([$delete_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from event_rsvps if member has RSVPs
            if ($checkTable('event_rsvps') && $checkColumn('event_rsvps', 'member_id')) {
                try {
                    $stmt = $conn->prepare("DELETE FROM event_rsvps WHERE member_id = ?");
                    $stmt->execute([$delete_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from member_skills if table exists
            if ($checkTable('member_skills') && $checkColumn('member_skills', 'member_id')) {
                try {
                    $stmt = $conn->prepare("DELETE FROM member_skills WHERE member_id = ?");
                    $stmt->execute([$delete_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from family_relationships if table exists
            if ($checkTable('family_relationships') && $checkColumn('family_relationships', 'member_id')) {
                try {
                    $stmt = $conn->prepare("DELETE FROM family_relationships WHERE member_id = ? OR related_member_id = ?");
                    $stmt->execute([$delete_id, $delete_id]);
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Delete from member_gifts if table exists
            if ($checkTable('member_gifts')) {
                try {
                    if ($checkColumn('member_gifts', 'sender_id')) {
                        $stmt = $conn->prepare("DELETE FROM member_gifts WHERE sender_id = ? OR recipient_id = ?");
                        $stmt->execute([$delete_id, $delete_id]);
                    } else if ($checkColumn('member_gifts', 'recipient_id')) {
                        $stmt = $conn->prepare("DELETE FROM member_gifts WHERE recipient_id = ?");
                        $stmt->execute([$delete_id]);
                    }
                } catch (PDOException $e) {
                    // Continue if error
                }
            }

            // Now delete the member
            $stmt = $conn->prepare("DELETE FROM members WHERE id = ?");
            if ($stmt->execute([$delete_id])) {
                $conn->commit();
                $_SESSION['message'] = __('member_deleted_successfully');
                // Redirect to prevent resubmission
                header('Location: members.php');
                exit();
            } else {
                $conn->rollBack();
                $error_message = __('error_deleting_member') . ": " . implode(", ", $stmt->errorInfo());
            }
        } catch (Exception $e) {
            $conn->rollBack();
            $error_message = __('error') . ": " . $e->getMessage();
        }
    }
}

// Include pagination component
require_once 'includes/pagination.php';

// Set search parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$gender_filter = isset($_GET['gender']) ? trim($_GET['gender']) : '';

// Pagination setup
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$records_per_page = isset($_GET['limit']) ? max(10, min(100, intval($_GET['limit']))) : 20;

// Count total records for pagination
$count_query = "SELECT COUNT(*) as total FROM members";
$count_params = [];
$where_conditions = [];

if (!empty($search)) {
    $where_conditions[] = "(full_name LIKE ? OR email LIKE ? OR phone_number LIKE ?)";
    $search_param = "%$search%";
    $count_params = array_merge($count_params, [$search_param, $search_param, $search_param]);
}

if (!empty($gender_filter)) {
    $where_conditions[] = "gender = ?";
    $count_params[] = $gender_filter;
}

if (!empty($where_conditions)) {
    $count_query .= " WHERE " . implode(" AND ", $where_conditions);
}

$stmt = $conn->prepare($count_query);
$stmt->execute($count_params);
$count_result = $stmt->fetch();
$total_records = $count_result['total'];

// Calculate pagination
$pagination = calculate_pagination($total_records, $page, $records_per_page);
$offset = $pagination['offset'];

// Get members with pagination and search
$query = "SELECT
    id,
    full_name,
    email,
    phone_number,
    CASE
        WHEN birth_date IS NOT NULL THEN birth_date
        WHEN date_of_birth IS NOT NULL THEN date_of_birth
        ELSE NULL
    END as birth_date,
    gender,
    created_at,
    image_path
FROM members";

$params = [];
$where_conditions = [];

if (!empty($search)) {
    $where_conditions[] = "(full_name LIKE ? OR email LIKE ? OR phone_number LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
}

if (!empty($gender_filter)) {
    $where_conditions[] = "gender = ?";
    $params[] = $gender_filter;
}

if (!empty($where_conditions)) {
    $query .= " WHERE " . implode(" AND ", $where_conditions);
}
$query .= " ORDER BY created_at DESC LIMIT ?, ?";

try {
    $stmt = $conn->prepare($query);

    // Bind parameters correctly
    if (!empty($params)) {
        // First bind the search parameters
        foreach ($params as $index => $value) {
            $stmt->bindValue($index + 1, $value, PDO::PARAM_STR);
        }
        // Then bind the pagination parameters
        $stmt->bindValue(count($params) + 1, $offset, PDO::PARAM_INT);
        $stmt->bindValue(count($params) + 2, $records_per_page, PDO::PARAM_INT);
    } else {
        // If no search parameters, just bind the pagination parameters
        $stmt->bindValue(1, $offset, PDO::PARAM_INT);
        $stmt->bindValue(2, $records_per_page, PDO::PARAM_INT);
    }

    $stmt->execute();
    $members = $stmt->fetchAll();
} catch (PDOException $e) {
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    $error = "Error: " . $e->getMessage();
    $members = [];
}

// Close the database connection
$conn = null;

// Set page variables
$page_title = __('members');
$page_header = __('members');
$page_description = __('manage_members_description');

// Include header
include 'includes/header.php';

// Display flash message if it exists
if (isset($_SESSION['message']) && !empty($_SESSION['message'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($_SESSION['message']) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
    // Clear the message after displaying it
    unset($_SESSION['message']);
}

// Display success message if it exists (local variable)
if (!empty($success_message)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($success_message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

// Display error message if it exists
if (!empty($error_message)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error_message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}
?>

<!-- Instructions panel -->
<div class="alert alert-info mb-4 instruction-panel">
    <div class="d-flex justify-content-between align-items-start">
        <h5><i class="bi bi-info-circle-fill me-2"></i><?php _e('managing_members'); ?></h5>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="<?php _e('close'); ?>"></button>
    </div>
    <p class="mb-2"><?php _e('members_page_description'); ?></p>
    <ul class="mb-0">
        <li><strong><?php _e('add_members'); ?>:</strong> <?php _e('add_members_description'); ?></li>
        <li><strong><?php _e('view_details'); ?>:</strong> <?php _e('view_details_description'); ?></li>
        <li><strong><?php _e('edit_information'); ?>:</strong> <?php _e('edit_information_description'); ?></li>
        <li><strong><?php _e('birthday_communications'); ?>:</strong> <?php _e('birthday_communications_description'); ?></li>
    </ul>
    <div class="mt-2">
        <strong><?php _e('tip'); ?>:</strong> <?php _e('search_tip_description'); ?>
    </div>
</div>

<!-- Enhanced Search and Filter Section -->
<div class="card search-filter-card shadow-sm mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-lg-8 col-md-7">
                <!-- Search form -->
                <form action="" method="GET" class="row g-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="bi bi-search text-muted"></i>
                            </span>
                            <input type="text" name="search" class="form-control"
                                   placeholder="<?php _e('search_members_placeholder'); ?>"
                                   value="<?php echo htmlspecialchars($search); ?>"
                                   data-bs-toggle="tooltip" data-bs-placement="top"
                                   title="<?php _e('search_by_name_email_phone'); ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="bi bi-funnel text-muted"></i>
                            </span>
                            <select name="gender" class="form-select" data-bs-toggle="tooltip" data-bs-placement="top" title="Filter by gender">
                                <option value="">All Genders</option>
                                <option value="Male" <?php echo ($gender_filter === 'Male') ? 'selected' : ''; ?>>
                                    <i class="bi bi-person"></i> Male
                                </option>
                                <option value="Female" <?php echo ($gender_filter === 'Female') ? 'selected' : ''; ?>>
                                    <i class="bi bi-person-dress"></i> Female
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary flex-fill">
                                <i class="bi bi-search me-1"></i><?php _e('search'); ?>
                            </button>
                            <?php if (!empty($search) || !empty($gender_filter)): ?>
                                <a href="members.php" class="btn btn-outline-secondary" data-bs-toggle="tooltip" title="Clear all filters">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-lg-4 col-md-5 text-end mt-3 mt-md-0">
                <div class="d-flex justify-content-end align-items-center gap-3">
                    <!-- Bulk Actions -->
                    <div id="bulkActionsContainer" style="display: none;">
                        <div class="btn-group shadow-sm" role="group">
                            <button type="button" class="btn btn-sm btn-danger" id="bulkDeleteBtn">
                                <i class="bi bi-trash me-1"></i><?php _e('delete_selected'); ?> (<span id="selectedCount">0</span>)
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="clearSelectionBtn">
                                <i class="bi bi-x-circle me-1"></i><?php _e('clear'); ?>
                            </button>
                        </div>
                    </div>
                    <!-- Add New Member Button -->
                    <a href="add_member.php" class="btn btn-success shadow-sm" data-bs-toggle="tooltip" data-bs-placement="left" title="<?php _e('add_new_member_tooltip'); ?>">
                        <i class="bi bi-person-plus me-1"></i><?php _e('add_new_member'); ?>
                    </a>
                </div>
            </div>
        </div>

        <!-- Active Filters Display -->
        <?php if (!empty($search) || !empty($gender_filter)): ?>
        <div class="mt-3 pt-3 border-top">
            <div class="d-flex align-items-center flex-wrap gap-2">
                <small class="text-muted me-2">
                    <i class="bi bi-funnel-fill me-1"></i>Active filters:
                </small>
                <?php if (!empty($search)): ?>
                    <span class="badge bg-primary-subtle text-primary border border-primary-subtle">
                        <i class="bi bi-search me-1"></i>Search: "<?php echo htmlspecialchars($search); ?>"
                        <a href="?<?php echo !empty($gender_filter) ? 'gender=' . urlencode($gender_filter) : ''; ?>" class="text-primary ms-1 text-decoration-none">
                            <i class="bi bi-x"></i>
                        </a>
                    </span>
                <?php endif; ?>
                <?php if (!empty($gender_filter)): ?>
                    <span class="badge bg-info-subtle text-info border border-info-subtle">
                        <i class="bi bi-<?php echo ($gender_filter === 'Male') ? 'person' : 'person-dress'; ?> me-1"></i>Gender: <?php echo htmlspecialchars($gender_filter); ?>
                        <a href="?<?php echo !empty($search) ? 'search=' . urlencode($search) : ''; ?>" class="text-info ms-1 text-decoration-none">
                            <i class="bi bi-x"></i>
                        </a>
                    </span>
                <?php endif; ?>
                <a href="members.php" class="btn btn-sm btn-outline-secondary ms-auto">
                    <i class="bi bi-arrow-clockwise me-1"></i>Clear All
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Members Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($members) > 0): ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <input type="checkbox" class="form-check-input" id="selectAllMembers" title="<?php _e('select_all'); ?>">
                        </th>
                        <th><?php _e('id'); ?></th>
                        <th><?php _e('photo'); ?></th>
                        <th><?php _e('name'); ?></th>
                        <th><?php _e('email'); ?></th>
                        <th><?php _e('phone'); ?></th>
                        <th><?php _e('birth_date'); ?></th>
                        <th><?php _e('gender'); ?></th>
                        <th><?php _e('joined'); ?></th>
                        <th><?php _e('actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($members as $member): ?>
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input member-checkbox"
                                   value="<?php echo $member['id']; ?>"
                                   data-name="<?php echo htmlspecialchars($member['full_name']); ?>"
                                   data-email="<?php echo htmlspecialchars($member['email']); ?>">
                        </td>
                        <td><?php echo $member['id']; ?></td>
                        <td>
                            <?php
                            // Fix image path - ensure it works correctly
                            $imagePath = '../assets/img/default-profile.jpg'; // Default fallback

                            if (!empty($member['image_path'])) {
                                $dbImagePath = $member['image_path'];

                                // Build the correct path for admin pages
                                if (strpos($dbImagePath, 'uploads/') === 0) {
                                    // Path is 'uploads/profiles/filename.jpg' - add '../'
                                    $relativePath = '../' . $dbImagePath;
                                } elseif (strpos($dbImagePath, '/uploads/') === 0) {
                                    // Path is '/uploads/profiles/filename.jpg' - add '..'
                                    $relativePath = '..' . $dbImagePath;
                                } else {
                                    // Legacy format - assume it needs uploads prefix
                                    $relativePath = '../uploads/profiles/' . basename($dbImagePath);
                                }

                                // Check if file exists using correct server path
                                $serverPath = __DIR__ . '/' . $relativePath;
                                if (file_exists($serverPath)) {
                                    $imagePath = $relativePath;
                                }
                                // If file doesn't exist, keep default fallback
                            }
                            ?>
                            <img src="<?php echo htmlspecialchars($imagePath); ?>?v=<?php echo time(); ?>"
                                 alt="<?php echo htmlspecialchars($member['full_name']); ?>"
                                 class="rounded-circle member-photo"
                                 style="width: 40px; height: 40px; object-fit: cover;"
                                 onerror="this.src='../assets/img/default-profile.jpg';"
                                 title="<?php echo htmlspecialchars($member['full_name']); ?>">
                        </td>
                        <td><?php echo htmlspecialchars($member['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($member['email']); ?></td>
                        <td><?php echo htmlspecialchars($member['phone_number'] ?? __('no_data_available')); ?></td>
                        <td>
                            <?php
                            echo formatBirthDate($member['birth_date']);
                            ?>
                        </td>
                        <td>
                            <?php if (!empty($member['gender'])): ?>
                                <span class="badge bg-<?php echo ($member['gender'] === 'Male') ? 'primary' : 'info'; ?>">
                                    <?php echo htmlspecialchars($member['gender']); ?>
                                </span>
                            <?php else: ?>
                                <span class="text-muted"><?php _e('not_specified'); ?></span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($member['created_at'])); ?></td>
                        <td>
                            <a href="view_member.php?id=<?php echo $member['id']; ?>" class="btn btn-sm btn-info" title="<?php _e('view_details'); ?>">
                                <i class="bi bi-eye"></i>
                            </a>
                            <a href="edit_member.php?id=<?php echo $member['id']; ?>" class="btn btn-sm btn-primary" title="<?php _e('edit_member'); ?>">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <a href="#" onclick="confirmDelete(<?php echo $member['id']; ?>, '<?php echo addslashes($member['full_name']); ?>')" class="btn btn-sm btn-danger" title="<?php _e('delete_member'); ?>">
                                <i class="bi bi-trash"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Enhanced Pagination -->
        <?php if ($pagination['total_pages'] > 1): ?>
            <?php
            // Set global variable for JavaScript
            $GLOBALS['total_pages'] = $pagination['total_pages'];

            // Generate pagination with search parameter preservation
            $url_params = [];
            if (!empty($search)) {
                $url_params['search'] = $search;
            }

            echo generate_pagination(
                $pagination['current_page'],
                $pagination['total_pages'],
                $pagination['total_records'],
                $pagination['records_per_page'],
                'members.php',
                $url_params,
                'page'
            );
            ?>
        <?php endif; ?>
        
        <?php else: ?>
        <p class="text-center"><?php _e('no_members_found'); ?></p>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel"><?php _e('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="<?php _e('close'); ?>"></button>
            </div>
            <div class="modal-body">
                <?php _e('confirm_delete_member_message'); ?> <strong><span id="memberNameToDelete"></span></strong>? <?php _e('action_cannot_be_undone'); ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e('cancel'); ?></button>
                <a href="#" id="confirmDeleteButton" class="btn btn-danger"><?php _e('delete'); ?></a>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle me-2"></i><?php _e('confirm_bulk_member_deletion'); ?>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h6><i class="bi bi-shield-exclamation me-2"></i>⚠️ <?php _e('danger_action_cannot_be_undone'); ?></h6>
                    <p class="mb-0"><?php _e('bulk_delete_warning_message'); ?></p>
                </div>

                <div class="mb-3">
                    <h6><?php _e('selected_members'); ?> (<span id="bulkDeleteCount">0</span>):</h6>
                    <div id="bulkDeletePreview" class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                        <!-- Member list will be populated here -->
                    </div>
                </div>

                <div class="mb-3">
                    <h6><?php _e('what_will_be_deleted'); ?>:</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check text-danger me-2"></i><?php _e('member_records'); ?></li>
                        <li><i class="bi bi-check text-danger me-2"></i><?php _e('profile_images'); ?></li>
                        <li><i class="bi bi-check text-danger me-2"></i><?php _e('email_logs_tracking_data'); ?></li>
                        <li><i class="bi bi-check text-danger me-2"></i><?php _e('all_associated_data'); ?></li>
                    </ul>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="confirmBulkDelete">
                    <label class="form-check-label text-danger fw-bold" for="confirmBulkDelete">
                        <?php _e('understand_permanent_action'); ?>
                    </label>
                </div>

                <div class="mb-3">
                    <label for="bulkDeleteConfirmText" class="form-label">
                        <?php _e('type_delete_to_confirm'); ?> <strong><?php _e('delete'); ?></strong>:
                    </label>
                    <input type="text" class="form-control" id="bulkDeleteConfirmText" placeholder="<?php _e('type_delete_placeholder'); ?>">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e('cancel'); ?></button>
                <button type="button" class="btn btn-danger" id="confirmBulkDeleteBtn" disabled>
                    <i class="bi bi-trash me-2"></i><?php _e('delete_selected_members'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Global modal backdrop cleanup function
    function cleanupModalBackdrops() {
        // Remove any orphaned modal backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            backdrop.remove();
        });

        // Remove modal-open class from body
        document.body.classList.remove('modal-open');

        // Reset body styles
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }

    function confirmDelete(memberId, memberName) {
        // Clean up any existing backdrops first
        cleanupModalBackdrops();

        const modal = new bootstrap.Modal(document.getElementById('deleteModal'), {
            backdrop: false,  // Disable backdrop completely
            keyboard: true    // Allow Escape key to close
        });
        document.getElementById('memberNameToDelete').textContent = memberName;
        document.getElementById('confirmDeleteButton').href = `members.php?delete_id=${memberId}&csrf_token=<?php echo $_SESSION['csrf_token']; ?>`;
        modal.show();
    }

    // Global modal event listeners for backdrop cleanup
    document.addEventListener('hidden.bs.modal', function(e) {
        // Clean up any orphaned backdrops when any modal is hidden
        setTimeout(() => {
            cleanupModalBackdrops();
        }, 100);
    });

    // Auto-search functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Clean up any existing backdrops on page load
        cleanupModalBackdrops();
        const searchInput = document.getElementById('search');
        const searchForm = document.getElementById('searchForm');
        const searchSpinner = document.getElementById('searchSpinner');
        let searchTimeout = null;

        searchInput.addEventListener('input', function() {
            // Show the spinner
            searchSpinner.classList.remove('d-none');
            
            // Clear any existing timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            // Set a new timeout to delay the search
            searchTimeout = setTimeout(function() {
                // Submit the form after the user stops typing for 500ms
                searchForm.submit();
            }, 500);
        });

        // Add click handler for member photos to show larger version
        document.querySelectorAll('.member-photo').forEach(photo => {
            photo.addEventListener('click', function() {
                // Clean up any existing backdrops first
                cleanupModalBackdrops();

                // Create modal element properly
                const modalElement = document.createElement('div');
                modalElement.className = 'modal fade';
                modalElement.tabIndex = -1;
                modalElement.innerHTML = `
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body text-center p-0">
                                <img src="${this.src}" class="img-fluid" style="max-height: 80vh;">
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modalElement);

                const modal = new bootstrap.Modal(modalElement);
                modal.show();

                modalElement.addEventListener('hidden.bs.modal', () => {
                    modalElement.remove();
                    cleanupModalBackdrops();
                });
            });
        });
    });

    // Bulk Delete Functionality for Members
    let selectedMembers = [];

    // Handle select all checkbox
    const selectAllCheckbox = document.getElementById('selectAllMembers');
    const memberCheckboxes = document.querySelectorAll('.member-checkbox');
    const bulkActionsContainer = document.getElementById('bulkActionsContainer');
    const selectedCountSpan = document.getElementById('selectedCount');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const clearSelectionBtn = document.getElementById('clearSelectionBtn');

    // Select All functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            memberCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateSelectedMembers();
        });
    }

    // Individual checkbox change
    memberCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedMembers);
    });

    // Update selected members array and UI
    function updateSelectedMembers() {
        selectedMembers = [];
        const checkedBoxes = document.querySelectorAll('.member-checkbox:checked');

        checkedBoxes.forEach(checkbox => {
            selectedMembers.push({
                id: checkbox.value,
                name: checkbox.dataset.name,
                email: checkbox.dataset.email
            });
        });

        // Update UI
        if (selectedMembers.length > 0) {
            bulkActionsContainer.style.display = 'inline-block';
            selectedCountSpan.textContent = selectedMembers.length;
        } else {
            bulkActionsContainer.style.display = 'none';
        }

        // Update select all checkbox state
        if (selectAllCheckbox) {
            if (selectedMembers.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (selectedMembers.length === memberCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        }
    }

    // Clear selection - Fixed functionality
    if (clearSelectionBtn) {
        clearSelectionBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Clear all member checkboxes
            document.querySelectorAll('.member-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });

            // Clear select all checkbox
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }

            // Update the UI
            updateSelectedMembers();

            // Hide bulk actions container
            if (bulkActionsContainer) {
                bulkActionsContainer.style.display = 'none';
            }
        });
    }

    // Bulk delete button click
    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', function() {
            if (selectedMembers.length === 0) {
                alert('Please select members to delete.');
                return;
            }

            // Populate modal with selected members
            const bulkDeleteCount = document.getElementById('bulkDeleteCount');
            const bulkDeletePreview = document.getElementById('bulkDeletePreview');

            bulkDeleteCount.textContent = selectedMembers.length;

            let previewHtml = '<div class="row">';
            selectedMembers.forEach((member, index) => {
                if (index > 0 && index % 2 === 0) {
                    previewHtml += '</div><div class="row">';
                }
                previewHtml += `
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-person-circle me-2 text-muted"></i>
                            <div>
                                <div class="fw-bold">${escapeHtml(member.name)}</div>
                                <small class="text-muted">${escapeHtml(member.email)}</small>
                            </div>
                        </div>
                    </div>
                `;
            });
            previewHtml += '</div>';

            bulkDeletePreview.innerHTML = previewHtml;

            // Clean up any existing backdrops first
            cleanupModalBackdrops();

            // Show modal
            const bulkDeleteModal = new bootstrap.Modal(document.getElementById('bulkDeleteModal'));
            bulkDeleteModal.show();
        });
    }

    // Modal confirmation logic
    const confirmCheckbox = document.getElementById('confirmBulkDelete');
    const confirmTextInput = document.getElementById('bulkDeleteConfirmText');
    const confirmButton = document.getElementById('confirmBulkDeleteBtn');

    function updateConfirmButton() {
        const isChecked = confirmCheckbox && confirmCheckbox.checked;
        const isTextCorrect = confirmTextInput && confirmTextInput.value.toUpperCase() === 'DELETE';

        if (confirmButton) {
            confirmButton.disabled = !(isChecked && isTextCorrect);
        }
    }

    if (confirmCheckbox) {
        confirmCheckbox.addEventListener('change', updateConfirmButton);
    }

    if (confirmTextInput) {
        confirmTextInput.addEventListener('input', updateConfirmButton);
    }

    // Confirm bulk delete
    if (confirmButton) {
        confirmButton.addEventListener('click', function() {
            if (selectedMembers.length === 0) {
                alert('No members selected.');
                return;
            }

            // Disable button and show loading
            confirmButton.disabled = true;
            confirmButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Deleting...';

            // Prepare data
            const memberIds = selectedMembers.map(member => member.id);

            // Send AJAX request
            fetch('ajax/bulk_delete_members.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    member_ids: memberIds,
                    confirmation_token: 'BULK_DELETE_CONFIRMED'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showNotification(data.message, 'success');

                    // Show file cleanup info
                    if (data.file_cleanup) {
                        showNotification(data.file_cleanup, 'info');
                    }

                    // Show detailed results if available
                    if (data.results && data.results.failed_deletions && data.results.failed_deletions.length > 0) {
                        console.warn('Some deletions failed:', data.results.failed_deletions);
                        showNotification(data.warnings || 'Some members could not be deleted', 'warning');
                    }

                    // Close modal and reload page
                    const modal = bootstrap.Modal.getInstance(document.getElementById('bulkDeleteModal'));
                    modal.hide();

                    // Clean up backdrops
                    cleanupModalBackdrops();

                    // Reload page after short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);

                } else {
                    showNotification(data.message || 'Error occurred during bulk deletion', 'error');

                    // Re-enable button
                    confirmButton.disabled = false;
                    confirmButton.innerHTML = '<i class="bi bi-trash me-2"></i>Delete Selected Members';
                }
            })
            .catch(error => {
                console.error('Bulk delete error:', error);
                showNotification('Network error occurred during bulk deletion', 'error');

                // Re-enable button
                confirmButton.disabled = false;
                confirmButton.innerHTML = '<i class="bi bi-trash me-2"></i>Delete Selected Members';
            });
        });
    }

    // Reset modal when closed
    const bulkDeleteModal = document.getElementById('bulkDeleteModal');
    if (bulkDeleteModal) {
        bulkDeleteModal.addEventListener('hidden.bs.modal', function() {
            // Reset form
            if (confirmCheckbox) confirmCheckbox.checked = false;
            if (confirmTextInput) confirmTextInput.value = '';
            if (confirmButton) {
                confirmButton.disabled = true;
                confirmButton.innerHTML = '<i class="bi bi-trash me-2"></i>Delete Selected Members';
            }

            // Clean up any orphaned backdrops
            cleanupModalBackdrops();
        });
    }

    // Utility function for HTML escaping
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Notification function
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Pagination JavaScript
    window.changePaginationLimit = function(newLimit) {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('limit', newLimit);
        urlParams.set('page', '1'); // Reset to first page
        window.location.href = 'members.php?' + urlParams.toString();
    };

    window.goToPage = function(pageNumber) {
        const totalPages = <?php echo isset($pagination['total_pages']) ? $pagination['total_pages'] : 1; ?>;
        pageNumber = parseInt(pageNumber);

        if (pageNumber < 1 || pageNumber > totalPages || isNaN(pageNumber)) {
            alert('Please enter a valid page number between 1 and ' + totalPages);
            return;
        }

        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('page', pageNumber);
        window.location.href = 'members.php?' + urlParams.toString();
    };
</script>