<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../config.php';

// Get opportunity ID
$opportunity_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$opportunity_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid opportunity ID']);
    exit();
}

try {
    // Get opportunity details
    $stmt = $pdo->prepare("
        SELECT * FROM volunteer_opportunities 
        WHERE id = ?
    ");
    $stmt->execute([$opportunity_id]);
    $opportunity = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$opportunity) {
        echo json_encode(['success' => false, 'message' => 'Opportunity not found']);
        exit();
    }
    
    echo json_encode([
        'success' => true,
        'opportunity' => $opportunity
    ]);
    
} catch (PDOException $e) {
    error_log("Get opportunity error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
