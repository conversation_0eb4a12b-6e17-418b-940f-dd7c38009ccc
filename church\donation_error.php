<?php
require_once 'config.php';

$error_message = "We're sorry, but there was an error processing your donation. ";

switch ($_GET['error'] ?? '') {
    case 'payment_cancelled':
        $error_message .= "The payment was cancelled.";
        break;
    case 'payment_failed':
        $error_message .= "The payment could not be completed. Please try again or contact us for assistance.";
        break;
    default:
        $error_message .= "Please try again or contact us for assistance.";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Donation Error - <?php echo htmlspecialchars(get_organization_name()); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <?php include 'navbar.php'; ?>
    
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-md-8 text-center">
                <i class="bi bi-exclamation-circle-fill error-icon mb-4"></i>
                <h1 class="mb-4">Donation Error</h1>
                
                <div class="alert alert-danger mb-5">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
                
                <div class="mt-4">
                    <a href="donate.php" class="btn btn-primary me-3">Try Again</a>
                    <a href="contact.php" class="btn btn-outline-primary">Contact Us</a>
                </div>
            </div>
        </div>
    </div>
    
    <?php include 'footer.php'; ?>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 