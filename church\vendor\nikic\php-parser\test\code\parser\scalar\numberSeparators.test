Different integer syntaxes
-----
<?php

6.674_083e-11;
299_792_458;
0x7AFE_F00D;
0b0101_1111;
0137_041;

// already a valid constant name
_100;

// syntax errors
100_;
1__1;
1_.0;
1._0;
0x_123;
0b_101;
1_e2;
1e_2;
-----
Syntax error, unexpected T_STRING from 13:4 to 13:4
Syntax error, unexpected T_STRING from 14:2 to 14:4
Syntax error, unexpected T_STRING from 15:2 to 15:2
Syntax error, unexpected T_STRING from 16:3 to 16:4
Syntax error, unexpected T_STRING from 17:2 to 17:6
Syntax error, unexpected T_STRING from 18:2 to 18:6
Syntax error, unexpected T_STRING from 19:2 to 19:4
Syntax error, unexpected T_STRING from 20:2 to 20:4
array(
    0: Stmt_Expression(
        expr: Scalar_Float(
            value: 6.674083E-11
        )
    )
    1: Stmt_Expression(
        expr: <PERSON>alar_Int(
            value: *********
        )
    )
    2: Stmt_Expression(
        expr: Scalar_Int(
            value: 2063527949
        )
    )
    3: Stmt_Expression(
        expr: Scalar_Int(
            value: 95
        )
    )
    4: Stmt_Expression(
        expr: Scalar_Int(
            value: 48673
        )
    )
    5: Stmt_Expression(
        expr: Expr_ConstFetch(
            name: Name(
                name: _100
            )
        )
        comments: array(
            0: // already a valid constant name
        )
    )
    6: Stmt_Expression(
        expr: Scalar_Int(
            value: 100
        )
        comments: array(
            0: // syntax errors
        )
    )
    7: Stmt_Expression(
        expr: Expr_ConstFetch(
            name: Name(
                name: _
            )
        )
    )
    8: Stmt_Expression(
        expr: Scalar_Int(
            value: 1
        )
    )
    9: Stmt_Expression(
        expr: Expr_ConstFetch(
            name: Name(
                name: __1
            )
        )
    )
    10: Stmt_Expression(
        expr: Scalar_Int(
            value: 1
        )
    )
    11: Stmt_Expression(
        expr: Expr_ConstFetch(
            name: Name(
                name: _
            )
        )
    )
    12: Stmt_Expression(
        expr: Scalar_Float(
            value: 0
        )
    )
    13: Stmt_Expression(
        expr: Scalar_Float(
            value: 1
        )
    )
    14: Stmt_Expression(
        expr: Expr_ConstFetch(
            name: Name(
                name: _0
            )
        )
    )
    15: Stmt_Expression(
        expr: Scalar_Int(
            value: 0
        )
    )
    16: Stmt_Expression(
        expr: Expr_ConstFetch(
            name: Name(
                name: x_123
            )
        )
    )
    17: Stmt_Expression(
        expr: Scalar_Int(
            value: 0
        )
    )
    18: Stmt_Expression(
        expr: Expr_ConstFetch(
            name: Name(
                name: b_101
            )
        )
    )
    19: Stmt_Expression(
        expr: Scalar_Int(
            value: 1
        )
    )
    20: Stmt_Expression(
        expr: Expr_ConstFetch(
            name: Name(
                name: _e2
            )
        )
    )
    21: Stmt_Expression(
        expr: Scalar_Int(
            value: 1
        )
    )
    22: Stmt_Expression(
        expr: Expr_ConstFetch(
            name: Name(
                name: e_2
            )
        )
    )
)
