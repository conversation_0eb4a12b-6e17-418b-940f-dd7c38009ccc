<?php
/**
 * Donation System
 *
 * Comprehensive donation system with birthday gifts, digital cards, scheduling, and organization integration
 */

// Start session for user authentication
session_start();

require_once 'config.php';

// Check if user is logged in (optional - allow both logged in and anonymous donations)
$isLoggedIn = isset($_SESSION['user_id']);
$userData = null;

if ($isLoggedIn) {
    // Get user data from members table
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $userData = $stmt->fetch(PDO::FETCH_ASSOC);
}

// Get site settings for branding
$sitename = get_organization_name() . ' - Donation';
$organization_name = get_organization_name();
require_once 'includes/email_functions.php';

// Get organization information
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('organization_name', 'organization_type', 'sender_name', 'sender_email')");
$stmt->execute();
$org_settings = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $org_settings[$row['setting_key']] = $row['setting_value'];
}

// Default organization info if not set
$organization_name = $org_settings['organization_name'] ?? get_organization_name();
$organization_type = $org_settings['organization_type'] ?? 'church';
$sender_name = $org_settings['sender_name'] ?? $organization_name;
$sender_email = $org_settings['sender_email'] ?? '<EMAIL>';

// Get payment settings
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM payment_settings");
$stmt->execute();
$payment_settings = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $payment_settings[$row['setting_key']] = $row['setting_value'];
}

// Check if donations are enabled
$donations_enabled = isset($payment_settings['donations_enabled']) && $payment_settings['donations_enabled'] === '1';
$birthday_gifts_enabled = isset($payment_settings['birthday_gifts_enabled']) && $payment_settings['birthday_gifts_enabled'] === '1';
$enhanced_donations_enabled = isset($payment_settings['enhanced_donations_enabled']) && $payment_settings['enhanced_donations_enabled'] === '1';

// Get currency settings
$default_currency = $payment_settings['default_currency'] ?? 'USD';
$minimum_donation = $payment_settings['minimum_donation_amount'] ?? '5';

$currencies = [
    'USD' => ['name' => 'US Dollar', 'symbol' => '$'],
    'EUR' => ['name' => 'Euro', 'symbol' => '€'],
    'GBP' => ['name' => 'British Pound', 'symbol' => '£'],
    'ZAR' => ['name' => 'South African Rand', 'symbol' => 'R'],
    'NGN' => ['name' => 'Nigerian Naira', 'symbol' => '₦'],
    'KES' => ['name' => 'Kenyan Shilling', 'symbol' => 'KSh'],
    'UGX' => ['name' => 'Ugandan Shilling', 'symbol' => 'USh'],
    'GHS' => ['name' => 'Ghanaian Cedi', 'symbol' => 'GH₵'],
    'CAD' => ['name' => 'Canadian Dollar', 'symbol' => 'C$'],
    'AUD' => ['name' => 'Australian Dollar', 'symbol' => 'A$']
];

$currency_symbol = $currencies[$default_currency]['symbol'] ?? '$';

// Get upcoming birthday members (next 30 days)
$birthday_members = [];
if ($birthday_gifts_enabled) {
    $stmt = $pdo->prepare("
        SELECT id, full_name, birth_date, email
        FROM members 
        WHERE birth_date IS NOT NULL 
        AND (
            (MONTH(birth_date) = MONTH(CURRENT_DATE) AND DAY(birth_date) >= DAY(CURRENT_DATE))
            OR 
            (MONTH(birth_date) = MONTH(DATE_ADD(CURRENT_DATE, INTERVAL 1 MONTH)) 
             AND DAY(birth_date) <= DAY(DATE_ADD(CURRENT_DATE, INTERVAL 30 DAY)))
        )
        ORDER BY 
            CASE 
                WHEN MONTH(birth_date) = MONTH(CURRENT_DATE) THEN DAY(birth_date)
                ELSE DAY(birth_date) + 31
            END
    ");
    $stmt->execute();
    $birthday_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get gift templates
$gift_templates = [];
if ($enhanced_donations_enabled) {
    $stmt = $pdo->prepare("SELECT * FROM gift_templates WHERE is_active = 1 ORDER BY template_category, template_name");
    $stmt->execute();
    $gift_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'user/includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .donation-container {
            margin-top: 2rem;
        }

        .donation-header {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            border-radius: var(--bs-border-radius, 15px);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
            text-align: center;
        }

        .donation-header h1 {
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .donation-header p {
            opacity: 0.9;
            margin: 0;
        }

        .donation-card {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 15px);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .donation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .donation-card h5 {
            color: var(--bs-body-color, #2c3e50);
            font-weight: 600;
            margin-bottom: 1rem;
        }
        .donation-option {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            border-radius: var(--bs-border-radius, 10px);
        }
        .donation-option:hover {
            border-color: var(--bs-primary, #007bff);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(var(--bs-primary-rgb, 0, 123, 255), 0.2);
        }
        .donation-option.selected {
            border-color: var(--bs-primary, #007bff);
            background-color: rgba(var(--bs-primary-rgb, 0, 123, 255), 0.1);
        }
        .gift-type-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid var(--bs-border-color, #e9ecef);
            border-radius: var(--bs-border-radius, 10px);
        }
        .gift-type-card:hover {
            border-color: var(--bs-primary, #007bff);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(var(--bs-primary-rgb, 0, 123, 255), 0.2);
        }
        .gift-type-card.selected {
            border-color: var(--bs-primary, #007bff);
            background-color: rgba(var(--bs-primary-rgb, 0, 123, 255), 0.1);
        }
        .template-preview {
            max-height: 200px;
            overflow: hidden;
            border: 1px solid var(--bs-border-color, #dee2e6);
            border-radius: var(--bs-border-radius, 8px);
            padding: 10px;
            background: var(--bs-gray-100, #f8f9fa);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0.5rem 1rem;
        }
        .step-number {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: var(--bs-gray-600, #6c757d);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .step.active .step-number {
            background: var(--bs-primary, #007bff);
            transform: scale(1.1);
        }
        .step.completed .step-number {
            background: var(--bs-success, #28a745);
        }
        .step-text {
            font-weight: 500;
            color: var(--bs-body-color, #212529);
        }
        .form-section {
            display: none;
        }
        .form-section.active {
            display: block;
        }

        /* Additional responsive improvements */
        @media (max-width: 576px) {
            .donation-container {
                margin-top: 1rem;
            }

            .donation-header {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .donation-header h1 {
                font-size: 1.75rem;
            }

            .step-indicator {
                margin-bottom: 1.5rem;
            }

            .step {
                margin: 0.25rem 0.5rem;
            }

            .step-number {
                width: 30px;
                height: 30px;
                font-size: 0.875rem;
            }

            .step-text {
                font-size: 0.875rem;
            }
        }

        /* Form styling improvements */
        .form-control, .form-select {
            border-radius: var(--bs-border-radius, 8px);
            border: 1px solid var(--bs-border-color, #dee2e6);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--bs-primary, #007bff);
            box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb, 0, 123, 255), 0.25);
        }

        .btn {
            border-radius: var(--bs-border-radius, 8px);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php if ($isLoggedIn): ?>
        <?php include 'user/includes/navbar.php'; ?>
    <?php else: ?>
        <!-- Simple navbar for anonymous users -->
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand" href="register.php">
                    <?php
                    // Use the existing logo management system
                    $headerLogo = get_site_setting('header_logo', '');
                    $mainLogo = get_site_setting('main_logo', '');
                    $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;

                    if (!empty($logoToUse)): ?>
                        <img src="<?php echo get_base_url() . '/' . htmlspecialchars($logoToUse); ?>"
                             alt="<?php echo get_organization_name(); ?>"
                             class="navbar-logo me-2">
                        <?php echo htmlspecialchars(get_organization_name()); ?>
                    <?php else: ?>
                        <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($organization_name); ?>
                    <?php endif; ?>
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="user/login.php" style="color: rgba(255,255,255,0.9);">
                        <i class="bi bi-box-arrow-in-right"></i> Login
                    </a>
                </div>
            </div>
        </nav>
    <?php endif; ?>

    <div class="container donation-container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <!-- Donation Header -->
                <div class="donation-header">
                    <h1><i class="bi bi-heart-fill"></i> Donation System</h1>
                    <p class="lead">Support <?php echo htmlspecialchars($organization_name); ?> with flexible giving options</p>
                </div>

                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step active" id="step-1">
                        <div class="step-number">1</div>
                        <span class="step-text">Type</span>
                    </div>
                    <div class="step" id="step-2">
                        <div class="step-number">2</div>
                        <span class="step-text">Details</span>
                    </div>
                    <div class="step" id="step-3">
                        <div class="step-number">3</div>
                        <span class="step-text">Amount</span>
                    </div>
                    <div class="step" id="step-4">
                        <div class="step-number">4</div>
                        <span class="step-text">Payment</span>
                    </div>
                </div>

                <?php if (!$donations_enabled): ?>
                    <div class="alert alert-warning">
                        <h4>Donations are currently disabled</h4>
                        <p>The donation system is currently disabled. Please contact the administrator for more information.</p>
                    </div>
                <?php else: ?>
                    <form id="enhanced-donation-form" action="process_enhanced_donation.php" method="POST">
                        <!-- Step 1: Donation Type Selection -->
                        <div class="form-section active" id="section-1">
                            <div class="donation-card">
                                <h5>Select Donation Type</h5>
                                    <div class="row g-3">
                                        <!-- General Donation -->
                                        <div class="col-md-6">
                                            <div class="gift-type-card donation-option h-100 p-3" data-type="general">
                                                <div class="text-center">
                                                    <i class="bi bi-heart-fill" style="font-size: 2rem; color: var(--bs-primary);"></i>
                                                    <h5 class="mt-2">General Donation</h5>
                                                    <p class="text-muted">Support our mission and <?php echo strtolower(get_group_term(true)); ?></p>
                                                </div>
                                            </div>
                                        </div>

                                        <?php if ($birthday_gifts_enabled && !empty($birthday_members)): ?>
                                        <!-- Birthday Gift -->
                                        <div class="col-md-6">
                                            <div class="gift-type-card donation-option h-100 p-3" data-type="birthday_gift">
                                                <div class="text-center">
                                                    <i class="bi bi-gift-fill" style="font-size: 2rem; color: var(--bs-warning);"></i>
                                                    <h5 class="mt-2">Birthday Gift</h5>
                                                    <p class="text-muted">Send a special gift to celebrate someone's birthday</p>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <input type="hidden" name="donation_type" id="donation_type" required>
                                    
                                    <div class="text-center mt-4">
                                        <button type="button" class="btn btn-primary btn-lg" id="next-step-1" disabled>
                                            Next <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                            </div>
                        </div>

                        <!-- Step 2: Gift Details (for birthday gifts) -->
                        <div class="form-section" id="section-2">
                            <div class="donation-card">
                                <h5>Gift Details</h5>

                                    <!-- Birthday Member Selection -->
                                    <div id="birthday-member-section" style="display: none;">
                                        <div class="mb-4">
                                            <label for="recipient_id" class="form-label">Select Birthday Celebrant</label>
                                            <select class="form-select" name="recipient_id" id="recipient_id">
                                                <option value="">Choose a member...</option>
                                                <?php foreach ($birthday_members as $member): ?>
                                                    <?php
                                                    $birth_date = new DateTime($member['birth_date']);
                                                    $formatted_date = $birth_date->format('M d');
                                                    ?>
                                                    <option value="<?php echo $member['id']; ?>">
                                                        <?php echo htmlspecialchars($member['full_name'] . ' (' . $formatted_date . ')'); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <!-- Gift Type Selection -->
                                        <div class="mb-4">
                                            <label class="form-label">Gift Type</label>
                                            <div class="row g-3">
                                                <div class="col-md-4">
                                                    <div class="card gift-option" data-gift-type="monetary">
                                                        <div class="card-body text-center">
                                                            <i class="bi bi-cash-coin text-success" style="font-size: 2rem;"></i>
                                                            <h6 class="mt-2">Monetary Gift</h6>
                                                            <p class="small text-muted">Direct financial gift</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="card gift-option" data-gift-type="digital_card">
                                                        <div class="card-body text-center">
                                                            <i class="bi bi-card-image text-primary" style="font-size: 2rem;"></i>
                                                            <h6 class="mt-2">Digital Card</h6>
                                                            <p class="small text-muted">Beautiful digital greeting card</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="card gift-option" data-gift-type="gift_card">
                                                        <div class="card-body text-center">
                                                            <i class="bi bi-gift text-warning" style="font-size: 2rem;"></i>
                                                            <h6 class="mt-2">Gift Card</h6>
                                                            <p class="small text-muted">Digital gift card</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <input type="hidden" name="gift_type" id="gift_type">
                                        </div>

                                        <!-- Template Selection (for digital cards) -->
                                        <div id="template-selection" class="mb-4" style="display: none;">
                                            <label class="form-label">Choose Card Template</label>
                                            <div class="row g-3">
                                                <?php foreach ($gift_templates as $template): ?>
                                                <div class="col-md-6">
                                                    <div class="card template-option" data-template-id="<?php echo $template['id']; ?>">
                                                        <div class="card-body">
                                                            <h6><?php echo htmlspecialchars($template['template_name']); ?></h6>
                                                            <div class="template-preview">
                                                                <?php echo $template['template_content']; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                            <input type="hidden" name="gift_template_id" id="gift_template_id">
                                        </div>

                                        <!-- Delivery Options -->
                                        <div class="mb-4">
                                            <label class="form-label">Delivery Options</label>
                                            <div class="row g-3">
                                                <div class="col-md-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="delivery_method" id="immediate" value="immediate" checked>
                                                        <label class="form-check-label" for="immediate">
                                                            <strong>Immediate</strong><br>
                                                            <small class="text-muted">Send right away</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="delivery_method" id="birthday_morning" value="scheduled">
                                                        <label class="form-check-label" for="birthday_morning">
                                                            <strong>On Birthday</strong><br>
                                                            <small class="text-muted">Deliver on their birthday</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="delivery_method" id="custom_schedule" value="scheduled">
                                                        <label class="form-check-label" for="custom_schedule">
                                                            <strong>Custom Date</strong><br>
                                                            <small class="text-muted">Choose specific date</small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Custom Delivery Date -->
                                        <div id="custom-date-section" class="mb-4" style="display: none;">
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="scheduled_delivery_date" class="form-label">Delivery Date</label>
                                                    <input type="date" class="form-control" name="scheduled_delivery_date" id="scheduled_delivery_date">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="scheduled_delivery_time" class="form-label">Delivery Time</label>
                                                    <input type="time" class="form-control" name="scheduled_delivery_time" id="scheduled_delivery_time" value="09:00">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Personal Message -->
                                        <div class="mb-4">
                                            <label for="sender_message" class="form-label">Personal Message</label>
                                            <textarea class="form-control" name="sender_message" id="sender_message" rows="4" placeholder="Write a personal birthday message..."></textarea>
                                        </div>

                                        <!-- Anonymous Gift Option -->
                                        <div class="mb-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="anonymous_gift" id="anonymous_gift" value="1">
                                                <label class="form-check-label" for="anonymous_gift">
                                                    Send this gift anonymously
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary btn-lg" id="prev-step-2">
                                            <i class="bi bi-arrow-left"></i> Previous
                                        </button>
                                        <button type="button" class="btn btn-primary btn-lg" id="next-step-2">
                                            Next <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                            </div>
                        </div>

                        <!-- Step 3: Amount Selection -->
                        <div class="form-section" id="section-3">
                            <div class="donation-card">
                                <h5>Select Amount</h5>
                                    <div class="row g-3 mb-4">
                                        <div class="col-6 col-md-3">
                                            <div class="donation-option text-center p-3" data-amount="10">
                                                <h4><?php echo $currency_symbol; ?>10</h4>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <div class="donation-option text-center p-3" data-amount="25">
                                                <h4><?php echo $currency_symbol; ?>25</h4>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <div class="donation-option text-center p-3" data-amount="50">
                                                <h4><?php echo $currency_symbol; ?>50</h4>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <div class="donation-option text-center p-3" data-amount="100">
                                                <h4><?php echo $currency_symbol; ?>100</h4>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="custom-amount" class="form-label">Or enter custom amount</label>
                                        <div class="input-group">
                                            <span class="input-group-text currency-symbol"><?php echo $currency_symbol; ?></span>
                                            <input type="number" class="form-control" id="custom-amount" name="amount" min="<?php echo $minimum_donation; ?>" step="0.01" placeholder="0.00" required>
                                        </div>
                                        <div class="form-text">Minimum donation: <?php echo $currency_symbol . $minimum_donation; ?></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="currency" class="form-label">Currency</label>
                                        <select class="form-select" id="currency" name="currency">
                                            <?php foreach ($currencies as $code => $details): ?>
                                                <option value="<?php echo $code; ?>" <?php echo $code === $default_currency ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($code . ' - ' . $details['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary btn-lg" id="prev-step-3">
                                            <i class="bi bi-arrow-left"></i> Previous
                                        </button>
                                        <button type="button" class="btn btn-primary btn-lg" id="next-step-3">
                                            Next <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                            </div>
                        </div>

                        <!-- Step 4: Donor Information & Payment -->
                        <div class="form-section" id="section-4">
                            <div class="donation-card">
                                <h5>Your Information</h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="donor_name" class="form-label">Full Name</label>
                                            <input type="text" class="form-control" id="donor_name" name="donor_name"
                                                   value="<?php echo $isLoggedIn && $userData ? htmlspecialchars($userData['full_name']) : ''; ?>" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="donor_email" class="form-label">Email Address</label>
                                            <input type="email" class="form-control" id="donor_email" name="donor_email"
                                                   value="<?php echo $isLoggedIn && $userData ? htmlspecialchars($userData['email']) : ''; ?>" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="sender_organization" class="form-label">Organization (Optional)</label>
                                            <input type="text" class="form-control" id="sender_organization" name="sender_organization" placeholder="Your organization name">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="sender_department" class="form-label">Department/Role (Optional)</label>
                                            <input type="text" class="form-control" id="sender_department" name="sender_department" placeholder="Your department or role">
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <label for="general_message" class="form-label">Message (Optional)</label>
                                        <textarea class="form-control" id="general_message" name="message" rows="3" placeholder="Add a message with your donation..."></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Method -->
                            <div class="card mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">Payment Method</h5>
                                    <div class="row g-3">
                                        <?php
                                        // Check if any payment methods are enabled
                                        $paypal_enabled = isset($payment_settings['paypal_enabled']) && $payment_settings['paypal_enabled'] === '1';
                                        $stripe_enabled = isset($payment_settings['stripe_enabled']) && $payment_settings['stripe_enabled'] === '1';

                                        // If no payment methods are enabled, default to PayPal
                                        if (!$paypal_enabled && !$stripe_enabled) {
                                            $paypal_enabled = true;
                                        }

                                        if ($paypal_enabled):
                                        ?>
                                        <div class="col-md-6">
                                            <div class="payment-method-option donation-option h-100 p-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="payment_method" id="paypal" value="paypal" checked>
                                                    <label class="form-check-label" for="paypal">
                                                        <img src="assets/images/paypal-logo.png" alt="PayPal" height="30" onerror="this.onerror=null; this.src='assets/images/default-logo.png'; this.alt='PayPal';">
                                                        PayPal
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <?php if ($stripe_enabled): ?>
                                        <div class="col-md-6">
                                            <div class="payment-method-option donation-option h-100 p-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="payment_method" id="stripe" value="stripe" <?php echo !$paypal_enabled ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="stripe">
                                                        <img src="assets/images/stripe-logo.png" alt="Stripe" height="30" onerror="this.onerror=null; this.src='assets/images/default-logo.png'; this.alt='Stripe';">
                                                        Stripe
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-secondary btn-lg" id="prev-step-4">
                                    <i class="bi bi-arrow-left"></i> Previous
                                </button>
                                <button type="submit" class="btn btn-success btn-lg px-5">
                                    <i class="bi bi-heart-fill"></i> Complete Donation
                                </button>
                            </div>
                            </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentStep = 1;
            const totalSteps = 4;

            // Step navigation functions
            function showStep(step) {
                // Hide all sections
                document.querySelectorAll('.form-section').forEach(section => {
                    section.classList.remove('active');
                });

                // Show current section
                document.getElementById(`section-${step}`).classList.add('active');

                // Update step indicators
                document.querySelectorAll('.step').forEach((stepEl, index) => {
                    stepEl.classList.remove('active', 'completed');
                    if (index + 1 < step) {
                        stepEl.classList.add('completed');
                    } else if (index + 1 === step) {
                        stepEl.classList.add('active');
                    }
                });

                currentStep = step;
            }

            // Step 1: Donation Type Selection
            document.querySelectorAll('.gift-type-card').forEach(card => {
                card.addEventListener('click', function() {
                    document.querySelectorAll('.gift-type-card').forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');

                    const donationType = this.dataset.type;
                    document.getElementById('donation_type').value = donationType;
                    document.getElementById('next-step-1').disabled = false;

                    // Show/hide birthday member section based on selection
                    const birthdaySection = document.getElementById('birthday-member-section');
                    if (donationType === 'birthday_gift') {
                        birthdaySection.style.display = 'block';
                    } else {
                        birthdaySection.style.display = 'none';
                    }
                });
            });

            // Step 2: Gift Options
            document.querySelectorAll('.gift-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.gift-option').forEach(o => o.classList.remove('selected'));
                    this.classList.add('selected');

                    const giftType = this.dataset.giftType;
                    document.getElementById('gift_type').value = giftType;

                    // Show template selection for digital cards
                    const templateSection = document.getElementById('template-selection');
                    if (giftType === 'digital_card') {
                        templateSection.style.display = 'block';
                    } else {
                        templateSection.style.display = 'none';
                    }
                });
            });

            // Template selection
            document.querySelectorAll('.template-option').forEach(template => {
                template.addEventListener('click', function() {
                    document.querySelectorAll('.template-option').forEach(t => t.classList.remove('selected'));
                    this.classList.add('selected');

                    const templateId = this.dataset.templateId;
                    document.getElementById('gift_template_id').value = templateId;
                });
            });

            // Delivery method handling
            document.querySelectorAll('input[name="delivery_method"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    const customDateSection = document.getElementById('custom-date-section');
                    if (this.id === 'custom_schedule') {
                        customDateSection.style.display = 'block';
                    } else {
                        customDateSection.style.display = 'none';
                    }
                });
            });

            // Step 3: Amount Selection
            document.querySelectorAll('.donation-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.donation-option').forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    document.getElementById('custom-amount').value = this.dataset.amount;
                });
            });

            // Currency change handler
            document.getElementById('currency').addEventListener('change', function() {
                const currencySymbols = {
                    'USD': '$',
                    'EUR': '€',
                    'GBP': '£',
                    'ZAR': 'R',
                    'NGN': '₦',
                    'KES': 'KSh',
                    'UGX': 'USh',
                    'GHS': 'GH₵',
                    'CAD': 'C$',
                    'AUD': 'A$'
                };

                const symbol = currencySymbols[this.value] || '$';
                document.querySelectorAll('.currency-symbol').forEach(el => {
                    el.textContent = symbol;
                });

                // Update donation option displays
                document.querySelectorAll('.donation-option h4').forEach(el => {
                    const amount = el.textContent.replace(/[^\d]/g, '');
                    el.textContent = symbol + amount;
                });
            });

            // Step navigation buttons
            document.getElementById('next-step-1').addEventListener('click', function() {
                showStep(2);
            });

            document.getElementById('prev-step-2').addEventListener('click', function() {
                showStep(1);
            });

            document.getElementById('next-step-2').addEventListener('click', function() {
                showStep(3);
            });

            document.getElementById('prev-step-3').addEventListener('click', function() {
                showStep(2);
            });

            document.getElementById('next-step-3').addEventListener('click', function() {
                const amount = document.getElementById('custom-amount').value;
                if (!amount || parseFloat(amount) < <?php echo $minimum_donation; ?>) {
                    alert('Please enter a valid donation amount (minimum: <?php echo $currency_symbol . $minimum_donation; ?>)');
                    return;
                }
                showStep(4);
            });

            document.getElementById('prev-step-4').addEventListener('click', function() {
                showStep(3);
            });

            // Form submission validation
            document.getElementById('enhanced-donation-form').addEventListener('submit', function(e) {
                const donationType = document.getElementById('donation_type').value;

                if (donationType === 'birthday_gift') {
                    const recipientId = document.getElementById('recipient_id').value;
                    if (!recipientId) {
                        e.preventDefault();
                        alert('Please select a birthday celebrant.');
                        showStep(2);
                        return;
                    }
                }

                const amount = document.getElementById('custom-amount').value;
                if (!amount || parseFloat(amount) < <?php echo $minimum_donation; ?>) {
                    e.preventDefault();
                    alert('Please enter a valid donation amount.');
                    showStep(3);
                    return;
                }

                const donorName = document.getElementById('donor_name').value;
                const donorEmail = document.getElementById('donor_email').value;

                if (!donorName || !donorEmail) {
                    e.preventDefault();
                    alert('Please fill in your name and email address.');
                    showStep(4);
                    return;
                }
            });
        });
    </script>

    <!-- Footer -->
    <?php if ($isLoggedIn): ?>
        <?php include 'user/includes/footer.php'; ?>
    <?php else: ?>
        <!-- Simple footer for anonymous users -->
        <footer class="bg-dark text-light py-4 mt-5">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h5><?php echo htmlspecialchars($organization_name); ?></h5>
                        <p class="mb-0">Thank you for your generous support!</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($organization_name); ?>. All rights reserved.</p>
                    </div>
                </div>
            </div>
        </footer>
    <?php endif; ?>
</body>
</html>
