isset() and empty()
-----
<?php
isset($a);
isset($a, $b, $c);

empty($a);
empty(foo());
empty(array(1, 2, 3));
-----
array(
    0: Stmt_Expression(
        expr: Expr_Isset(
            vars: array(
                0: Expr_Variable(
                    name: a
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_Isset(
            vars: array(
                0: Expr_Variable(
                    name: a
                )
                1: Expr_Variable(
                    name: b
                )
                2: Expr_Variable(
                    name: c
                )
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_Empty(
            expr: Expr_Variable(
                name: a
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_Empty(
            expr: Expr_FuncCall(
                name: Name(
                    name: foo
                )
                args: array(
                )
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_Empty(
            expr: Expr_Array(
                items: array(
                    0: ArrayItem(
                        key: null
                        value: Scalar_Int(
                            value: 1
                        )
                        byRef: false
                        unpack: false
                    )
                    1: ArrayItem(
                        key: null
                        value: Scalar_Int(
                            value: 2
                        )
                        byRef: false
                        unpack: false
                    )
                    2: ArrayItem(
                        key: null
                        value: Scalar_Int(
                            value: 3
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
        )
    )
)
