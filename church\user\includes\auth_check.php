<?php
/**
 * Authentication Check for User Pages
 * 
 * This file handles user authentication and session management
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes if not already included
if (!class_exists('SecurityManager')) {
    require_once '../classes/SecurityManager.php';
}
if (!class_exists('UserAuthManager')) {
    require_once '../classes/UserAuthManager.php';
}

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];

// Get user data from members table if not already set
if (!isset($userData)) {
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$userId]);
    $userData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userData) {
        session_destroy();
        header("Location: login.php");
        exit();
    }
}

// Check if user must change password (redirect to change password page)
if ($userData['must_change_password'] && basename($_SERVER['PHP_SELF']) !== 'change_password.php') {
    header("Location: change_password.php");
    exit();
}

// Set global variables for convenience
$sitename = get_site_setting('site_name', get_organization_name());
?>
