<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include the SecurityManager class
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize SecurityManager
$security = new SecurityManager($conn);

// Set security headers
$security->setSecurityHeaders();

// Log this access
$security->logSecurityEvent('Security audit page accessed', [
    'admin_id' => $_SESSION['admin_id']
]);

// Get filter parameters
$eventType = isset($_GET['event_type']) ? $security->sanitizeInput($_GET['event_type'], 'text') : '';
$username = isset($_GET['username']) ? $security->sanitizeInput($_GET['username'], 'text') : '';
$startDate = isset($_GET['start_date']) ? $security->sanitizeInput($_GET['start_date'], 'date') : '';
$endDate = isset($_GET['end_date']) ? $security->sanitizeInput($_GET['end_date'], 'date') : '';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$perPage = 50;

// Build query
$query = "
    SELECT sl.*, a.username 
    FROM security_logs sl
    LEFT JOIN admins a ON sl.user_id = a.id
    WHERE 1=1
";
$params = [];

if (!empty($eventType)) {
    $query .= " AND sl.event_type LIKE ?";
    $params[] = "%$eventType%";
}

if (!empty($username)) {
    $query .= " AND a.username LIKE ?";
    $params[] = "%$username%";
}

if (!empty($startDate)) {
    $query .= " AND DATE(sl.created_at) >= ?";
    $params[] = $startDate;
}

if (!empty($endDate)) {
    $query .= " AND DATE(sl.created_at) <= ?";
    $params[] = $endDate;
}

// Get total count
$countQuery = str_replace("sl.*, a.username", "COUNT(*) as total", $query);
$stmt = $conn->prepare($countQuery);
$stmt->execute($params);
$totalRows = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
$totalPages = ceil($totalRows / $perPage);

// Add pagination
$query .= " ORDER BY sl.created_at DESC LIMIT " . (($page - 1) * $perPage) . ", " . $perPage;

// Get logs
$stmt = $conn->prepare($query);
$stmt->execute($params);
$logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get unique event types for filter
$stmt = $conn->query("SELECT DISTINCT event_type FROM security_logs ORDER BY event_type");
$eventTypes = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Page title and header info
$page_title = "Security Audit";
$page_header = "Security Audit";
$page_description = "Review security logs and audit trail for your church management system.";

include 'includes/header.php';
?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="event_type" class="form-label">Event Type</label>
                            <select name="event_type" id="event_type" class="form-select">
                                <option value="">All Events</option>
                                <?php foreach ($eventTypes as $type): ?>
                                    <option value="<?php echo htmlspecialchars($type); ?>" <?php echo $eventType === $type ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($type); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($username); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo htmlspecialchars($startDate); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo htmlspecialchars($endDate); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">Filter</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Logs Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Date/Time</th>
                                    <th>Event Type</th>
                                    <th>Username</th>
                                    <th>IP Address</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($logs as $log): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars(date('Y-m-d H:i:s', strtotime($log['created_at']))); ?></td>
                                        <td><?php echo htmlspecialchars($log['event_type']); ?></td>
                                        <td><?php echo htmlspecialchars($log['username'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                                        <td>
                                            <?php 
                                            if (!empty($log['event_data'])) {
                                                $data = json_decode($log['event_data'], true);
                                                if (is_array($data)) {
                                                    foreach ($data as $key => $value) {
                                                        if (is_string($value)) {
                                                            echo htmlspecialchars($key) . ': ' . htmlspecialchars($value) . '<br>';
                                                        }
                                                    }
                                                }
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Security logs pagination">
                            <ul class="pagination justify-content-center">
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo $page === $i ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&event_type=<?php echo urlencode($eventType); ?>&username=<?php echo urlencode($username); ?>&start_date=<?php echo urlencode($startDate); ?>&end_date=<?php echo urlencode($endDate); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>


<?php include 'includes/footer.php'; ?> 