# Birthday Notification Template Fixes

## Overview
This document outlines the fixes implemented to resolve issues with birthday notification templates where:
1. The celebrant's profile image was not displaying properly in emails
2. Wrong images were being attached to birthday notifications
3. Image paths were not being converted to proper URLs for email delivery

## Root Causes Identified

### 1. Image Path vs URL Confusion
- System had multiple ways of handling images (file paths vs full URLs)
- Template replacement function had inconsistent URL construction logic
- Email embedding logic expected specific URL formats

### 2. Celebrant vs Recipient Image Confusion
- Birthday notifications are sent TO other members ABOUT the birthday member
- Code sometimes used recipient's image instead of celebrant's image
- Multiple image placeholder types caused confusion

### 3. Inconsistent Birthday Notification Detection
- Different files used different methods to detect birthday notifications
- Template replacement function couldn't reliably identify birthday emails
- Email embedding logic had separate detection mechanisms

### 4. Email Embedding Logic Issues
- Complex logic for embedding images had multiple failure points
- URL-to-CID replacement was fragile and could fail silently
- Attachment logic interfered with inline image embedding

## Fixes Implemented

### 1. Enhanced Template Replacement Function (`church/config.php`)

#### Improved Birthday Notification Detection
```php
// Enhanced birthday notification detection - check multiple indicators
$isBirthdayNotification = !empty($memberData['_is_birthday_notification']) ||
                         !empty($memberData['birthday_member_name']) ||
                         !empty($memberData['birthday_member_full_name']) ||
                         !empty($memberData['birthday_member_photo_url']) ||
                         !empty($memberData['birthday_member_image']);
```

#### Celebrant Image Priority Logic
```php
if ($isBirthdayNotification) {
    // For birthday notifications, ALWAYS use celebrant's image, never recipient's
    if (!empty($memberData['birthday_member_photo_url'])) {
        $memberImageUrl = $memberData['birthday_member_photo_url'];
    } elseif (!empty($memberData['birthday_member_image'])) {
        // Convert path to URL if needed
    } elseif (!empty($memberData['_birthday_member_original_image_path'])) {
        // Convert original path to URL
    } else {
        // Use default avatar
    }
}
```

#### Standardized Image Placeholders
- All birthday-related image placeholders now consistently use celebrant's image
- Removed duplicate placeholder definitions
- Added proper alt text using celebrant's name

### 2. Improved Email Embedding Logic (`church/config.php`)

#### Robust Birthday Photo Detection
```php
// Try multiple sources for the birthday member photo URL
if (isset($memberData['birthday_member_photo_url']) && !empty($memberData['birthday_member_photo_url'])) {
    $birthdayPhotoUrl = $memberData['birthday_member_photo_url'];
} elseif (isset($memberData['birthday_member_image']) && !empty($memberData['birthday_member_image'])) {
    $birthdayPhotoUrl = $memberData['birthday_member_image'];
} elseif (isset($memberData['member_image_url']) && !empty($memberData['member_image_url'])) {
    // For birthday notifications, member_image_url should contain celebrant's image
    $birthdayPhotoUrl = $memberData['member_image_url'];
}
```

#### Enhanced URL-to-CID Replacement
- More robust image file detection
- Better error handling and logging
- Fallback mechanisms for different image path formats

### 3. Updated Birthday Notification Senders

#### `church/user/send_birthday_message.php`
- Added `_is_birthday_notification` flag
- Added `_skip_attachments` flag to prevent unwanted attachments
- Added `_birthday_member_original_image_path` for proper embedding

#### `church/admin/send_birthday_message.php`
- Added birthday notification flags
- Set birthday member specific placeholders
- Ensured celebrant data is properly structured

#### `church/send_birthday_reminders.php`
- Already had proper flag setting (verified and maintained)
- Confirmed celebrant image data is set correctly

### 4. Enhanced Debug Logging

#### New Debug Helper Function
```php
function logBirthdayNotificationDebug($memberData, $context = '') {
    // Logs all relevant birthday notification data for debugging
}
```

#### Strategic Logging Points
- Template replacement function logs key image placeholders
- Email embedding function logs CID replacement operations
- Birthday notification senders log data before processing

#### Log Files Created
- `logs/birthday_debug.log` - General birthday notification debugging
- `logs/birthday_image_embedding.log` - Specific image embedding operations
- `logs/email_debug.log` - General email sending debugging

## Files Modified

1. **`church/config.php`**
   - Enhanced `replaceTemplatePlaceholders()` function
   - Improved `sendEmail()` function
   - Added `logBirthdayNotificationDebug()` helper function

2. **`church/user/send_birthday_message.php`**
   - Added birthday notification flags
   - Added debug logging call

3. **`church/admin/send_birthday_message.php`**
   - Added birthday notification flags
   - Set celebrant-specific placeholders
   - Added debug logging call

4. **`church/send_birthday_reminders.php`**
   - Verified existing implementation (no changes needed)

## Testing

### Test Script Created
- `church/test_birthday_notification_fixes.php`
- Tests template replacement with birthday notification data
- Validates image URL construction
- Checks birthday notification detection logic

### Manual Testing Steps
1. Send a birthday notification email
2. Check debug logs for proper flag detection
3. Verify celebrant's image appears in email (not recipient's)
4. Confirm no unwanted attachments are added
5. Test with different email clients

## Expected Results

### Before Fixes
- ❌ Recipient's image shown instead of celebrant's
- ❌ Images not displaying in emails (broken URLs)
- ❌ Wrong images attached to emails
- ❌ Inconsistent behavior across different birthday notification types

### After Fixes
- ✅ Celebrant's image consistently used in all birthday notifications
- ✅ Images properly embedded with correct URLs converted to CIDs
- ✅ No unwanted attachments added to birthday notifications
- ✅ Consistent behavior across all birthday notification senders
- ✅ Comprehensive logging for troubleshooting

## Monitoring and Maintenance

### Log Files to Monitor
- Check `logs/birthday_debug.log` for birthday notification processing
- Monitor `logs/birthday_image_embedding.log` for image embedding issues
- Review `logs/email_debug.log` for general email sending problems

### Key Indicators of Success
- Birthday notification emails display celebrant's photo correctly
- No "image not found" placeholders in emails
- No unwanted file attachments on birthday notifications
- Consistent image display across different email clients

### Troubleshooting
If issues persist:
1. Check if `_is_birthday_notification` flag is being set
2. Verify celebrant image data is in `memberData` array
3. Confirm image files exist at specified paths
4. Check SITE_URL configuration for proper URL construction
5. Review email client compatibility for embedded images
