Dereferencing of constants
-----
<?php

A->length;
A->length();
A[0];
A[0][1][2];

A::B[0];
A::B[0][1][2];
A::B->length;
A::B->length();
A::B::C;
A::B::$c;
A::B::c();

__FUNCTION__[0];
__FUNCTION__->length;
__FUNCIONT__->length();
-----
array(
    0: Stmt_Expression(
        expr: Expr_PropertyFetch(
            var: Expr_ConstFetch(
                name: Name(
                    name: A
                )
            )
            name: Identifier(
                name: length
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Expr_ConstFetch(
                name: Name(
                    name: A
                )
            )
            name: Identifier(
                name: length
            )
            args: array(
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_ArrayDimFetch(
            var: Expr_ConstFetch(
                name: Name(
                    name: A
                )
            )
            dim: Scalar_Int(
                value: 0
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_ArrayDimFetch(
            var: Expr_ArrayDimFetch(
                var: Expr_ArrayDimFetch(
                    var: Expr_ConstFetch(
                        name: Name(
                            name: A
                        )
                    )
                    dim: Scalar_Int(
                        value: 0
                    )
                )
                dim: Scalar_Int(
                    value: 1
                )
            )
            dim: Scalar_Int(
                value: 2
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_ArrayDimFetch(
            var: Expr_ClassConstFetch(
                class: Name(
                    name: A
                )
                name: Identifier(
                    name: B
                )
            )
            dim: Scalar_Int(
                value: 0
            )
        )
    )
    5: Stmt_Expression(
        expr: Expr_ArrayDimFetch(
            var: Expr_ArrayDimFetch(
                var: Expr_ArrayDimFetch(
                    var: Expr_ClassConstFetch(
                        class: Name(
                            name: A
                        )
                        name: Identifier(
                            name: B
                        )
                    )
                    dim: Scalar_Int(
                        value: 0
                    )
                )
                dim: Scalar_Int(
                    value: 1
                )
            )
            dim: Scalar_Int(
                value: 2
            )
        )
    )
    6: Stmt_Expression(
        expr: Expr_PropertyFetch(
            var: Expr_ClassConstFetch(
                class: Name(
                    name: A
                )
                name: Identifier(
                    name: B
                )
            )
            name: Identifier(
                name: length
            )
        )
    )
    7: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Expr_ClassConstFetch(
                class: Name(
                    name: A
                )
                name: Identifier(
                    name: B
                )
            )
            name: Identifier(
                name: length
            )
            args: array(
            )
        )
    )
    8: Stmt_Expression(
        expr: Expr_ClassConstFetch(
            class: Expr_ClassConstFetch(
                class: Name(
                    name: A
                )
                name: Identifier(
                    name: B
                )
            )
            name: Identifier(
                name: C
            )
        )
    )
    9: Stmt_Expression(
        expr: Expr_StaticPropertyFetch(
            class: Expr_ClassConstFetch(
                class: Name(
                    name: A
                )
                name: Identifier(
                    name: B
                )
            )
            name: VarLikeIdentifier(
                name: c
            )
        )
    )
    10: Stmt_Expression(
        expr: Expr_StaticCall(
            class: Expr_ClassConstFetch(
                class: Name(
                    name: A
                )
                name: Identifier(
                    name: B
                )
            )
            name: Identifier(
                name: c
            )
            args: array(
            )
        )
    )
    11: Stmt_Expression(
        expr: Expr_ArrayDimFetch(
            var: Scalar_MagicConst_Function(
            )
            dim: Scalar_Int(
                value: 0
            )
        )
    )
    12: Stmt_Expression(
        expr: Expr_PropertyFetch(
            var: Scalar_MagicConst_Function(
            )
            name: Identifier(
                name: length
            )
        )
    )
    13: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Expr_ConstFetch(
                name: Name(
                    name: __FUNCIONT__
                )
            )
            name: Identifier(
                name: length
            )
            args: array(
            )
        )
    )
)
