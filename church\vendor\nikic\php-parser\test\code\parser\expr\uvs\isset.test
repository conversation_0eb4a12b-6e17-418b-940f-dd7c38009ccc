UVS isset() on temporaries
-----
<?php

isset(([0, 1] + [])[0]);
isset(['a' => 'b']->a);
isset("str"->a);
-----
array(
    0: Stmt_Expression(
        expr: Expr_Isset(
            vars: array(
                0: Expr_ArrayDimFetch(
                    var: Expr_BinaryOp_Plus(
                        left: Expr_Array(
                            items: array(
                                0: ArrayItem(
                                    key: null
                                    value: Scalar_Int(
                                        value: 0
                                    )
                                    byRef: false
                                    unpack: false
                                )
                                1: ArrayItem(
                                    key: null
                                    value: Scalar_Int(
                                        value: 1
                                    )
                                    byRef: false
                                    unpack: false
                                )
                            )
                        )
                        right: Expr_Array(
                            items: array(
                            )
                        )
                    )
                    dim: Scalar_Int(
                        value: 0
                    )
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_Isset(
            vars: array(
                0: Expr_PropertyFetch(
                    var: Expr_Array(
                        items: array(
                            0: ArrayItem(
                                key: Scalar_String(
                                    value: a
                                )
                                value: Scalar_String(
                                    value: b
                                )
                                byRef: false
                                unpack: false
                            )
                        )
                    )
                    name: Identifier(
                        name: a
                    )
                )
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_Isset(
            vars: array(
                0: Expr_PropertyFetch(
                    var: Scalar_String(
                        value: str
                    )
                    name: Identifier(
                        name: a
                    )
                )
            )
        )
    )
)
