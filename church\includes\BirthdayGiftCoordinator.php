<?php
/**
 * Birthday Gift Coordinator
 * 
 * Integrates the enhanced donation system with the existing birthday reminder system
 * for seamless gift coordination and delivery
 */

require_once 'EnhancedDonationNotifier.php';

class BirthdayGiftCoordinator {
    private $pdo;
    private $notifier;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->notifier = new EnhancedDonationNotifier($pdo);
    }
    
    /**
     * Process scheduled birthday gifts for delivery
     * This should be called daily via cron job
     */
    public function processScheduledGifts() {
        try {
            $today = date('Y-m-d');
            $results = [
                'processed' => 0,
                'delivered' => 0,
                'failed' => 0,
                'errors' => []
            ];
            
            // Get gifts scheduled for today
            $stmt = $this->pdo->prepare("
                SELECT d.*, m.full_name as recipient_name, m.email as recipient_email, m.birth_date
                FROM donations d
                JOIN members m ON d.recipient_id = m.id
                WHERE d.donation_type = 'birthday_gift'
                AND d.delivery_status = 'scheduled'
                AND (
                    d.scheduled_delivery_date = ?
                    OR (d.scheduled_delivery_date IS NULL AND DATE(m.birth_date) = DATE(CONCAT(YEAR(?), '-', MONTH(m.birth_date), '-', DAY(m.birth_date))))
                )
                AND d.payment_status = 'completed'
            ");
            $stmt->execute([$today, $today]);
            $scheduled_gifts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($scheduled_gifts as $gift) {
                $results['processed']++;
                
                try {
                    // Send the gift notification
                    $notification_result = $this->notifier->sendDonationNotifications($gift['id']);
                    
                    if ($notification_result['success']) {
                        // Update delivery status
                        $this->updateGiftDeliveryStatus($gift['id'], 'delivered');
                        $results['delivered']++;
                        
                        // Log successful delivery
                        $this->logGiftDelivery($gift['id'], 'success', 'Gift delivered on scheduled date');
                        
                    } else {
                        // Mark as failed
                        $this->updateGiftDeliveryStatus($gift['id'], 'failed');
                        $results['failed']++;
                        
                        // Log failure
                        $this->logGiftDelivery($gift['id'], 'failed', $notification_result['error'] ?? 'Unknown error');
                        $results['errors'][] = "Gift #{$gift['id']}: " . ($notification_result['error'] ?? 'Unknown error');
                    }
                    
                } catch (Exception $e) {
                    $results['failed']++;
                    $error_msg = "Error processing gift #{$gift['id']}: " . $e->getMessage();
                    $results['errors'][] = $error_msg;
                    error_log($error_msg);
                    
                    // Log failure
                    $this->logGiftDelivery($gift['id'], 'failed', $e->getMessage());
                }
            }
            
            return $results;
            
        } catch (Exception $e) {
            error_log("Error in processScheduledGifts: " . $e->getMessage());
            return [
                'processed' => 0,
                'delivered' => 0,
                'failed' => 0,
                'errors' => [$e->getMessage()]
            ];
        }
    }
    
    /**
     * Get upcoming birthdays with gift opportunities
     */
    public function getUpcomingBirthdaysWithGifts($days_ahead = 30) {
        $stmt = $this->pdo->prepare("
            SELECT 
                m.id,
                m.full_name,
                m.email,
                m.birth_date,
                DATEDIFF(
                    DATE(CONCAT(
                        CASE 
                            WHEN MONTH(m.birth_date) < MONTH(CURDATE()) 
                                OR (MONTH(m.birth_date) = MONTH(CURDATE()) AND DAY(m.birth_date) < DAY(CURDATE()))
                            THEN YEAR(CURDATE()) + 1
                            ELSE YEAR(CURDATE())
                        END,
                        '-', MONTH(m.birth_date), '-', DAY(m.birth_date)
                    )),
                    CURDATE()
                ) as days_until_birthday,
                COUNT(d.id) as gift_count,
                COALESCE(SUM(d.amount), 0) as total_gift_amount
            FROM members m
            LEFT JOIN donations d ON m.id = d.recipient_id 
                AND d.donation_type = 'birthday_gift' 
                AND d.payment_status = 'completed'
                AND YEAR(d.created_at) = YEAR(CURDATE())
            WHERE m.birth_date IS NOT NULL
            AND DATEDIFF(
                DATE(CONCAT(
                    CASE 
                        WHEN MONTH(m.birth_date) < MONTH(CURDATE()) 
                            OR (MONTH(m.birth_date) = MONTH(CURDATE()) AND DAY(m.birth_date) < DAY(CURDATE()))
                        THEN YEAR(CURDATE()) + 1
                        ELSE YEAR(CURDATE())
                    END,
                    '-', MONTH(m.birth_date), '-', DAY(m.birth_date)
                )),
                CURDATE()
            ) <= ?
            GROUP BY m.id, m.full_name, m.email, m.birth_date
            ORDER BY days_until_birthday ASC
        ");
        $stmt->execute([$days_ahead]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get gift statistics for a specific member
     */
    public function getMemberGiftStats($member_id) {
        $stmt = $this->pdo->prepare("
            SELECT
                COUNT(*) as total_gifts_received,
                COALESCE(SUM(amount), 0) as total_amount_received,
                COUNT(CASE WHEN YEAR(created_at) = YEAR(CURDATE()) THEN 1 END) as gifts_this_year,
                COALESCE(SUM(CASE WHEN YEAR(created_at) = YEAR(CURDATE()) THEN amount ELSE 0 END), 0) as amount_this_year,
                MAX(created_at) as last_gift_date
            FROM donations
            WHERE recipient_id = ?
            AND donation_type = 'birthday_gift'
            AND payment_status = 'completed'
        ");
        $stmt->execute([$member_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Create birthday gift reminder notifications for members
     */
    public function createBirthdayGiftReminders($days_before = 7) {
        try {
            $upcoming_birthdays = $this->getUpcomingBirthdaysWithGifts($days_before);
            $reminders_created = 0;
            
            foreach ($upcoming_birthdays as $birthday) {
                if ($birthday['days_until_birthday'] <= $days_before && $birthday['days_until_birthday'] > 0) {
                    // Check if reminder already exists
                    $stmt = $this->pdo->prepare("
                        SELECT id FROM birthday_gift_reminders 
                        WHERE member_id = ? 
                        AND reminder_date = CURDATE()
                        AND reminder_type = 'gift_opportunity'
                    ");
                    $stmt->execute([$birthday['id']]);
                    
                    if (!$stmt->fetch()) {
                        // Create reminder
                        $stmt = $this->pdo->prepare("
                            INSERT INTO birthday_gift_reminders 
                            (member_id, reminder_type, reminder_date, days_until_birthday, message)
                            VALUES (?, 'gift_opportunity', CURDATE(), ?, ?)
                        ");
                        
                        $message = "Upcoming birthday for {$birthday['full_name']} in {$birthday['days_until_birthday']} days. Consider sending a birthday gift!";
                        $stmt->execute([$birthday['id'], $birthday['days_until_birthday'], $message]);
                        $reminders_created++;
                    }
                }
            }
            
            return [
                'success' => true,
                'reminders_created' => $reminders_created
            ];
            
        } catch (Exception $e) {
            error_log("Error creating birthday gift reminders: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update gift delivery status
     */
    private function updateGiftDeliveryStatus($donation_id, $status) {
        $stmt = $this->pdo->prepare("
            UPDATE donations 
            SET delivery_status = ?, delivered_at = CASE WHEN ? = 'delivered' THEN NOW() ELSE delivered_at END
            WHERE id = ?
        ");
        $stmt->execute([$status, $status, $donation_id]);
    }
    
    /**
     * Log gift delivery attempt
     */
    private function logGiftDelivery($donation_id, $status, $message = '') {
        try {
            // Get birthday gift ID if exists
            $stmt = $this->pdo->prepare("SELECT id FROM birthday_gifts WHERE donation_id = ?");
            $stmt->execute([$donation_id]);
            $birthday_gift = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($birthday_gift) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO gift_delivery_log 
                    (birthday_gift_id, delivery_status, delivery_method, error_message)
                    VALUES (?, ?, 'email', ?)
                ");
                $stmt->execute([$birthday_gift['id'], $status, $message]);
            }
        } catch (Exception $e) {
            error_log("Error logging gift delivery: " . $e->getMessage());
        }
    }
    
    /**
     * Get birthday gift dashboard data
     */
    public function getBirthdayGiftDashboard() {
        try {
            // Get today's birthdays
            $stmt = $this->pdo->prepare("
                SELECT m.*,
                       COUNT(d.id) as gifts_received_today,
                       COALESCE(SUM(d.amount), 0) as total_gift_amount_today
                FROM members m
                LEFT JOIN donations d ON m.id = d.recipient_id
                    AND d.donation_type = 'birthday_gift'
                    AND DATE(d.created_at) = CURDATE()
                    AND d.payment_status = 'completed'
                WHERE MONTH(m.birth_date) = MONTH(CURDATE())
                AND DAY(m.birth_date) = DAY(CURDATE())
                GROUP BY m.id
            ");
            $stmt->execute();
            $todays_birthdays = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get upcoming birthdays (next 7 days)
            $upcoming_birthdays = $this->getUpcomingBirthdaysWithGifts(7);
            
            // Get recent gift activity
            $stmt = $this->pdo->prepare("
                SELECT d.*, m.full_name as recipient_name
                FROM donations d
                JOIN members m ON d.recipient_id = m.id
                WHERE d.donation_type = 'birthday_gift'
                AND d.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                ORDER BY d.created_at DESC
                LIMIT 10
            ");
            $stmt->execute();
            $recent_gifts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get statistics
            $stmt = $this->pdo->prepare("
                SELECT
                    COUNT(*) as total_gifts_this_month,
                    COALESCE(SUM(amount), 0) as total_amount_this_month,
                    COUNT(CASE WHEN delivery_status = 'delivered' THEN 1 END) as delivered_this_month,
                    COUNT(CASE WHEN delivery_status = 'pending' THEN 1 END) as pending_this_month
                FROM donations
                WHERE donation_type = 'birthday_gift'
                AND MONTH(created_at) = MONTH(CURDATE())
                AND YEAR(created_at) = YEAR(CURDATE())
                AND payment_status = 'completed'
            ");
            $stmt->execute();
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);

            // Ensure all stats have default values
            $stats = array_merge([
                'total_gifts_this_month' => 0,
                'total_amount_this_month' => 0,
                'delivered_this_month' => 0,
                'pending_this_month' => 0
            ], $stats ?: []);
            
            return [
                'todays_birthdays' => $todays_birthdays,
                'upcoming_birthdays' => $upcoming_birthdays,
                'recent_gifts' => $recent_gifts,
                'stats' => $stats
            ];
            
        } catch (Exception $e) {
            error_log("Error getting birthday gift dashboard: " . $e->getMessage());
            return [
                'todays_birthdays' => [],
                'upcoming_birthdays' => [],
                'recent_gifts' => [],
                'stats' => [
                    'total_gifts_this_month' => 0,
                    'total_amount_this_month' => 0,
                    'delivered_this_month' => 0,
                    'pending_this_month' => 0
                ]
            ];
        }
    }
    
    /**
     * Create birthday gift reminder table if it doesn't exist
     */
    public function createReminderTable() {
        try {
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS birthday_gift_reminders (
                    id INT(11) AUTO_INCREMENT PRIMARY KEY,
                    member_id INT(11) NOT NULL,
                    reminder_type ENUM('gift_opportunity', 'delivery_reminder') NOT NULL,
                    reminder_date DATE NOT NULL,
                    days_until_birthday INT(11) NOT NULL,
                    message TEXT NOT NULL,
                    is_processed BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
                    INDEX idx_reminder_date (reminder_date, is_processed),
                    INDEX idx_member_reminder (member_id, reminder_type)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
            ");
            return true;
        } catch (Exception $e) {
            error_log("Error creating birthday gift reminder table: " . $e->getMessage());
            return false;
        }
    }
}

?>
