Trailing comma in parameter list
-----
<?php

function foo($bar, ) {
}
-----
array(
    0: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: foo
        )
        params: array(
            0: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: bar
                )
                default: null
                hooks: array(
                )
            )
        )
        returnType: null
        stmts: array(
        )
    )
)
-----
<?php

class Foo
{
    function __construct($name, $value, )
    {
    }
}
-----
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: Foo
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_ClassMethod(
                attrGroups: array(
                )
                flags: 0
                byRef: false
                name: Identifier(
                    name: __construct
                )
                params: array(
                    0: Param(
                        attrGroups: array(
                        )
                        flags: 0
                        type: null
                        byRef: false
                        variadic: false
                        var: Expr_Variable(
                            name: name
                        )
                        default: null
                        hooks: array(
                        )
                    )
                    1: Param(
                        attrGroups: array(
                        )
                        flags: 0
                        type: null
                        byRef: false
                        variadic: false
                        var: Expr_Variable(
                            name: value
                        )
                        default: null
                        hooks: array(
                        )
                    )
                )
                returnType: null
                stmts: array(
                )
            )
        )
    )
)
-----
<?php
fn($foo, ) => $bar;
-----
array(
    0: Stmt_Expression(
        expr: Expr_ArrowFunction(
            attrGroups: array(
            )
            static: false
            byRef: false
            params: array(
                0: Param(
                    attrGroups: array(
                    )
                    flags: 0
                    type: null
                    byRef: false
                    variadic: false
                    var: Expr_Variable(
                        name: foo
                    )
                    default: null
                    hooks: array(
                    )
                )
            )
            returnType: null
            expr: Expr_Variable(
                name: bar
            )
        )
    )
)
