<?php
/**
 * Birthday Template Preview AJAX Endpoint
 * 
 * Returns HTML preview of a birthday template with specific recipient data
 */

// Include configuration
require_once '../../config.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../../classes/SecurityManager.php';
require_once '../../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    http_response_code(401);
    echo 'Unauthorized';
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    http_response_code(401);
    echo 'Unauthorized';
    exit();
}

// Get parameters from request
$templateId = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;
$recipientId = isset($_GET['recipient_id']) ? intval($_GET['recipient_id']) : 0;

if (!$templateId || !$recipientId) {
    http_response_code(400);
    echo 'Invalid parameters';
    exit();
}

try {
    // Get template from admin-created email templates
    $stmt = $pdo->prepare("
        SELECT
            id,
            template_name as name,
            subject as description,
            content as template_content,
            template_category as category
        FROM email_templates
        WHERE id = ? AND is_birthday_template = 1
    ");
    $stmt->execute([$templateId]);
    $template = $stmt->fetch();
    
    if (!$template) {
        http_response_code(404);
        echo 'Template not found';
        exit();
    }
    
    // Get recipient with image path
    $stmt = $pdo->prepare("
        SELECT id, full_name, first_name, last_name, email, image_path
        FROM members
        WHERE id = ? AND status = 'active' AND id != ?
    ");
    $stmt->execute([$recipientId, $userId]);
    $recipient = $stmt->fetch();
    
    if (!$recipient) {
        http_response_code(404);
        echo 'Recipient not found';
        exit();
    }
    
    // Get site settings for branding
    $sitename = get_organization_name();
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
    $stmt->execute();
    $siteNameResult = $stmt->fetch();
    if ($siteNameResult) {
        $sitename = $siteNameResult['setting_value'];
    }
    
    // Prepare member data for placeholder replacement using the standardized function
    $memberData = [
        'full_name' => $recipient['full_name'],
        'first_name' => $recipient['first_name'],
        'last_name' => $recipient['last_name'] ?? '',
        'email' => $recipient['email'],
        'organization_name' => $sitename,
        'organization_type' => 'church',
        'sender_name' => $userData['first_name'],
        'sender_full_name' => $userData['first_name'] . ' ' . ($userData['last_name'] ?? ''),
        'church_name' => $sitename, // Legacy support
        'recipient_name' => $recipient['full_name'], // Legacy support

        // Core image path for replaceTemplatePlaceholders function
        'image_path' => $recipient['image_path'] ?? null,

        // Birthday member specific placeholders
        'birthday_member_name' => $recipient['first_name'],
        'birthday_member_first_name' => $recipient['first_name'],
        'birthday_member_full_name' => $recipient['full_name'],
        'birthday_member_email' => $recipient['email'],
    ];

    // Use standardized placeholder replacement function
    $previewContent = replaceTemplatePlaceholders($template['template_content'], $memberData);

    // Convert any image URLs to HTML img tags for preview display
    if (!empty($recipient['image_path'])) {
        // Create proper HTML img tag for birthday member image
        $birthdayMemberImagePath = '../' . ltrim($recipient['image_path'], '/');
        $birthdayMemberImageHtml = '<img src="' . $birthdayMemberImagePath . '" alt="' .
            htmlspecialchars($recipient['full_name']) .
            '" style="width: 140px; height: 140px; border-radius: 50%; object-fit: cover; border: 5px solid #ff758c; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">';

        // Handle multiple possible URL formats that could be generated
        $possibleUrls = [
            'http://localhost/campaign/church/' . ltrim($recipient['image_path'], '/'),
            'http://localhost/campaign/church/' . $recipient['image_path'],
            'http://localhost/campaign/' . ltrim($recipient['image_path'], '/'),
            'http://localhost/campaign/' . $recipient['image_path'],
            '../' . ltrim($recipient['image_path'], '/'),
            $recipient['image_path']
        ];

        // Only replace if the URL hasn't already been replaced with an img tag
        foreach ($possibleUrls as $url) {
            if (strpos($previewContent, $url) !== false && strpos($previewContent, '<img') === false) {
                $previewContent = str_replace($url, $birthdayMemberImageHtml, $previewContent);
                break; // Stop after first successful replacement
            }
        }
    }

    // Output the preview
    echo '<div class="template-preview-header mb-3">';
    echo '<h6>' . htmlspecialchars($template['name']) . '</h6>';
    echo '<p class="text-muted small">Preview for: <strong>' . htmlspecialchars($recipient['full_name']) . '</strong></p>';
    echo '<span class="badge bg-secondary">' . ucfirst($template['category']) . '</span>';
    echo '</div>';
    
    echo '<div class="template-preview-content">';
    echo $previewContent;
    echo '</div>';
    
    echo '<div class="template-preview-footer mt-3">';
    echo '<small class="text-muted">This is how the message will appear when sent to ' . htmlspecialchars($recipient['first_name']) . '.</small>';
    echo '</div>';
    
} catch (Exception $e) {
    http_response_code(500);
    echo 'Error loading template preview';
    error_log('Birthday template preview error: ' . $e->getMessage());
}
?>
