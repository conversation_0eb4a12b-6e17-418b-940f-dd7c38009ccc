<?php
require_once 'config.php';

echo "<h1>🔍 URL Configuration Debug</h1>\n";

echo "<h2>📊 Server Variables</h2>\n";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
echo "<tr><th>Variable</th><th>Value</th></tr>\n";
echo "<tr><td>HTTP_HOST</td><td>" . ($_SERVER['HTTP_HOST'] ?? 'NOT SET') . "</td></tr>\n";
echo "<tr><td>SERVER_NAME</td><td>" . ($_SERVER['SERVER_NAME'] ?? 'NOT SET') . "</td></tr>\n";
echo "<tr><td>SCRIPT_NAME</td><td>" . ($_SERVER['SCRIPT_NAME'] ?? 'NOT SET') . "</td></tr>\n";
echo "<tr><td>REQUEST_URI</td><td>" . ($_SERVER['REQUEST_URI'] ?? 'NOT SET') . "</td></tr>\n";
echo "<tr><td>HTTPS</td><td>" . ($_SERVER['HTTPS'] ?? 'NOT SET') . "</td></tr>\n";
echo "<tr><td>DOCUMENT_ROOT</td><td>" . ($_SERVER['DOCUMENT_ROOT'] ?? 'NOT SET') . "</td></tr>\n";
echo "</table>\n";

echo "<h2>🌐 Detected Environment</h2>\n";
$environment = 'development';
if (isset($_SERVER['SERVER_NAME'])) {
    $serverName = $_SERVER['SERVER_NAME'];
    if (strpos($serverName, 'localhost') === false && 
        strpos($serverName, '127.0.0.1') === false &&
        strpos($serverName, '::1') === false) {
        $environment = 'production';
    }
}
echo "<p><strong>Environment:</strong> $environment</p>\n";

echo "<h2>🔗 URL Constants</h2>\n";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
echo "<tr><th>Constant</th><th>Value</th></tr>\n";
echo "<tr><td>SITE_URL</td><td>" . (defined('SITE_URL') ? SITE_URL : 'NOT DEFINED') . "</td></tr>\n";
echo "<tr><td>BASE_URL</td><td>" . (defined('BASE_URL') ? BASE_URL : 'NOT DEFINED') . "</td></tr>\n";
echo "<tr><td>ADMIN_URL</td><td>" . (defined('ADMIN_URL') ? ADMIN_URL : 'NOT DEFINED') . "</td></tr>\n";
echo "<tr><td>USER_URL</td><td>" . (defined('USER_URL') ? USER_URL : 'NOT DEFINED') . "</td></tr>\n";
echo "<tr><td>ASSETS_URL</td><td>" . (defined('ASSETS_URL') ? ASSETS_URL : 'NOT DEFINED') . "</td></tr>\n";
echo "<tr><td>UPLOADS_URL</td><td>" . (defined('UPLOADS_URL') ? UPLOADS_URL : 'NOT DEFINED') . "</td></tr>\n";
echo "</table>\n";

echo "<h2>🛠️ Helper Functions</h2>\n";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
echo "<tr><th>Function</th><th>Result</th></tr>\n";
echo "<tr><td>get_base_url()</td><td>" . (function_exists('get_base_url') ? get_base_url() : 'FUNCTION NOT FOUND') . "</td></tr>\n";
echo "<tr><td>get_admin_url()</td><td>" . (function_exists('get_admin_url') ? get_admin_url() : 'FUNCTION NOT FOUND') . "</td></tr>\n";
echo "<tr><td>url_for('test.php')</td><td>" . (function_exists('url_for') ? url_for('test.php') : 'FUNCTION NOT FOUND') . "</td></tr>\n";
echo "<tr><td>admin_url_for('test.php')</td><td>" . (function_exists('admin_url_for') ? admin_url_for('test.php') : 'FUNCTION NOT FOUND') . "</td></tr>\n";
echo "</table>\n";

echo "<h2>🔍 Path Detection Logic</h2>\n";
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$scriptPath = dirname($_SERVER['SCRIPT_NAME'] ?? '');

echo "<p><strong>Protocol:</strong> $protocol</p>\n";
echo "<p><strong>Host:</strong> $host</p>\n";
echo "<p><strong>Script Path:</strong> $scriptPath</p>\n";

// Clean up script path to get base path
$basePath = $scriptPath;
echo "<p><strong>Initial Base Path:</strong> $basePath</p>\n";

if (strpos($basePath, '/admin') !== false) {
    $basePath = dirname($basePath);
    echo "<p><strong>After removing /admin:</strong> $basePath</p>\n";
}
if (strpos($basePath, '/user') !== false) {
    $basePath = dirname($basePath);
    echo "<p><strong>After removing /user:</strong> $basePath</p>\n";
}

// Construct base URL
$baseUrl = $protocol . '://' . $host . $basePath;
echo "<p><strong>Final Base URL:</strong> $baseUrl</p>\n";

echo "<h2>🧪 Test Links</h2>\n";
echo "<ul>\n";
echo "<li><a href='" . get_base_url() . "/admin/dashboard.php'>Admin Dashboard</a></li>\n";
echo "<li><a href='" . get_base_url() . "/user/dashboard.php'>User Dashboard</a></li>\n";
echo "<li><a href='" . get_base_url() . "/index.php'>Home Page</a></li>\n";
echo "</ul>\n";

echo "<h2>📝 Current Page Info</h2>\n";
echo "<p><strong>Current URL:</strong> " . $protocol . '://' . $host . $_SERVER['REQUEST_URI'] . "</p>\n";
echo "<p><strong>Should redirect to:</strong> " . get_base_url() . "/admin/dashboard.php</p>\n";

echo "<h2>🔧 Recommendations</h2>\n";
if (strpos(get_base_url(), 'freedomassemblydb.online') !== false) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 15px 0;'>\n";
    echo "<h3>❌ ISSUE FOUND:</h3>\n";
    echo "<p>URLs still contain 'freedomassemblydb.online' - this should be dynamic!</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 15px 0;'>\n";
    echo "<h3>✅ LOOKS GOOD:</h3>\n";
    echo "<p>URLs are properly dynamic and match the current domain.</p>\n";
    echo "</div>\n";
}

echo "<p><a href='admin/dashboard.php'>🏠 Go to Dashboard</a></p>\n";
?>
