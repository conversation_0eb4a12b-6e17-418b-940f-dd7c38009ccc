<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Set default response
$response = [
    'success' => false,
    'message' => 'An error occurred while preparing the WhatsApp message.'
];

// Initialize log directory
$logDir = __DIR__ . '/../logs';
if (!file_exists($logDir)) {
    mkdir($logDir, 0755, true);
}

// Function to log errors to file
function logError($message) {
    global $logDir;
    $date = date('Y-m-d H:i:s');
    $logFile = $logDir . '/whatsapp_errors.log';
    $logMessage = "[$date] $message" . PHP_EOL;
    
    // Log to error_log and to file
    error_log($message);
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// Debug function for more verbose debugging when needed
function debugWhatsApp($message, $data = null) {
    global $logDir;
    $date = date('Y-m-d H:i:s');
    $logFile = $logDir . '/whatsapp_debug.log';
    
    $logMessage = "[$date] $message";
    if ($data !== null) {
        $logMessage .= ": " . print_r($data, true);
    }
    $logMessage .= PHP_EOL;
    
    // Only log to file, not error_log to prevent flooding
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// Ensure WhatsApp logs table exists
try {
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'whatsapp_logs'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        // Create whatsapp_logs table
        $sql = "CREATE TABLE IF NOT EXISTS whatsapp_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            template_id INT NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'initiated',
            message TEXT NULL,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (member_id),
            INDEX (template_id)
        )";
        
        $pdo->exec($sql);
        error_log("Created whatsapp_logs table");
    }
} catch (PDOException $e) {
    logError("Error checking/creating whatsapp_logs table: " . $e->getMessage());
    $_SESSION['error'] = "Database error: Unable to set up WhatsApp logs. Please contact support.";
    header('Location: whatsapp_messages.php');
    exit;
}

// Process request (handles both GET and POST requests)
$member_id = null;
$template_id = null;
$recipient_phone = null;
$recipient_name = null;
$custom_message = '';

// Check request method and get parameters
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get data from POST request
    $member_id = $_POST['member_id'] ?? null;
    $template_id = $_POST['template_id'] ?? null;
    $recipient_phone = $_POST['recipient_phone'] ?? null;
    $recipient_name = $_POST['recipient_name'] ?? null;
    $custom_message = $_POST['custom_message'] ?? '';
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Get data from GET request for quick buttons
    $member_id = $_GET['member_id'] ?? null;
    $template_id = $_GET['template_id'] ?? null;
    $recipient_phone = $_GET['recipient_phone'] ?? null;
    $recipient_name = $_GET['recipient_name'] ?? null;
    $custom_message = $_GET['custom_message'] ?? '';
} else {
    logError("Invalid request method: " . $_SERVER['REQUEST_METHOD']);
    $_SESSION['error'] = 'Invalid request method.';
    header('Location: whatsapp_messages.php');
    exit;
}

// Log received parameters for debugging
logError("Received parameters: member_id=$member_id, template_id=$template_id, recipient_name=" . 
    ($recipient_name ? substr($recipient_name, 0, 30) : 'null') . 
    ", phone=" . ($recipient_phone ? substr($recipient_phone, 0, 15) : 'null'));

// Validate required fields
if (empty($member_id)) {
    logError("Missing required parameter: member_id");
    $_SESSION['error'] = 'Member ID is required.';
    header('Location: whatsapp_messages.php');
    exit;
}

if (empty($template_id)) {
    logError("Missing required parameter: template_id");
    $_SESSION['error'] = 'Template ID is required.';
    header('Location: whatsapp_messages.php');
    exit;
}

if (empty($recipient_phone)) {
    logError("Missing required parameter: recipient_phone");
    $_SESSION['error'] = 'Recipient phone number is required.';
    header('Location: whatsapp_messages.php');
    exit;
}

if (empty($recipient_name)) {
    logError("Missing required parameter: recipient_name");
    $_SESSION['error'] = 'Recipient name is required.';
    header('Location: whatsapp_messages.php');
    exit;
}

try {
    // Get the WhatsApp template
    $stmt = $pdo->prepare("SELECT * FROM whatsapp_templates WHERE id = ?");
    $stmt->execute([$template_id]);
    $template = $stmt->fetch();

    if (!$template) {
        logError("Template not found with ID: $template_id");
        $_SESSION['error'] = 'WhatsApp template not found.';
        header('Location: whatsapp_messages.php');
        exit;
    }

    // Check if template content is empty
    if (empty($template['message_content'])) {
        logError("Template content is empty for template ID: $template_id");
        $_SESSION['error'] = 'The selected template has no content.';
        header('Location: whatsapp_messages.php');
        exit;
    }

    // Get member details if exists
    $member = null;
    $birth_date = date('d M'); // Default to today's date

    try {
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();

        if ($member) {
            // Format birth date if we have it
            $birth_date = date('d M', strtotime($member['birth_date']));
        }
    } catch (PDOException $e) {
        // Log but continue - we'll use the passed parameters
        logError("Error fetching member details: " . $e->getMessage());
    }

    // If member not found in DB, create a basic member data array from passed parameters
    if (!$member) {
        $member = [
            'full_name' => $recipient_name,
            'phone_number' => $recipient_phone,
            'birth_date' => date('Y-m-d') // Fallback to today
        ];
    }
    
    // Get WhatsApp settings for sender name
    $sender_name = get_organization_name(); // Default
    try {
        $stmt = $pdo->prepare("SELECT sender_name FROM whatsapp_settings WHERE id = 1");
        $stmt->execute();
        if ($stmt->rowCount() > 0) {
            $settings = $stmt->fetch();
            $sender_name = $settings['sender_name'];
        }
    } catch (PDOException $e) {
        // Log but continue with default
        logError("Error getting WhatsApp settings: " . $e->getMessage());
    }
    
    // Make sure we have a first name for the placeholder
    $first_name = '';
    if (!empty($member['full_name'])) {
        $name_parts = explode(' ', $member['full_name']);
        $first_name = $name_parts[0];
    }
    
    // Replace placeholders in the template message
    $message = $template['message_content'];
    $message = str_replace('{full_name}', $member['full_name'], $message);
    $message = str_replace('{first_name}', $first_name, $message);
    $message = str_replace('{birth_date}', $birth_date, $message);
    $message = str_replace('{church_name}', 'Freedom Assembly Church', $message);
    $message = str_replace('{sender_name}', $sender_name, $message);
    
    // Add custom message if provided
    if (!empty($custom_message)) {
        $message .= "\n\n" . $custom_message;
    }

    // Log the original message for debugging
    debugWhatsApp("Original message", $message);
    
    // Format phone number - remove any non-digit characters
    $formattedPhone = preg_replace('/\D/', '', $recipient_phone);
    
    // Phone number validation
    if (empty($formattedPhone)) {
        logError("Invalid phone number after formatting: $recipient_phone");
        $_SESSION['error'] = 'The phone number is invalid. Please update the member record with a valid phone number.';
        header('Location: whatsapp_messages.php');
        exit;
    }
    
    // If it starts with 0, remove it and add country code (assume Nigerian number)
    if (substr($formattedPhone, 0, 1) === '0') {
        $formattedPhone = '234' . substr($formattedPhone, 1);
    }
    
    // Log the formatted phone and message
    logError("Formatted phone: $formattedPhone, Message length: " . strlen($message));
    
    // NEW IMPROVED ENCODING FOR WHATSAPP CLIENTS
    // 1. Convert newlines to URL-encoded format
    $processedMessage = str_replace(["\r\n", "\n", "\r"], "%0A", $message);

    // 2. Encode special characters but keep WhatsApp's URL format
    $processedMessage = urlencode($processedMessage);

    // 3. Fix double encoding of %0A newlines
    $processedMessage = str_replace('%250A', '%0A', $processedMessage);

    // 4. Create URLs for different WhatsApp clients
    $webUrl = "https://web.whatsapp.com/send?phone=$formattedPhone&text=" . urlencode($processedMessage);
    $desktopUrl = "https://api.whatsapp.com/send?phone=$formattedPhone&text=" . urlencode($processedMessage);
    
    // Log URLs for debugging
    debugWhatsApp("Primary WhatsApp URL", $webUrl);
    debugWhatsApp("Desktop URL", $desktopUrl);
    
    // Log a shorter version to error log
    $urlPreview = substr($webUrl, 0, 100) . '...';
    logError("WhatsApp URL (partial): $urlPreview");
    
    // Log this action to database
    try {
        $stmt = $pdo->prepare("INSERT INTO whatsapp_logs (member_id, template_id, status, message, sent_at) VALUES (?, ?, 'initiated', ?, NOW())");
        if ($stmt->execute([$member_id, $template_id, substr($message, 0, 255)])) {
            $log_id = $pdo->lastInsertId();
            logError("WhatsApp message logged with ID: $log_id");
        } else {
            logError("Failed to insert into whatsapp_logs table");
            $log_id = 0;
        }
    } catch (PDOException $e) {
        logError("Error logging WhatsApp message: " . $e->getMessage());
        // Continue even if logging fails
    }
    
    // Store success message in session
    $_SESSION['message'] = "WhatsApp message prepared for $recipient_name. Your WhatsApp will open shortly.";
    
    // Store alternate URL in session for fallback
    $_SESSION['alternate_whatsapp_url'] = $desktopUrl;
    
    // Create an intermediate page for better user experience and fallback options
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Open WhatsApp</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        body { font-family: Arial, sans-serif; background-color: #f0f2f5; }
        .container { max-width: 800px; margin-top: 2rem; }
        .whatsapp-btn { background-color: #25D366; border-color: #25D366; color: white; }
        .client-options { margin-top: 2rem; }
        .message-preview {
            background-color: #f8f9fa; 
            border-radius: 10px; 
            padding: 15px; 
            margin: 20px 0;
            border-left: 4px solid #25D366;
            white-space: pre-line;
        }
        .copy-btn {
            cursor: pointer;
        }
        .copied {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0"><i class="bi bi-whatsapp"></i> Open WhatsApp</h4>
            </div>
            <div class="card-body">
                <h5 class="mb-3">Send message to '.htmlspecialchars($recipient_name).'</h5>
                
                <!-- Message Preview -->
                <div class="message-preview">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <span class="text-muted">Message content:</span>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" onclick="copyMessage()">
                            <i class="bi bi-clipboard"></i> Copy
                        </button>
                    </div>
                    <div id="message-content">'.nl2br(htmlspecialchars($message)).'</div>
                </div>
                
                <!-- Windows App Solution -->
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> Windows Desktop App Users:</h6>
                    <p>If your WhatsApp Desktop app opens with an empty message, please:</p>
                    <ol>
                        <li>Click the "Copy" button above to copy the message</li>
                        <li>Open the chat using the "Open in Windows App" button below</li>
                        <li>Paste the message (Ctrl+V) in the chat field</li>
                    </ol>
                </div>
                
                <div class="client-options">
                    <div class="row g-3 justify-content-center">
                        <!-- WhatsApp Web -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h3 class="mb-3"><i class="bi bi-browser-chrome"></i> WhatsApp Web</h3>
                                    <p class="text-muted mb-4">Open in your browser for full functionality</p>
                                    <a href="'.htmlspecialchars($webUrl).'" 
                                       class="btn whatsapp-btn btn-lg w-100 py-3"
                                       target="_blank">
                                        <i class="bi bi-whatsapp"></i> Open in Browser
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- WhatsApp Desktop -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h3 class="mb-3"><i class="bi bi-windows"></i> Windows App</h3>
                                    <p class="text-muted mb-4">Open directly in WhatsApp Desktop</p>
                                    <a href="'.htmlspecialchars($desktopUrl).'" 
                                       class="btn btn-primary btn-lg w-100 py-3">
                                        <i class="bi bi-whatsapp"></i> Open in Windows
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 text-center">
                    <p class="text-muted">Having issues? Try these:</p>
                    <div class="btn-group">
                        <a href="whatsapp_messages.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Go Back
                        </a>
                        <button class="btn btn-outline-secondary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> Try Again
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    function copyMessage() {
        const messageContent = document.getElementById("message-content").innerText;
        navigator.clipboard.writeText(messageContent)
            .then(() => {
                const copyBtn = document.querySelector(".copy-btn");
                copyBtn.innerHTML = "<i class=\"bi bi-check-lg\"></i> Copied!";
                copyBtn.classList.add("copied");
                setTimeout(() => {
                    copyBtn.innerHTML = "<i class=\"bi bi-clipboard\"></i> Copy";
                    copyBtn.classList.remove("copied");
                }, 2000);
            })
            .catch(err => {
                console.error("Error copying text: ", err);
                alert("Failed to copy. Please select the text and copy manually.");
            });
    }
    </script>
</body>
</html>';
    exit;
    
} catch (PDOException $e) {
    // Database errors
    logError("Database error in send_whatsapp_message.php: " . $e->getMessage());
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    header('Location: whatsapp_messages.php');
    exit;
} catch (Exception $e) {
    // All other errors
    logError("Exception in send_whatsapp_message.php: " . $e->getMessage());
    $_SESSION['error'] = 'Error: ' . $e->getMessage();
    header('Location: whatsapp_messages.php');
    exit;
} 