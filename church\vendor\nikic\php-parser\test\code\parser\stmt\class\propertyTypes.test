Class declaration
-----
<?php

class A {
    public string $a;
    protected static D $b;
    private ?float $c;
    readonly static public ?int $d;
}
-----
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_Property(
                attrGroups: array(
                )
                flags: PUBLIC (1)
                type: Identifier(
                    name: string
                )
                props: array(
                    0: PropertyItem(
                        name: VarLikeIdentifier(
                            name: a
                        )
                        default: null
                    )
                )
                hooks: array(
                )
            )
            1: Stmt_Property(
                attrGroups: array(
                )
                flags: PROTECTED | STATIC (10)
                type: Name(
                    name: D
                )
                props: array(
                    0: PropertyItem(
                        name: VarLikeIdentifier(
                            name: b
                        )
                        default: null
                    )
                )
                hooks: array(
                )
            )
            2: Stmt_Property(
                attrGroups: array(
                )
                flags: PRIVATE (4)
                type: NullableType(
                    type: Identifier(
                        name: float
                    )
                )
                props: array(
                    0: PropertyItem(
                        name: VarLikeIdentifier(
                            name: c
                        )
                        default: null
                    )
                )
                hooks: array(
                )
            )
            3: Stmt_Property(
                attrGroups: array(
                )
                flags: PUBLIC | STATIC | READONLY (73)
                type: NullableType(
                    type: Identifier(
                        name: int
                    )
                )
                props: array(
                    0: PropertyItem(
                        name: VarLikeIdentifier(
                            name: d
                        )
                        default: null
                    )
                )
                hooks: array(
                )
            )
        )
    )
)
