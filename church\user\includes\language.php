<?php
/**
 * User Portal Language System
 * Handles translation loading and text rendering for user portal
 */

// Include the main language manager from admin
require_once __DIR__ . '/../../admin/includes/language.php';

/**
 * Initialize user language detection and override admin detection
 */
function initUserLanguageSystem() {
    global $lang, $pdo;

    // Check user preference from database (user portal)
    if (isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
        $userLang = getUserLanguagePreference($userId);
        if ($userLang) {
            // Try to set the language - setLanguage will validate if it's supported
            $lang->setLanguage($userLang);
            return;
        }
    }

    // If no user preference found, use the existing detection logic
    // The LanguageManager will handle session, cookie, browser, and system defaults
}

/**
 * Get user language preference from user_preferences table
 */
function getUserLanguagePreference($userId) {
    global $pdo;
    if (!isset($pdo)) return null;

    try {
        $stmt = $pdo->prepare("SELECT preference_value FROM user_preferences WHERE user_id = ? AND preference_key = 'language'");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();

        if ($result) {
            return $result['preference_value'];
        }
    } catch (Exception $e) {
        error_log("Error getting user language preference: " . $e->getMessage());
    }

    return null;
}

/**
 * Save user language preference to user_preferences table
 */
function saveUserLanguagePreference($userId, $language) {
    global $pdo;
    if (!isset($pdo)) return false;

    try {
        $stmt = $pdo->prepare("
            INSERT INTO user_preferences (user_id, preference_key, preference_value, value_type)
            VALUES (?, 'language', ?, 'string')
            ON DUPLICATE KEY UPDATE
            preference_value = VALUES(preference_value),
            updated_at = NOW()
        ");
        $stmt->execute([$userId, $language]);
        return true;
    } catch (Exception $e) {
        error_log("Error saving user language preference: " . $e->getMessage());
        return false;
    }
}

/**
 * Override the setLanguage function to save user preferences
 */
function setUserLanguage($language) {
    global $lang;

    // Set the language using the main language manager (it will validate if supported)
    $result = $lang->setLanguage($language);

    // Also save to user preferences if logged in and language was set successfully
    if ($result && isset($_SESSION['user_id'])) {
        saveUserLanguagePreference($_SESSION['user_id'], $language);
    }

    return $result;
}

// Initialize the user language system
initUserLanguageSystem();

// Get reference to the global language manager
$userLang = $lang;

// Helper functions (same as admin but using user language manager)
if (!function_exists('__')) {
    function __($key, $params = []) {
        global $userLang;
        return $userLang->get($key, $params);
    }
}

if (!function_exists('_e')) {
    function _e($key, $params = []) {
        global $userLang;
        $userLang->echo($key, $params);
    }
}

if (!function_exists('get_current_language')) {
    function get_current_language() {
        global $userLang;
        return $userLang->getCurrentLanguage();
    }
}

if (!function_exists('set_language')) {
    function set_language($language) {
        return setUserLanguage($language);
    }
}

if (!function_exists('get_language_direction')) {
    function get_language_direction() {
        global $userLang;
        return $userLang->getLanguageDirection();
    }
}

// Set HTML lang attribute
if (!headers_sent()) {
    $currentLang = get_current_language();
    if ($currentLang) {
        // This will be used in the HTML head
        $GLOBALS['html_lang'] = $currentLang;
        $GLOBALS['html_dir'] = get_language_direction();
    }
}
?>
