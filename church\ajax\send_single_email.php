<?php
require_once '../config.php';
header('Content-Type: application/json');

try {
    // Get form data
    $templateId = $_POST['template_id'] ?? '';
    $recipientType = $_POST['recipient_type'] ?? '';
    $recipientIds = isset($_POST['recipient_ids']) ? json_decode($_POST['recipient_ids'], true) : [];
    
    // Validate required fields
    if (empty($templateId) || empty($recipientType) || empty($recipientIds)) {
        throw new Exception('Missing required parameters');
    }

    // Get template
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
    $stmt->execute([$templateId]);
    $template = $stmt->fetch();

    if (!$template) {
        throw new Exception('Template not found');
    }

    // Get recipient
    $recipientId = $recipientIds[0]; // Get first recipient ID
    $table = $recipientType === 'members' ? 'members' : 'contacts';
    $nameField = $recipientType === 'members' ? 'full_name' : 'name';
    
    // Improved recipient fetching with better error handling
    $stmt = $pdo->prepare("SELECT id, $nameField as full_name, email, phone_number, birth_date FROM $table WHERE id = ?");
    if (!$stmt->execute([$recipientId])) {
        throw new Exception("Database error while fetching recipient details");
    }
    
    $recipient = $stmt->fetch(PDO::FETCH_ASSOC);
    if (!$recipient) {
        throw new Exception("Recipient with ID $recipientId not found in $table table");
    }

    if (empty($recipient['email'])) {
        throw new Exception("Recipient {$recipient['full_name']} (ID: $recipientId) has no email address");
    }

    // Replace placeholders in template
    $subject = replaceTemplatePlaceholders($template['subject'], $recipient);
    $content = replaceTemplatePlaceholders($template['content'], $recipient);

    // Format content for HTML email
    $htmlContent = nl2br($content);
    $htmlContent = "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;'>
        <h2 style='color: #333;'>{$subject}</h2>
        <div style='line-height: 1.6; color: #555;'>{$htmlContent}</div>
        <div style='margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #777; font-size: 12px;'>
            This email was sent from Freedom Assembly Church International.
        </div>
    </div>";

    // Begin transaction for logging
    $pdo->beginTransaction();
    
    try {
        // Send email
        if (sendEmail($recipient['email'], $recipient['full_name'], $subject, $htmlContent, true, $recipient)) {
            // Log successful send
            $stmt = $pdo->prepare("INSERT INTO email_logs (member_id, template_id, email_type, subject, status) VALUES (?, ?, 'bulk', ?, 'success')");
            $stmt->execute([$recipientId, $templateId, $subject]);
            
            // Generate tracking ID
            $trackingId = uniqid('track_', true);
            
            // Add tracking record
            $stmt = $pdo->prepare("INSERT INTO email_tracking (member_id, tracking_id, email_type, sent_at) VALUES (?, ?, 'bulk', NOW())");
            $stmt->execute([$recipientId, $trackingId]);
            
            $pdo->commit();
            echo json_encode([
                'success' => true,
                'recipient' => [
                    'id' => $recipient['id'],
                    'name' => $recipient['full_name'],
                    'email' => $recipient['email']
                ]
            ]);
        } else {
            // Get error message from global variable
            global $last_email_error;
            $errorMessage = $last_email_error ?? 'Unknown error occurred while sending email';
            
            // Log failed send
            $stmt = $pdo->prepare("INSERT INTO email_logs (member_id, template_id, email_type, subject, status, error_message) VALUES (?, ?, 'bulk', ?, 'failed', ?)");
            $stmt->execute([$recipientId, $templateId, $subject, $errorMessage]);
            
            $pdo->commit();
            throw new Exception("Failed to send email to {$recipient['full_name']}: $errorMessage");
        }
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Error sending email: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Error sending email: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}