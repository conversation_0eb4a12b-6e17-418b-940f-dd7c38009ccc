<?php
session_start();
require_once "../config.php";

// Check if admin is logged in
if (!isset($_SESSION["admin_id"])) {
    header("Location: login.php");
    exit();
}

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST["action"])) {
    try {
        if ($_POST["action"] === "update_setting") {
            $stmt = $pdo->prepare("
                UPDATE appearance_settings 
                SET setting_value = ?, updated_at = NOW() 
                WHERE setting_name = ?
            ");
            $stmt->execute([$_POST["setting_value"], $_POST["setting_name"]]);
            $success = "Setting updated successfully!";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get branding settings
try {
    $stmt = $pdo->prepare("SELECT * FROM appearance_settings WHERE category = 'branding' ORDER BY setting_name");
    $stmt->execute();
    $brandingSettings = $stmt->fetchAll();
} catch (Exception $e) {
    $error = "Error loading settings: " . $e->getMessage();
    $brandingSettings = [];
}

$pageTitle = "Branding Settings";
include "includes/header.php";
?>

        <div class="container-fluid">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="bi bi-palette"></i> Branding Settings</h1>
            </div>

            <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-brush"></i> Organization Branding</h5>
                    <p class="text-muted mb-0">Customize your organization's visual identity and branding elements.</p>
                </div>
                <div class="card-body">
                    <?php if (count($brandingSettings) > 0): ?>
                        <?php foreach ($brandingSettings as $setting): ?>
                            <form method="post" class="mb-4">
                                <input type="hidden" name="action" value="update_setting">
                                <input type="hidden" name="setting_name" value="<?php echo htmlspecialchars($setting["setting_name"]); ?>">
                                
                                <div class="row align-items-end">
                                    <div class="col-md-3">
                                        <label class="form-label">
                                            <strong><?php echo ucwords(str_replace("_", " ", $setting["setting_name"])); ?></strong>
                                        </label>
                                    </div>
                                    <div class="col-md-6">
                                        <?php if ($setting["setting_type"] === "color"): ?>
                                            <input type="color" name="setting_value" class="form-control form-control-color" 
                                                   value="<?php echo htmlspecialchars($setting["setting_value"]); ?>">
                                        <?php else: ?>
                                            <input type="text" name="setting_value" class="form-control" 
                                                   value="<?php echo htmlspecialchars($setting["setting_value"]); ?>"
                                                   placeholder="Enter <?php echo strtolower(str_replace("_", " ", $setting["setting_name"])); ?>">
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-lg"></i> Update
                                        </button>
                                    </div>
                                </div>
                                
                                <?php if (!empty($setting["description"])): ?>
                                    <div class="row mt-1">
                                        <div class="col-md-9 offset-md-3">
                                            <small class="text-muted"><?php echo htmlspecialchars($setting["description"]); ?></small>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </form>
                            <hr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-palette display-1 text-muted"></i>
                            <h4 class="text-muted">No branding settings found</h4>
                            <p class="text-muted">Branding settings need to be initialized. Please contact your system administrator.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include "includes/footer.php"; ?>
