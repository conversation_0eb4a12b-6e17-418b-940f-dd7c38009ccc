<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Date range filter
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$endDate = $_GET['end_date'] ?? date('Y-m-d'); // Today

// Validate dates
if (strtotime($startDate) > strtotime($endDate)) {
    $temp = $startDate;
    $startDate = $endDate;
    $endDate = $temp;
}

// Get SMS analytics data
$analytics = [];

try {
    // Overall statistics
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_campaigns,
            SUM(total_recipients) as total_recipients,
            SUM(sent_count) as total_sent,
            SUM(delivered_count) as total_delivered,
            SUM(failed_count) as total_failed,
            ROUND(AVG(CASE WHEN sent_count > 0 THEN (delivered_count / sent_count) * 100 ELSE 0 END), 2) as avg_delivery_rate
        FROM sms_campaigns 
        WHERE created_at BETWEEN ? AND ?
    ");
    $stmt->execute([$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
    $analytics['overview'] = $stmt->fetch(PDO::FETCH_ASSOC);

    // Campaign performance over time
    $stmt = $pdo->prepare("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as campaigns_created,
            SUM(sent_count) as messages_sent,
            SUM(delivered_count) as messages_delivered,
            SUM(failed_count) as messages_failed
        FROM sms_campaigns 
        WHERE created_at BETWEEN ? AND ?
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");
    $stmt->execute([$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
    $analytics['daily_performance'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Campaign status distribution
    $stmt = $pdo->prepare("
        SELECT 
            status,
            COUNT(*) as count,
            ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sms_campaigns WHERE created_at BETWEEN ? AND ?)), 2) as percentage
        FROM sms_campaigns 
        WHERE created_at BETWEEN ? AND ?
        GROUP BY status
        ORDER BY count DESC
    ");
    $stmt->execute([$startDate . ' 00:00:00', $endDate . ' 23:59:59', $startDate . ' 00:00:00', $endDate . ' 23:59:59']);
    $analytics['status_distribution'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Top performing campaigns
    $stmt = $pdo->prepare("
        SELECT
            name as campaign_name,
            total_recipients,
            sent_count,
            delivered_count,
            failed_count,
            CASE WHEN sent_count > 0 THEN ROUND((delivered_count / sent_count) * 100, 2) ELSE 0 END as delivery_rate,
            created_at
        FROM sms_campaigns
        WHERE created_at BETWEEN ? AND ? AND status = 'completed'
        ORDER BY delivery_rate DESC, delivered_count DESC
        LIMIT 10
    ");
    $stmt->execute([$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
    $analytics['top_campaigns'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Recent activity
    $stmt = $pdo->prepare("
        SELECT
            name as campaign_name,
            status,
            total_recipients,
            sent_count,
            delivered_count,
            created_at,
            sent_at
        FROM sms_campaigns
        WHERE created_at BETWEEN ? AND ?
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
    $analytics['recent_activity'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = "Error fetching analytics data: " . $e->getMessage();
    $analytics = [
        'overview' => ['total_campaigns' => 0, 'total_recipients' => 0, 'total_sent' => 0, 'total_delivered' => 0, 'total_failed' => 0, 'avg_delivery_rate' => 0],
        'daily_performance' => [],
        'status_distribution' => [],
        'top_campaigns' => [],
        'recent_activity' => []
    ];
}

// Set page variables
$page_title = 'SMS Analytics';
$page_header = 'SMS Analytics';
$page_description = 'View SMS campaign performance and statistics.';

// Include header
include 'includes/header.php';
?>

<style>
.analytics-card {
    border-left: 4px solid;
    transition: transform 0.2s;
}
.analytics-card:hover {
    transform: translateY(-2px);
}
.analytics-card.primary { border-left-color: #007bff; }
.analytics-card.success { border-left-color: #28a745; }
.analytics-card.warning { border-left-color: #ffc107; }
.analytics-card.danger { border-left-color: #dc3545; }
.analytics-card.info { border-left-color: #17a2b8; }

.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}
.performance-table {
    font-size: 0.875rem;
}
.delivery-rate {
    font-weight: bold;
}
.delivery-rate.excellent { color: #28a745; }
.delivery-rate.good { color: #17a2b8; }
.delivery-rate.average { color: #ffc107; }
.delivery-rate.poor { color: #dc3545; }
</style>

<!-- Page Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="bi bi-graph-up me-2"></i><?php echo $page_header; ?>
    </h1>
    <div>
        <button onclick="exportAnalytics()" class="btn btn-outline-success">
            <i class="bi bi-download me-2"></i>Export Data
        </button>
        <a href="bulk_sms.php" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>Create Campaign
        </a>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
<?php endif; ?>

<!-- Date Range Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="<?php echo htmlspecialchars($startDate); ?>" max="<?php echo date('Y-m-d'); ?>">
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="<?php echo htmlspecialchars($endDate); ?>" max="<?php echo date('Y-m-d'); ?>">
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-funnel me-2"></i>Apply Filter
                </button>
                <button type="button" onclick="setQuickRange('today')" class="btn btn-outline-secondary">Today</button>
                <button type="button" onclick="setQuickRange('week')" class="btn btn-outline-secondary">This Week</button>
                <button type="button" onclick="setQuickRange('month')" class="btn btn-outline-secondary">This Month</button>
            </div>
        </form>
    </div>
</div>

<!-- Overview Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card analytics-card primary h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Campaigns</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($analytics['overview']['total_campaigns'] ?? 0); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-megaphone fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card analytics-card success h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Messages Sent</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($analytics['overview']['total_sent'] ?? 0); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-send fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card analytics-card info h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Delivered</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($analytics['overview']['total_delivered'] ?? 0); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card analytics-card warning h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Delivery Rate</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($analytics['overview']['avg_delivery_rate'] ?? 0, 1); ?>%</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Performance Chart -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">SMS Performance Over Time</h6>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Top Performing Campaigns -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Top Performing Campaigns</h6>
            </div>
            <div class="card-body">
                <?php if (empty($analytics['top_campaigns'])): ?>
                    <div class="text-center py-3">
                        <i class="bi bi-graph-up text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No completed campaigns in the selected date range.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm performance-table">
                            <thead>
                                <tr>
                                    <th>Campaign Name</th>
                                    <th>Recipients</th>
                                    <th>Sent</th>
                                    <th>Delivered</th>
                                    <th>Delivery Rate</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($analytics['top_campaigns'] as $campaign): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($campaign['campaign_name']); ?></td>
                                        <td><?php echo number_format($campaign['total_recipients'] ?? 0); ?></td>
                                        <td><?php echo number_format($campaign['sent_count'] ?? 0); ?></td>
                                        <td><?php echo number_format($campaign['delivered_count'] ?? 0); ?></td>
                                        <td>
                                            <?php
                                            $rate = $campaign['delivery_rate'];
                                            $class = $rate >= 95 ? 'excellent' : ($rate >= 85 ? 'good' : ($rate >= 70 ? 'average' : 'poor'));
                                            ?>
                                            <span class="delivery-rate <?php echo $class; ?>"><?php echo $rate; ?>%</span>
                                        </td>
                                        <td><?php echo date('M j', strtotime($campaign['created_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Status Distribution & Recent Activity -->
    <div class="col-lg-4">
        <!-- Status Distribution -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Campaign Status Distribution</h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 250px;">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
            </div>
            <div class="card-body">
                <?php if (empty($analytics['recent_activity'])): ?>
                    <div class="text-center py-3">
                        <i class="bi bi-clock text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No recent activity in the selected date range.</p>
                    </div>
                <?php else: ?>
                    <div class="timeline">
                        <?php foreach ($analytics['recent_activity'] as $activity): ?>
                            <div class="timeline-item mb-3">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <?php
                                        $statusColors = [
                                            'completed' => 'success',
                                            'sending' => 'warning',
                                            'scheduled' => 'info',
                                            'failed' => 'danger',
                                            'cancelled' => 'secondary'
                                        ];
                                        $color = $statusColors[$activity['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $color; ?> rounded-pill"><?php echo ucfirst($activity['status']); ?></span>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($activity['campaign_name']); ?></h6>
                                        <p class="mb-1 small text-muted">
                                            <?php echo number_format($activity['total_recipients']); ?> recipients
                                            <?php if ($activity['sent_count'] > 0): ?>
                                                • <?php echo number_format($activity['delivered_count']); ?>/<?php echo number_format($activity['sent_count']); ?> delivered
                                            <?php endif; ?>
                                        </p>
                                        <small class="text-muted"><?php echo date('M j, Y g:i A', strtotime($activity['created_at'])); ?></small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Performance Chart
const performanceData = <?php echo json_encode($analytics['daily_performance']); ?>;
const performanceCtx = document.getElementById('performanceChart').getContext('2d');

new Chart(performanceCtx, {
    type: 'line',
    data: {
        labels: performanceData.map(item => new Date(item.date).toLocaleDateString()),
        datasets: [{
            label: 'Messages Sent',
            data: performanceData.map(item => item.messages_sent),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'Messages Delivered',
            data: performanceData.map(item => item.messages_delivered),
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                position: 'top'
            }
        }
    }
});

// Status Distribution Chart
const statusData = <?php echo json_encode($analytics['status_distribution']); ?>;
const statusCtx = document.getElementById('statusChart').getContext('2d');

new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: statusData.map(item => item.status.charAt(0).toUpperCase() + item.status.slice(1)),
        datasets: [{
            data: statusData.map(item => item.count),
            backgroundColor: [
                '#28a745', // completed - green
                '#007bff', // scheduled - blue
                '#ffc107', // sending - yellow
                '#dc3545', // failed - red
                '#6c757d', // cancelled - gray
                '#17a2b8'  // draft - teal
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Quick date range functions
function setQuickRange(range) {
    const today = new Date();
    let startDate, endDate;
    
    switch(range) {
        case 'today':
            startDate = endDate = today.toISOString().split('T')[0];
            break;
        case 'week':
            const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
            startDate = weekStart.toISOString().split('T')[0];
            endDate = new Date().toISOString().split('T')[0];
            break;
        case 'month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
            endDate = new Date().toISOString().split('T')[0];
            break;
    }
    
    document.getElementById('start_date').value = startDate;
    document.getElementById('end_date').value = endDate;
    document.querySelector('form').submit();
}

// Export analytics data
function exportAnalytics() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    // Create CSV content
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "SMS Analytics Report\n";
    csvContent += "Date Range: " + startDate + " to " + endDate + "\n\n";
    
    // Overview data
    csvContent += "Overview Statistics\n";
    csvContent += "Total Campaigns,<?php echo $analytics['overview']['total_campaigns']; ?>\n";
    csvContent += "Total Recipients,<?php echo $analytics['overview']['total_recipients']; ?>\n";
    csvContent += "Messages Sent,<?php echo $analytics['overview']['total_sent']; ?>\n";
    csvContent += "Messages Delivered,<?php echo $analytics['overview']['total_delivered']; ?>\n";
    csvContent += "Messages Failed,<?php echo $analytics['overview']['total_failed']; ?>\n";
    csvContent += "Average Delivery Rate,<?php echo $analytics['overview']['avg_delivery_rate']; ?>%\n\n";
    
    // Top campaigns
    csvContent += "Top Performing Campaigns\n";
    csvContent += "Campaign Name,Recipients,Sent,Delivered,Delivery Rate,Date\n";
    <?php foreach ($analytics['top_campaigns'] as $campaign): ?>
    csvContent += "<?php echo addslashes($campaign['campaign_name']); ?>,<?php echo $campaign['total_recipients']; ?>,<?php echo $campaign['sent_count']; ?>,<?php echo $campaign['delivered_count']; ?>,<?php echo $campaign['delivery_rate']; ?>%,<?php echo date('Y-m-d', strtotime($campaign['created_at'])); ?>\n";
    <?php endforeach; ?>
    
    // Download the file
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "sms_analytics_" + startDate + "_to_" + endDate + ".csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>

<?php include 'includes/footer.php'; ?>
