List destructing with keys
-----
<?php

list('a' => $b) = ['a' => 'b'];
list('a' => list($b => $c), 'd' => $e) = $x;
-----
array(
    0: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_List(
                items: array(
                    0: ArrayItem(
                        key: Scalar_String(
                            value: a
                        )
                        value: Expr_Variable(
                            name: b
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            expr: Expr_Array(
                items: array(
                    0: ArrayItem(
                        key: Scalar_String(
                            value: a
                        )
                        value: Scalar_String(
                            value: b
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_List(
                items: array(
                    0: ArrayItem(
                        key: Scalar_String(
                            value: a
                        )
                        value: Expr_List(
                            items: array(
                                0: ArrayItem(
                                    key: Expr_Variable(
                                        name: b
                                    )
                                    value: Expr_Variable(
                                        name: c
                                    )
                                    byRef: false
                                    unpack: false
                                )
                            )
                        )
                        byRef: false
                        unpack: false
                    )
                    1: ArrayItem(
                        key: Scalar_String(
                            value: d
                        )
                        value: Expr_Variable(
                            name: e
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            expr: Expr_Variable(
                name: x
            )
        )
    )
)
