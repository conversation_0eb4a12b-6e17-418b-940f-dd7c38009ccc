Try/Catch without variable
-----
<?php

try {

} catch (Exception) {

}
-----
array(
    0: Stmt_TryCatch(
        stmts: array(
        )
        catches: array(
            0: Stmt_Catch(
                types: array(
                    0: Name(
                        name: Exception
                    )
                )
                var: null
                stmts: array(
                )
            )
        )
        finally: null
    )
)
