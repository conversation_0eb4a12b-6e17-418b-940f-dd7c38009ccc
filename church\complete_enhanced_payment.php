<?php
/**
 * Enhanced Payment Completion Handler
 * 
 * Handles payment completion and sends automatic email notifications with sender and organization information
 */

require_once 'config.php';
require_once 'includes/email_functions.php';
require_once 'includes/EnhancedDonationNotifier.php';

// Get PayPal token from URL
$token = $_GET['token'] ?? '';
if (!$token) {
    header("Location: donation_error.php");
    exit();
}

// Get organization information
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('organization_name', 'organization_type', 'sender_name', 'sender_email')");
$stmt->execute();
$org_settings = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $org_settings[$row['setting_key']] = $row['setting_value'];
}

$organization_name = $org_settings['organization_name'] ?? get_organization_name();
$organization_type = $org_settings['organization_type'] ?? 'church';
$system_sender_name = $org_settings['sender_name'] ?? $organization_name;
$system_sender_email = $org_settings['sender_email'] ?? '<EMAIL>';

// Get payment settings
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM payment_settings");
$stmt->execute();
$payment_settings = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $payment_settings[$row['setting_key']] = $row['setting_value'];
}

// Initialize PayPal
require_once 'vendor/autoload.php';

$paypal_client_id = $payment_settings['paypal_client_id'] ?? '';
$paypal_client_secret = $payment_settings['paypal_client_secret'] ?? '';
$paypal_sandbox = isset($payment_settings['paypal_sandbox_mode']) && $payment_settings['paypal_sandbox_mode'] === '1';

$environment = $paypal_sandbox ? 
    new \PayPalCheckoutSdk\Core\SandboxEnvironment($paypal_client_id, $paypal_client_secret) :
    new \PayPalCheckoutSdk\Core\ProductionEnvironment($paypal_client_id, $paypal_client_secret);

$client = new \PayPalCheckoutSdk\Core\PayPalHttpClient($environment);

try {
    // Get the order details
    $response = $client->execute(new \PayPalCheckoutSdk\Orders\OrdersGetRequest($token));
    
    // Get donation details from reference_id
    $donation_id = $response->result->purchase_units[0]->reference_id;
    
    // Start transaction
    $pdo->beginTransaction();
    
    // Update donation status
    $stmt = $pdo->prepare("UPDATE donations SET payment_status = 'completed', delivered_at = NOW() WHERE id = ?");
    $stmt->execute([$donation_id]);
    
    // Update transaction status
    $stmt = $pdo->prepare("UPDATE payment_transactions 
                          SET payment_status = 'completed', 
                              payment_method_details = ? 
                          WHERE donation_id = ? AND payment_provider = 'paypal'");
    $stmt->execute([json_encode($response->result), $donation_id]);
    
    // Send automatic notifications using the enhanced notification system
    $notifier = new EnhancedDonationNotifier($pdo);
    $notification_result = $notifier->sendDonationNotifications($donation_id);

    if (!$notification_result['success']) {
        error_log("Failed to send donation notifications: " . $notification_result['error']);
        // Continue processing even if notifications fail
    }

    
    $pdo->commit();
    
    // Redirect to success page
    header("Location: donation_success.php?id=" . $donation_id);
    exit();
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Enhanced Payment Error: " . $e->getMessage());
    header("Location: donation_error.php");
    exit();
}

?>
