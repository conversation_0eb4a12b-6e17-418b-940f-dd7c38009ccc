-- Events Management System Tables
-- This file creates all necessary tables for the events management system

-- Create events table
CREATE TABLE IF NOT EXISTS events (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_date DATETIME NOT NULL,
    location VARCHAR(255),
    max_attendees INT(11) DEFAULT NULL,
    category_id INT(11) DEFAULT NULL,
    created_by INT(11) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_event_date (event_date),
    INDEX idx_category_id (category_id),
    INDEX idx_created_by (created_by),
    INDEX idx_is_active (is_active)
);

-- Create event_categories table
CREATE TABLE IF NOT EXISTS event_categories (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#007bff',
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_name (name)
);

-- Create event_files table (enhanced for promotional materials)
CREATE TABLE IF NOT EXISTS event_files (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    event_id INT(11) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(100),
    file_size INT(11),
    uploaded_by INT(11) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    file_category ENUM('promotional', 'document', 'other') DEFAULT 'document',
    is_header_banner TINYINT(1) DEFAULT 0,
    display_order INT(11) DEFAULT 0,
    alt_text VARCHAR(255) DEFAULT NULL,
    INDEX idx_event_id (event_id),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_file_category (file_category),
    INDEX idx_is_header_banner (is_header_banner),
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- Create event_rsvps table
CREATE TABLE IF NOT EXISTS event_rsvps (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    event_id INT(11) NOT NULL,
    member_id INT(11) NOT NULL,
    status ENUM('attending', 'not_attending', 'maybe') DEFAULT 'attending',
    guest_count INT(11) DEFAULT 0,
    notes TEXT,
    rsvp_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_event_id (event_id),
    INDEX idx_member_id (member_id),
    INDEX idx_status (status),
    UNIQUE KEY unique_event_member (event_id, member_id),
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- Insert default event categories
INSERT IGNORE INTO event_categories (name, description, color) VALUES
('Worship Service', 'Regular worship services and special services', '#007bff'),
('Bible Study', 'Bible study sessions and small groups', '#28a745'),
('Fellowship', 'Fellowship events and social gatherings', '#ffc107'),
('Outreach', 'Community outreach and evangelism events', '#dc3545'),
('Youth', 'Youth ministry events and activities', '#6f42c1'),
('Children', 'Children ministry events and programs', '#fd7e14'),
('Music', 'Music ministry events and concerts', '#20c997'),
('Prayer', 'Prayer meetings and prayer events', '#6c757d'),
('Conference', 'Conferences, seminars, and special meetings', '#e83e8c'),
('Holiday', 'Holiday celebrations and special occasions', '#17a2b8');
