<?php
// Include session manager for timeout handling
require_once __DIR__ . '/session-manager.php';

// Include language system
require_once __DIR__ . '/language.php';

// Include admin notification functions
require_once __DIR__ . '/admin_notification_functions.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: " . admin_url_for('login.php'));
    exit();
}
?>
<!DOCTYPE html>
<html lang="<?php echo get_current_language(); ?>" dir="<?php echo get_language_direction(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="generator" content="<?php echo get_organization_name(); ?> Management System v1.0">
    <!-- Debug info for path troubleshooting -->
    <meta name="debug-site-url" content="<?php echo stripslashes(SITE_URL); ?>">
    <meta name="debug-admin-url" content="<?php echo stripslashes(ADMIN_URL); ?>">
    <meta name="debug-env" content="<?php echo $environment ?? 'unknown'; ?>">
    <!-- Favicon Support -->
    <?php
    $favicon = get_site_setting('favicon_logo', '');
    if (!empty($favicon)): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo get_base_url() . '/' . htmlspecialchars($favicon); ?>">
        <link rel="shortcut icon" type="image/x-icon" href="<?php echo get_base_url() . '/' . htmlspecialchars($favicon); ?>">
    <?php else: ?>
        <link rel="icon" type="image/x-icon" href="<?php echo get_base_url(); ?>/assets/images/favicon.ico">
    <?php endif; ?>

    <title><?php echo isset($page_title) ? $page_title : 'Admin Panel'; ?> - <?php echo get_site_title(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons (primary) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap Icons (backup) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Added cache-busting parameter to prevent caching issues -->
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/admin-css-proxy.php?t=<?php echo time(); ?>">
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/font-fix.css?t=<?php echo time(); ?>">
    <link rel="stylesheet" href="../css/promotional-materials.css?t=<?php echo time(); ?>">

    <!-- Custom Theme CSS -->
    <?php
    $customThemeFile = __DIR__ . '/../css/custom-theme.css';
    if (file_exists($customThemeFile)): ?>
        <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/custom-theme.css?t=<?php echo filemtime($customThemeFile); ?>">
    <?php endif; ?>

    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/dark-mode.css?t=<?php echo time(); ?>">

    <!-- Accessibility CSS -->
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/accessibility.css?t=<?php echo time(); ?>">

    <?php if (isset($extra_css)): ?>
        <?php echo $extra_css; ?>
    <?php endif; ?>
    
    <!-- Base URL for JavaScript -->
    <script>
        var BASE_URL = '<?php echo get_base_url(); ?>';
        var ADMIN_URL = '<?php echo get_admin_url(); ?>';
        var SITE_URL = '<?php echo SITE_URL; ?>';
    </script>

    <!-- Admin Top Navigation Styles -->
    <style>
        /* Admin Top Navigation Bar - sticky positioned within main content */
        .admin-top-nav {
            background: linear-gradient(135deg, #343a40 0%, #495057 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 0.75rem 0;
            margin: -var(--admin-vertical-spacing) 0 1.5rem 0;
            border-radius: 0;
            position: sticky;
            top: 0;
            z-index: 1020;
            width: 100%;
            box-sizing: border-box;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* Admin Profile Button */
        .admin-profile-btn {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            color: #ffffff;
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.375rem;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .admin-profile-btn:hover,
        .admin-profile-btn:focus {
            background-color: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border-color: rgba(255, 255, 255, 0.3);
        }

        .admin-profile-btn.show {
            background-color: rgba(255, 255, 255, 0.15);
            color: #ffffff;
        }

        .admin-profile-img {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid rgba(255, 255, 255, 0.3);
            margin-right: 0.5rem;
        }

        .admin-profile-icon {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 50%;
            margin-right: 0.5rem;
            font-size: 12px;
            color: white;
        }

        .admin-profile-name {
            font-weight: 500;
            font-size: 0.9rem;
            margin-right: 0.5rem;
        }

        /* Admin Profile Dropdown */
        .admin-profile-dropdown {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 0.5rem;
            min-width: 280px;
            margin-top: 0.25rem;
        }

        .admin-profile-header {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
        }

        .admin-profile-header-img {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #e9ecef;
            margin-right: 0.75rem;
        }

        .admin-profile-header-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 50%;
            margin-right: 0.75rem;
            color: white;
            font-size: 18px;
        }

        .admin-profile-info {
            flex: 1;
        }

        .admin-profile-full-name {
            font-weight: 600;
            font-size: 0.95rem;
            color: #212529;
            margin-bottom: 0.25rem;
        }

        .admin-profile-role {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .admin-profile-dropdown .dropdown-item {
            padding: 0.75rem 1rem;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
        }

        .admin-profile-dropdown .dropdown-item i {
            width: 18px;
            margin-right: 0.75rem;
            text-align: center;
            font-size: 0.9rem;
        }

        .admin-profile-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #495057;
        }

        .admin-profile-dropdown .logout-link:hover {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* Sticky header enhancements */
        .admin-top-nav.scrolled {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            background: linear-gradient(135deg, rgba(52, 58, 64, 0.95) 0%, rgba(73, 80, 87, 0.95) 100%);
        }

        /* Smooth transition for sticky header */
        .admin-top-nav {
            transition: all 0.3s ease;
        }

        /* Ensure content doesn't jump when header becomes sticky */
        .main-content {
            scroll-behavior: smooth;
        }

        /* Add subtle animation when dropdown opens on sticky header */
        .admin-top-nav .dropdown-menu {
            animation: fadeInDown 0.3s ease;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .admin-top-nav {
                padding: 0.75rem 0;
            }

            .admin-profile-name {
                display: none;
            }

            .admin-profile-dropdown {
                min-width: 250px;
            }
        }
    </style>

    <!-- Theme Manager -->
    <script src="<?php echo ADMIN_URL; ?>/js/theme-manager.js?t=<?php echo time(); ?>"></script>

    <!-- Accessibility Manager -->
    <script src="<?php echo ADMIN_URL; ?>/js/accessibility-manager.js?t=<?php echo time(); ?>"></script>

    <!-- Language Switcher -->
    <script src="<?php echo ADMIN_URL; ?>/js/language-switcher.js?t=<?php echo time(); ?>"></script>

    <!-- Admin Notifications -->
    <script>
        // Admin notification system
        let notificationCheckInterval;

        function updateNotificationBadge() {
            fetch('<?php echo ADMIN_URL; ?>/ajax/get_admin_notifications.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const badge = document.querySelector('.nav-link[href*="notifications.php"] .badge');
                        if (data.unread_count > 0) {
                            if (badge) {
                                badge.textContent = data.unread_count > 99 ? '99+' : data.unread_count;
                                badge.style.display = 'inline-block';
                            } else {
                                // Create badge if it doesn't exist
                                const notificationLink = document.querySelector('.nav-link[href*="notifications.php"]');
                                if (notificationLink) {
                                    const newBadge = document.createElement('span');
                                    newBadge.className = 'badge bg-danger rounded-pill position-absolute';
                                    newBadge.style.fontSize = '0.65rem';
                                    newBadge.style.top = '-8px';
                                    newBadge.style.right = '-8px';
                                    newBadge.style.minWidth = '18px';
                                    newBadge.style.height = '18px';
                                    newBadge.style.display = 'flex';
                                    newBadge.style.alignItems = 'center';
                                    newBadge.style.justifyContent = 'center';
                                    newBadge.style.zIndex = '10';
                                    newBadge.style.border = '2px solid white';
                                    newBadge.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
                                    newBadge.textContent = data.unread_count > 99 ? '99+' : data.unread_count;
                                    notificationLink.appendChild(newBadge);
                                }
                            }
                        } else {
                            if (badge) {
                                badge.style.display = 'none';
                            }
                        }
                    }
                })
                .catch(error => {
                    console.log('Notification check failed:', error);
                });
        }

        // Check for new notifications every 30 seconds
        document.addEventListener('DOMContentLoaded', function() {
            updateNotificationBadge(); // Initial check
            notificationCheckInterval = setInterval(updateNotificationBadge, 30000);
        });

        // Clear interval when page unloads
        window.addEventListener('beforeunload', function() {
            if (notificationCheckInterval) {
                clearInterval(notificationCheckInterval);
            }
        });

        // Admin Profile Dropdown Functions
        function confirmLogout(event) {
            event.preventDefault();

            if (confirm('Are you sure you want to logout?')) {
                // Show loading state
                const logoutLink = event.target.closest('.logout-link');
                const originalContent = logoutLink.innerHTML;
                logoutLink.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging out...';
                logoutLink.style.pointerEvents = 'none';

                // Redirect to logout
                setTimeout(function() {
                    window.location.href = '<?php echo admin_url_for('logout.php'); ?>';
                }, 500);
            }
        }

        // Sticky Header Scroll Enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const adminHeader = document.querySelector('.admin-top-nav');
            const mainContent = document.querySelector('.main-content');

            if (adminHeader && mainContent) {
                let lastScrollTop = 0;
                let ticking = false;

                function updateHeaderOnScroll() {
                    const scrollTop = mainContent.scrollTop || document.documentElement.scrollTop;

                    // Add scrolled class when user scrolls down
                    if (scrollTop > 10) {
                        adminHeader.classList.add('scrolled');
                    } else {
                        adminHeader.classList.remove('scrolled');
                    }

                    lastScrollTop = scrollTop;
                    ticking = false;
                }

                function requestTick() {
                    if (!ticking) {
                        requestAnimationFrame(updateHeaderOnScroll);
                        ticking = true;
                    }
                }

                // Listen for scroll events on both main content and window
                mainContent.addEventListener('scroll', requestTick);
                window.addEventListener('scroll', requestTick);

                // Initial check
                updateHeaderOnScroll();
            }
        });
    </script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include the sidebar -->
            <?php include dirname(__FILE__) . '/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Admin Top Navigation Bar (only over main content) -->
                <div class="admin-top-nav">
                    <div class="d-flex justify-content-end align-items-center">
                        <!-- Admin Profile Dropdown -->
                        <div class="dropdown">
                            <button class="btn admin-profile-btn dropdown-toggle" type="button" id="adminProfileDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <?php
                                // Get admin profile picture or use default icon
                                // Check if admin has profile image in database
                                $admin_image = '';
                                $admin_name = $_SESSION['admin_name'] ?? $_SESSION['admin_username'] ?? 'Admin';
                                $admin_first_name = explode(' ', $admin_name)[0];

                                // Try to get admin image from database if admin_id is available
                                if (isset($_SESSION['admin_id']) && isset($pdo)) {
                                    try {
                                        $stmt = $pdo->prepare("SELECT profile_image FROM admins WHERE id = ?");
                                        $stmt->execute([$_SESSION['admin_id']]);
                                        $result = $stmt->fetch(PDO::FETCH_ASSOC);
                                        if ($result && !empty($result['profile_image'])) {
                                            $admin_image = $result['profile_image'];
                                        }
                                    } catch (PDOException $e) {
                                        // Column might not exist, ignore error
                                    }
                                }
                                ?>

                                <?php if (!empty($admin_image) && file_exists("../uploads/admin/" . $admin_image)): ?>
                                    <img src="../uploads/admin/<?= htmlspecialchars($admin_image) ?>" alt="Admin Profile" class="admin-profile-img">
                                <?php else: ?>
                                    <i class="fas fa-user-shield admin-profile-icon"></i>
                                <?php endif; ?>

                                <span class="admin-profile-name"><?= htmlspecialchars($admin_first_name) ?></span>
                            </button>

                            <ul class="dropdown-menu dropdown-menu-end admin-profile-dropdown" aria-labelledby="adminProfileDropdown">
                                <li class="dropdown-header">
                                    <div class="admin-profile-header">
                                        <?php if (!empty($admin_image) && file_exists("../uploads/admin/" . $admin_image)): ?>
                                            <img src="../uploads/admin/<?= htmlspecialchars($admin_image) ?>" alt="Admin Profile" class="admin-profile-header-img">
                                        <?php else: ?>
                                            <div class="admin-profile-header-icon">
                                                <i class="fas fa-user-shield"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="admin-profile-info">
                                            <div class="admin-profile-full-name"><?= htmlspecialchars($admin_name) ?></div>
                                            <div class="admin-profile-role">Administrator</div>
                                        </div>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider"></li>

                                <li><a class="dropdown-item" href="<?= admin_url_for('profile.php') ?>">
                                    <i class="fas fa-user"></i> My Profile
                                </a></li>

                                <li><a class="dropdown-item" href="<?= admin_url_for('settings.php') ?>">
                                    <i class="fas fa-cog"></i> Settings
                                </a></li>

                                <li><a class="dropdown-item" href="<?= admin_url_for('appearance_settings.php') ?>">
                                    <i class="fas fa-palette"></i> Appearance Settings
                                </a></li>

                                <li><a class="dropdown-item" href="<?= admin_url_for('members.php') ?>">
                                    <i class="fas fa-users"></i> View Members
                                </a></li>

                                <li><a class="dropdown-item" href="<?= admin_url_for('profile.php') ?>#password-section">
                                    <i class="fas fa-key"></i> Change Password
                                </a></li>

                                <li><hr class="dropdown-divider"></li>

                                <li><a class="dropdown-item logout-link" href="#" onclick="confirmLogout(event)">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php if (isset($page_header)): ?>
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h2><?php echo $page_header; ?></h2>
                        <?php if (isset($page_description)): ?>
                            <p><?php echo $page_description; ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (isset($message) && !empty($message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error) && !empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?> 