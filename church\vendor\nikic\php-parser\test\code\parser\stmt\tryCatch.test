Try/catch
-----
<?php

try {
    doTry();
} catch (A $b) {
    doCatchA();
} catch (B $c) {
    doCatchB();
} finally {
    doFinally();
}

// no finally
try { }
catch (A $b) { }

// no catch
try { }
finally { }

-----
array(
    0: Stmt_TryCatch(
        stmts: array(
            0: Stmt_Expression(
                expr: Expr_FuncCall(
                    name: Name(
                        name: doTry
                    )
                    args: array(
                    )
                )
            )
        )
        catches: array(
            0: Stmt_Catch(
                types: array(
                    0: Name(
                        name: A
                    )
                )
                var: Expr_Variable(
                    name: b
                )
                stmts: array(
                    0: Stmt_Expression(
                        expr: Expr_FuncCall(
                            name: Name(
                                name: doCatchA
                            )
                            args: array(
                            )
                        )
                    )
                )
            )
            1: Stmt_Catch(
                types: array(
                    0: Name(
                        name: B
                    )
                )
                var: Expr_Variable(
                    name: c
                )
                stmts: array(
                    0: Stmt_Expression(
                        expr: Expr_FuncCall(
                            name: Name(
                                name: doCatchB
                            )
                            args: array(
                            )
                        )
                    )
                )
            )
        )
        finally: Stmt_Finally(
            stmts: array(
                0: Stmt_Expression(
                    expr: Expr_FuncCall(
                        name: Name(
                            name: doFinally
                        )
                        args: array(
                        )
                    )
                )
            )
        )
    )
    1: Stmt_TryCatch(
        stmts: array(
        )
        catches: array(
            0: Stmt_Catch(
                types: array(
                    0: Name(
                        name: A
                    )
                )
                var: Expr_Variable(
                    name: b
                )
                stmts: array(
                )
            )
        )
        finally: null
        comments: array(
            0: // no finally
        )
    )
    2: Stmt_TryCatch(
        stmts: array(
        )
        catches: array(
        )
        finally: Stmt_Finally(
            stmts: array(
            )
        )
        comments: array(
            0: // no catch
        )
    )
)
