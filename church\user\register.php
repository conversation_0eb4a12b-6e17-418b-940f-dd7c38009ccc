<?php
/**
 * User Registration Page
 *
 * Registration form for new church members
 */

// Include configuration first (which sets up session configuration)
require_once '../config.php';

// Start session after configuration is loaded
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get site settings
$sitename = get_site_setting('site_name', get_organization_name() . ' - Registration');

// PHP Handler Test
if (!function_exists('test_php_processing')) {
    function test_php_processing() {
        error_log('PHP processing test: ' . date('Y-m-d H:i:s'));
        return true;
    }
}
test_php_processing();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Member Registration - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>
    <!-- Preload font to avoid base64 loading -->
    <link rel="preload" href="../admin/font-access.php" as="font" type="font/ttf" crossorigin>
    <!-- Font security fix for CSP -->
    <script>
    // Override FontFace constructor to intercept base64 fonts
    if (window.FontFace) {
        const originalFontFace = window.FontFace;
        window.FontFace = function(family, source, descriptors) {
            // Check if this is a base64 font
            if (typeof source === 'string' && source.indexOf('data:application/x-font-ttf;base64,') === 0) {
                console.log('Intercepted base64 font:', family);
                // Replace with our proxy
                source = '../admin/font-access.php';
            }
            return new originalFontFace(family, source, descriptors);
        };
    }

    // Create a global error handler for font loading errors
    window.addEventListener('error', function(e) {
        // Check if this is a font loading error
        if (e.message && (e.message.includes('font') || e.filename && e.filename.includes('font'))) {
            console.error('Font loading error:', e);
            // Try to load the font through our proxy
            if (e.target && e.target.href && e.target.rel === 'stylesheet') {
                const fontCss = document.createElement('link');
                fontCss.rel = 'stylesheet';
                fontCss.href = '../admin/font-access.php';
                document.head.appendChild(fontCss);
            }
        }
    }, true);
    </script>
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%);
            font-family: 'Roboto', sans-serif;
            color: #2c3e50;
            min-height: 100vh;
            padding: 20px 0;
        }
        .container {
            padding-left: 10px;
            padding-right: 10px;
        }
        .header-image {
            background: url('../assets/images/banner.jpg') no-repeat center;
            background-size: cover;
            height: 200px;
            border-radius: 20px 20px 0 0;
            position: relative;
            margin-bottom: -20px;
            overflow: hidden;
        }
        .header-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(26, 54, 93, 0.8); /* Darkened for better text readability */
            border-radius: 20px 20px 0 0;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column; /* Stack items vertically */
            padding: 10px;
        }
        .header-text {
            color: white;
            text-align: center;
            padding: 5px;
            width: 100%;
            max-width: 100%;
        }
        .header-text h1 {
            font-family: 'Playfair Display', serif;
            font-size: 2.2rem;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            line-height: 1.2;
        }
        .header-text p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }
        .header-text .btn {
            margin-top: 5px;
            white-space: normal;
            word-wrap: break-word;
        }
        .form-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
            border-radius: 20px;
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            transition: transform 0.3s ease;
            overflow: hidden;
        }
        .form-content {
            padding: 40px;
        }
        .form-group {
            position: relative;
            margin-bottom: 25px;
        }
        .form-label {
            font-weight: 500;
            color: #2d3748;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }
        .form-label i {
            margin-right: 8px;
            color: #4a5568;
        }
        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 0.75rem;
            transition: all 0.3s ease;
            background-color: rgba(255, 255, 255, 0.9);
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        .form-control:focus {
            border-color: #4a5568;
            box-shadow: 0 0 0 0.2rem rgba(74, 85, 104, 0.2);
            background-color: #ffffff;
        }
        .preview-image {
            max-width: 200px;
            margin-top: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .btn-primary {
            background: linear-gradient(45deg, #1a365d 0%, #2c5282 100%);
            border: none;
            padding: 15px 40px;
            font-weight: 500;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(26, 54, 93, 0.3);
            background: linear-gradient(45deg, #2c5282 0%, #1a365d 100%);
        }
        .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            animation: slideIn 0.5s ease-out;
            margin-bottom: 20px;
            width: 100%;
            max-width: 100%;
            word-wrap: break-word;
        }
        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
        .required-field::after {
            content: '*';
            color: #e53e3e;
            margin-left: 4px;
        }
        @media (max-width: 768px) {
            .form-container { margin: 15px; }
            .header-text h1 { font-size: 1.8rem; }
            .header-text p { font-size: 0.95rem; }
            .form-content { padding: 20px; }
            .btn-primary { padding: 12px 30px; width: 100%; }
            .header-image { height: 150px; }
            .row { margin-right: 0; margin-left: 0; }
            .col-md-6 { padding-right: 0; padding-left: 0; }
        }

        /* Small phones */
        @media (max-width: 576px) {
            .form-container { margin: 10px; border-radius: 15px; }
            .header-image {
                height: 130px;
                border-radius: 15px 15px 0 0;
            }
            .header-overlay {
                border-radius: 15px 15px 0 0;
                padding: 5px 0;
            }
            .header-text {
                padding: 0;
                width: 90%;
            }
            .header-text h1 {
                font-size: 1.3rem;
                margin-bottom: 4px;
                padding: 0 5px;
                line-height: 1.1;
            }
            .header-text p {
                font-size: 0.85rem;
                margin-bottom: 4px;
                padding: 0 5px;
                line-height: 1.2;
            }
            .btn-outline-light {
                font-size: 0.75rem;
                padding: 5px 10px;
            }
            .header-button-container {
                margin-top: 5px;
            }
            .form-content { padding: 15px; }
            .alert { padding: 10px; }
            .card-title { font-size: 1.1rem; }
            .card-text { font-size: 0.9rem; }
            .form-label { font-size: 0.9rem; }
            .form-control { padding: 0.5rem; font-size: 0.9rem; }
            .form-text { font-size: 0.8rem; }
            input[type="file"].form-control { padding: 0.4rem; }
            .preview-image { max-width: 150px; }
        }

        /* Fix for date inputs on mobile */
        input[type="date"].form-control {
            -webkit-appearance: none;
            min-height: 2.8rem;
        }

        /* Add responsive container */
        .mobile-container {
            width: 100%;
            max-width: 100%;
            overflow-x: hidden;
        }

        /* Back to Main Site button container */
        .header-button-container {
            text-align: center;
            margin-top: 8px;
            width: 100%;
        }

        /* Ensure the button is responsive */
        .btn-outline-light {
            border: 1px solid white;
            white-space: normal;
            display: inline-block;
            font-size: 0.9rem;
            padding: 8px 16px;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <div class="container">
            <div class="form-container">
                <div class="header-image">
                    <div class="header-overlay">
                        <div class="header-text">
                            <h1><?php echo get_organization_name(); ?></h1>
                            <p>Join our community</p>
                            <div class="header-button-container">
                                <a href="login.php" class="btn btn-outline-light me-2">
                                    <i class="fas fa-sign-in-alt me-1"></i>Member Login
                                </a>
                                <a href="../admin/login.php" class="btn btn-outline-light me-2">
                                    <i class="fas fa-user-shield me-1"></i>Admin Login
                                </a>
                                <a href="../../index.html" class="btn btn-outline-light">
                                    <i class="fas fa-home me-2"></i>Back to Main Site
                                </a>
                            </div>
                        </div>
                    </div>
                <div class="form-content">
                    <?php
                    if (isset($_GET['success']) && $_GET['success'] == 1) {
                        if (isset($_GET['email']) && $_GET['email'] == 1) {
                            echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fas fa-check-circle me-2"></i> Registration successful! A welcome email has been sent to your email address.
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                  </div>';
                        } else {
                            echo '<div class="alert alert-warning alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i> Registration successful! However, we couldn\'t send the welcome email. Please contact support.
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                  </div>';
                        }
                    } elseif (isset($_GET['error'])) {
                        if ($_GET['error'] == 'duplicate_email') {
                            echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-times-circle me-2"></i> This email address is already registered. Please use a different email.
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                  </div>';
                        } else {
                            echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle me-2"></i> An error occurred during registration. Please try again later.
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                  </div>';
                        }
                    }
                    ?>

                    <div class="card mb-4 bg-light">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-info-circle text-primary me-2"></i>Welcome to Our Registration</h5>
                            <p class="card-text">Please complete this form to join our church family. Fields marked with <span class="text-danger">*</span> are required. Your information helps us better serve you and connect you with our community.</p>
                            <p class="card-text fst-italic text-center">"For where two or three gather in my name, there am I with them." — Matthew 18:20</p>
                        </div>
                    </div>

                    <form action="../process_registration.php" method="POST" enctype="multipart/form-data">
                        <div class="row g-3">
                            <div class="col-12 col-md-6 form-group">
                                <label for="full_name" class="form-label required-field"><i class="fas fa-user"></i> Full Name</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required data-bs-toggle="tooltip" data-bs-placement="top" title="Please enter your full name as it appears on official documents">
                            </div>
                            <div class="col-12 col-md-6 form-group">
                                <label for="occupation" class="form-label required-field"><i class="fas fa-briefcase"></i> Occupation</label>
                                <input type="text" class="form-control" id="occupation" name="occupation" required data-bs-toggle="tooltip" data-bs-placement="top" title="Your current job or profession">
                            </div>
                        </div>

                        <div class="form-group mt-3">
                            <label for="profile_image" class="form-label"><i class="fas fa-camera"></i> Profile Image</label>
                            <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*" onchange="previewImage(this)" data-bs-toggle="tooltip" data-bs-placement="top" title="Upload a recent photo for our church directory">
                            <img id="preview" class="preview-image d-none mt-2">
                            <small class="form-text text-muted">Your photo helps our community recognize and welcome you.</small>
                        </div>

                        <div class="row g-3 mt-2">
                            <div class="col-12 col-md-6 form-group">
                                <label for="email" class="form-label required-field"><i class="fas fa-envelope"></i> Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" required data-bs-toggle="tooltip" data-bs-placement="top" title="We'll send important church announcements and your birthday blessings to this email">
                                <small class="form-text text-muted">We respect your privacy and will never share your email with third parties.</small>
                            </div>
                            <div class="col-12 col-md-6 form-group">
                                <label for="phone_number" class="form-label required-field"><i class="fas fa-phone"></i> Phone Number</label>
                                <input type="tel" class="form-control" id="phone_number" name="phone_number" placeholder="+27 XX XXX XXXX" required data-bs-toggle="tooltip" data-bs-placement="top" title="Please include your country code (e.g., +27 for South Africa, +1 for USA)">
                                <small class="form-text text-muted">Include your country code (e.g., +27 for South Africa) for international communication.</small>
                            </div>
                        </div>

                        <div class="form-group mt-3">
                            <label for="home_address" class="form-label required-field"><i class="fas fa-home"></i> Home Address</label>
                            <textarea class="form-control" id="home_address" name="home_address" rows="2" required data-bs-toggle="tooltip" data-bs-placement="top" title="For home visitations and sending physical mail when needed"></textarea>
                            <small class="form-text text-muted">This helps our pastoral team arrange home visits if requested.</small>
                        </div>

                        <div class="form-group mt-3">
                            <label for="birth_date" class="form-label required-field"><i class="fas fa-calendar"></i> Birth Date</label>
                            <input type="date" class="form-control" id="birth_date" name="birth_date" required data-bs-toggle="tooltip" data-bs-placement="top" title="We celebrate our members' birthdays with special blessings">
                            <small class="form-text text-muted">We'll send you birthday blessings on your special day!</small>
                        </div>

                        <div class="row g-3 mt-2">
                            <div class="col-12 col-md-6 form-group">
                                <label for="password" class="form-label required-field"><i class="fas fa-lock"></i> Password</label>
                                <input type="password" class="form-control" id="password" name="password" required data-bs-toggle="tooltip" data-bs-placement="top" title="Create a secure password for your member account">
                                <small class="form-text text-muted">Password must be at least 8 characters with uppercase, lowercase, numbers, and special characters.</small>
                            </div>
                            <div class="col-12 col-md-6 form-group">
                                <label for="confirm_password" class="form-label required-field"><i class="fas fa-lock"></i> Confirm Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required data-bs-toggle="tooltip" data-bs-placement="top" title="Re-enter your password to confirm">
                                <small class="form-text text-muted">Must match the password above.</small>
                            </div>
                        </div>

                        <div class="form-group mt-3">
                            <label for="message" class="form-label"><i class="fas fa-comment"></i> Prayer Requests or Comments</label>
                            <textarea class="form-control" id="message" name="message" rows="3" data-bs-toggle="tooltip" data-bs-placement="top" title="Share any prayer requests, questions, or how you'd like to get involved"></textarea>
                            <small class="form-text text-muted">Let us know how we can pray for you or if you're interested in any specific ministries.</small>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary"><i class="fas fa-paper-plane me-2"></i>Join Our Church Family</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewImage(input) {
            const preview = document.getElementById('preview');
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.classList.remove('d-none');
                }
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Password confirmation validation
            const passwordField = document.getElementById('password');
            const confirmPasswordField = document.getElementById('confirm_password');

            function validatePasswordMatch() {
                if (passwordField.value !== confirmPasswordField.value) {
                    confirmPasswordField.setCustomValidity('Passwords do not match');
                } else {
                    confirmPasswordField.setCustomValidity('');
                }
            }

            if (passwordField && confirmPasswordField) {
                passwordField.addEventListener('input', validatePasswordMatch);
                confirmPasswordField.addEventListener('input', validatePasswordMatch);
            }

            // Form submission handler
            const registrationForm = document.querySelector('form[action="../process_registration.php"]');
            if (registrationForm) {
                registrationForm.addEventListener('submit', function(event) {
                    // Validate password match before submission
                    if (passwordField && confirmPasswordField) {
                        if (passwordField.value !== confirmPasswordField.value) {
                            event.preventDefault();
                            alert('Passwords do not match. Please check and try again.');
                            return false;
                        }
                    }

                    // Check form validity
                    if (this.checkValidity()) {
                        // Create and show the success message
                        const successMessage = document.createElement('div');
                        successMessage.className = 'alert alert-success alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-4 shadow-lg';
                        successMessage.style.zIndex = '9999';
                        successMessage.style.maxWidth = '90%';
                        successMessage.style.width = '500px';
                        successMessage.innerHTML = `
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">Registration Submitted!</h5>
                                    <p class="mb-0">Thank you for joining our church family. Processing your information...</p>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;

                        document.body.appendChild(successMessage);

                        // Optional: Disable the submit button to prevent double submission
                        const submitButton = this.querySelector('button[type="submit"]');
                        if (submitButton) {
                            submitButton.disabled = true;
                            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                        }

                        // Allow the message to be visible for a moment before the form submits
                        // This small delay gives users visual feedback before the page reloads
                        setTimeout(() => {
                            // Form will continue with normal submission after timeout
                        }, 800);
                    }
                });
            }
        });
    </script>
</body>
</html>