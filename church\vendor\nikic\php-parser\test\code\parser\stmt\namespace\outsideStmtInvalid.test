There (mostly) can't be statements outside of namespaces
-----
<?php
echo 1;
echo 2;
namespace A;
-----
Namespace declaration statement has to be the very first statement in the script from 4:1 to 4:9
array(
    0: Stmt_Echo(
        exprs: array(
            0: <PERSON>alar_Int(
                value: 1
            )
        )
    )
    1: Stmt_Echo(
        exprs: array(
            0: Scalar_Int(
                value: 2
            )
        )
    )
    2: Stmt_Namespace(
        name: Name(
            name: A
        )
        stmts: array(
        )
    )
)
-----
<?php
namespace A {}
echo 1;
-----
No code may exist outside of namespace {} from 3:1 to 3:7
array(
    0: Stmt_Namespace(
        name: Name(
            name: A
        )
        stmts: array(
        )
    )
    1: Stmt_Echo(
        exprs: array(
            0: Scalar_Int(
                value: 1
            )
        )
    )
)
-----
<?php
namespace A {}
declare(ticks=1);
foo();
namespace B {}
-----
No code may exist outside of namespace {} from 3:1 to 3:17
array(
    0: Stmt_Namespace(
        name: Name(
            name: A
        )
        stmts: array(
        )
    )
    1: Stmt_Declare(
        declares: array(
            0: DeclareItem(
                key: Identifier(
                    name: ticks
                )
                value: Scalar_Int(
                    value: 1
                )
            )
        )
        stmts: null
    )
    2: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: foo
            )
            args: array(
            )
        )
    )
    3: Stmt_Namespace(
        name: Name(
            name: B
        )
        stmts: array(
        )
    )
)
