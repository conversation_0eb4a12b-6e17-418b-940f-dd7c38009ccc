<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

echo "<h1>Event Reports Database Structure Test</h1>";

try {
    // Test event_rsvps table structure
    echo "<h2>event_rsvps table structure:</h2>";
    $stmt = $pdo->query("SHOW COLUMNS FROM event_rsvps");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test event_rsvps_guests table structure
    echo "<h2>event_rsvps_guests table structure:</h2>";
    $stmt = $pdo->query("SHOW COLUMNS FROM event_rsvps_guests");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test sample data
    echo "<h2>Sample event data:</h2>";
    $stmt = $pdo->query("SELECT id, title, event_date FROM events ORDER BY event_date DESC LIMIT 5");
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($events)) {
        echo "<p>No events found in database.</p>";
    } else {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Title</th><th>Event Date</th></tr>";
        foreach ($events as $event) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($event['id']) . "</td>";
            echo "<td>" . htmlspecialchars($event['title']) . "</td>";
            echo "<td>" . htmlspecialchars($event['event_date']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test RSVP data for first event
        if (!empty($events)) {
            $first_event_id = $events[0]['id'];
            echo "<h3>RSVP data for event: " . htmlspecialchars($events[0]['title']) . "</h3>";
            
            // Check which column exists
            $columns_stmt = $pdo->query("SHOW COLUMNS FROM event_rsvps");
            $columns = $columns_stmt->fetchAll(PDO::FETCH_COLUMN);
            $member_id_column = in_array('user_id', $columns) ? 'user_id' : 'member_id';
            $date_column = in_array('rsvp_date', $columns) ? 'rsvp_date' : 'created_at';
            
            echo "<p>Using column: <strong>$member_id_column</strong> for member ID</p>";
            echo "<p>Using column: <strong>$date_column</strong> for date</p>";
            
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM event_rsvps WHERE event_id = ?");
            $stmt->execute([$first_event_id]);
            $member_rsvps = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM event_rsvps_guests WHERE event_id = ?");
            $stmt->execute([$first_event_id]);
            $guest_rsvps = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            echo "<p>Member RSVPs: $member_rsvps</p>";
            echo "<p>Guest RSVPs: $guest_rsvps</p>";
        }
    }
    
    echo "<h2>Test getEventAttendeesData function:</h2>";
    if (!empty($events)) {
        $first_event_id = $events[0]['id'];
        
        // Include the function from event_reports.php
        include_once 'event_reports.php';
        
        try {
            $attendees_data = getEventAttendeesData($first_event_id);
            echo "<p>Function executed successfully!</p>";
            echo "<p>Total attendees: " . count($attendees_data['attendees']) . "</p>";
            echo "<p>Statistics:</p>";
            echo "<ul>";
            foreach ($attendees_data['stats'] as $key => $value) {
                echo "<li>$key: $value</li>";
            }
            echo "</ul>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<p><a href='event_reports.php'>Back to Event Reports</a></p>";
?>
