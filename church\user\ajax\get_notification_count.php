<?php
/**
 * Get Notification Count AJAX Endpoint
 * 
 * Returns the unread notification count for the current user
 */

session_start();
require_once '../../config.php';
require_once '../../includes/notification_functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit;
}

$userId = $_SESSION['user_id'];

try {
    $count = getUnreadNotificationCount($pdo, $userId);
    
    echo json_encode([
        'success' => true,
        'count' => $count
    ]);
    
} catch (Exception $e) {
    error_log("Error getting notification count: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to get notification count'
    ]);
}
?>
