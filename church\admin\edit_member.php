<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Check if member ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: members.php");
    exit();
}

$member_id = intval($_GET['id']);

// Include the configuration file
require_once '../config.php';

// Include language system
require_once 'includes/language.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Get member details
$stmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$member_id]);
$member = $stmt->fetch();

if (!$member) {
    header("Location: members.php");
    exit();
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $phone_number = trim($_POST['phone_number']);
    $birth_date = trim($_POST['birth_date']);
    $gender = trim($_POST['gender'] ?? '');
    $home_address = trim($_POST['home_address'] ?? '');
    $occupation = trim($_POST['occupation'] ?? '');
    $message_text = trim($_POST['message'] ?? '');

    // Handle image upload and removal
    $image_path = $member['image_path']; // Keep existing path by default
    $upload_error = '';

    // Check if user wants to remove current image
    if (isset($_POST['remove_image']) && $_POST['remove_image'] == '1') {
        // Delete old image if it exists
        if (!empty($member['image_path']) && $member['image_path'] != 'default-profile.jpg') {
            $old_image_path = '../' . $member['image_path'];
            if (file_exists($old_image_path)) {
                unlink($old_image_path);
            }
        }
        $image_path = ''; // Set to empty to remove from database
    } elseif (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
        $file_extension = strtolower(pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION));

        if (in_array($file_extension, $allowed_types)) {
            $upload_dir = '../uploads/profiles/';

            // Create upload directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            // Generate unique filename
            $filename = uniqid() . '.' . $file_extension;
            $upload_path = $upload_dir . $filename;

            if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
                // Delete old image if it exists and is not the default
                if (!empty($member['image_path']) && $member['image_path'] != 'default-profile.jpg') {
                    $old_image_path = '../' . $member['image_path'];
                    if (file_exists($old_image_path)) {
                        unlink($old_image_path);
                    }
                }

                $image_path = 'uploads/profiles/' . $filename;
            } else {
                $upload_error = __('failed_upload_image');
            }
        } else {
            $upload_error = __('invalid_file_type_image');
        }
    }

    // Check if email already exists (except for this member)
    $stmt = $conn->prepare("SELECT id FROM members WHERE email = ? AND id != ?");
    $stmt->execute([$email, $member_id]);
    $existing_member = $stmt->fetch();

    if ($existing_member) {
        $error = __('email_already_exists_another_member');
    } elseif (!empty($upload_error)) {
        $error = $upload_error;
    } else {
        // Update member (including image path and gender)
        $stmt = $conn->prepare("
            UPDATE members SET
            full_name = ?,
            email = ?,
            phone_number = ?,
            birth_date = ?,
            gender = ?,
            home_address = ?,
            occupation = ?,
            message = ?,
            image_path = ?
            WHERE id = ?
        ");

        if ($stmt->execute([$full_name, $email, $phone_number, $birth_date, $gender, $home_address, $occupation, $message_text, $image_path, $member_id])) {
            $message = __('member_updated_successfully');

            // Refresh member data
            $stmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
            $stmt->execute([$member_id]);
            $member = $stmt->fetch();
        } else {
            $error = __('error_updating_member') . ": " . $conn->errorInfo()[2];
        }
    }
}

// Close the database connection
$conn = null;

// Set page variables
$page_title = __('edit_member');
$page_header = __('edit_member');
$page_description = __('update_member_info_description');

// Include header
include 'includes/header.php';
?>

<style>
.current-profile-image {
    margin-bottom: 15px;
}
.current-profile-image img,
.current-profile-image .img-thumbnail {
    border-radius: 50%;
    border: 3px solid #f8f9fa;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
.image-preview img {
    border-radius: 50%;
    border: 3px solid #28a745;
    box-shadow: 0 2px 10px rgba(40,167,69,0.2);
}
</style>

<?php

// Display success message if it exists
if (!empty($message)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
             <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($message) . '
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
}

// Display error message if it exists
if (!empty($error)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
             <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error) . '
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
}
?>

<div class="row mb-4">
    <div class="col-md-8">
        <h2><?php _e('edit_member'); ?></h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php"><?php _e('dashboard'); ?></a></li>
                <li class="breadcrumb-item"><a href="members.php"><?php _e('members'); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php _e('edit_member'); ?></li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="view_member.php?id=<?php echo $member_id; ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> <?php _e('back_to_member'); ?>
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . '?id=' . $member_id; ?>" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="full_name" class="form-label required-field"><?php _e('full_name'); ?></label>
                        <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo htmlspecialchars($member['full_name']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label required-field"><?php _e('email_address'); ?></label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($member['email']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone_number" class="form-label"><?php _e('phone_number'); ?></label>
                        <input type="text" class="form-control" id="phone_number" name="phone_number" value="<?php echo htmlspecialchars($member['phone_number'] ?? ''); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="birth_date" class="form-label required-field"><?php _e('birth_date'); ?></label>
                        <input type="date" class="form-control" id="birth_date" name="birth_date" value="<?php echo htmlspecialchars($member['birth_date']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="gender" class="form-label required-field"><?php _e('gender'); ?></label>
                        <select class="form-control" id="gender" name="gender" required>
                            <option value="">Please select...</option>
                            <option value="Male" <?php echo ($member['gender'] === 'Male') ? 'selected' : ''; ?>><?php _e('male'); ?></option>
                            <option value="Female" <?php echo ($member['gender'] === 'Female') ? 'selected' : ''; ?>><?php _e('female'); ?></option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="occupation" class="form-label"><?php _e('occupation'); ?></label>
                        <input type="text" class="form-control" id="occupation" name="occupation" value="<?php echo htmlspecialchars($member['occupation'] ?? ''); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="home_address" class="form-label"><?php _e('home_address'); ?></label>
                        <textarea class="form-control" id="home_address" name="home_address" rows="3"><?php echo htmlspecialchars($member['home_address'] ?? ''); ?></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="profile_image" class="form-label"><?php _e('profile_image'); ?></label>

                        <!-- Current Profile Image Display -->
                        <div class="current-image mb-2">
                            <?php
                            // Display current profile image using same logic as view_member.php
                            $currentImagePath = '';
                            $showCurrentImage = false;

                            if (!empty($member['image_path'])) {
                                $dbImagePath = $member['image_path'];
                                $testPaths = [];

                                // Try multiple possible path formats
                                if (strpos($dbImagePath, 'uploads/') === 0) {
                                    $testPaths[] = '../' . $dbImagePath;
                                } elseif (strpos($dbImagePath, '/uploads/') !== false) {
                                    $testPaths[] = '..' . $dbImagePath;
                                } else {
                                    $testPaths[] = '../uploads/' . basename($dbImagePath);
                                    $testPaths[] = '../' . $dbImagePath;
                                    $testPaths[] = $dbImagePath;
                                }

                                foreach ($testPaths as $testPath) {
                                    $fullPath = __DIR__ . '/' . $testPath;
                                    if (file_exists($fullPath)) {
                                        $currentImagePath = $testPath;
                                        $showCurrentImage = true;
                                        break;
                                    }
                                }
                            }
                            ?>

                            <div class="current-profile-image text-center">
                                <?php if ($showCurrentImage): ?>
                                <img src="<?php echo htmlspecialchars($currentImagePath); ?>?v=<?php echo time(); ?>"
                                     alt="<?php _e('current_profile_photo'); ?>"
                                     class="img-thumbnail"
                                     style="width: 120px; height: 120px; object-fit: cover;"
                                     title="<?php _e('current_profile_image'); ?>">
                                <p class="small text-muted mt-1"><?php _e('current_profile_image'); ?></p>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCurrentImage()">
                                    <i class="bi bi-trash"></i> <?php _e('remove_image'); ?>
                                </button>
                                <?php else: ?>
                                <div class="d-flex align-items-center justify-content-center bg-light img-thumbnail" style="width: 120px; height: 120px;">
                                    <i class="bi bi-person-circle text-secondary" style="font-size: 3rem;"></i>
                                </div>
                                <p class="small text-muted mt-1"><?php _e('no_current_image'); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Hidden field for remove image functionality -->
                        <input type="hidden" id="remove_image" name="remove_image" value="0">

                        <!-- Image Upload Field -->
                        <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*" onchange="previewImage(this)">
                        <div class="form-text"><?php _e('upload_image_help_text'); ?></div>

                        <!-- Image Preview -->
                        <div class="image-preview mt-2" style="display: none;">
                            <img id="preview" class="img-thumbnail" style="width: 120px; height: 120px; object-fit: cover;">
                            <p class="small text-muted mt-1"><?php _e('new_image_preview'); ?></p>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label"><?php _e('message'); ?></label>
                        <textarea class="form-control" id="message" name="message" rows="3"><?php echo htmlspecialchars($member['message'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-end">
                    <a href="view_member.php?id=<?php echo $member_id; ?>" class="btn btn-secondary me-2"><?php _e('cancel'); ?></a>
                    <button type="submit" class="btn btn-primary"><?php _e('update_member'); ?></button>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script>
function previewImage(input) {
    const preview = document.getElementById('preview');
    const previewContainer = document.querySelector('.image-preview');
    const removeImageField = document.getElementById('remove_image');

    // Reset remove image flag when new image is selected
    removeImageField.value = '0';

    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            previewContainer.style.display = 'block';
        }
        reader.readAsDataURL(input.files[0]);
    } else {
        previewContainer.style.display = 'none';
    }
}

function removeCurrentImage() {
    if (confirm('<?php _e('confirm_remove_profile_image'); ?>')) {
        const removeImageField = document.getElementById('remove_image');
        const currentImageContainer = document.querySelector('.current-profile-image');
        const fileInput = document.getElementById('profile_image');
        const previewContainer = document.querySelector('.image-preview');

        // Set flag to remove image
        removeImageField.value = '1';

        // Hide current image display
        currentImageContainer.innerHTML = '<div class="d-flex align-items-center justify-content-center bg-light img-thumbnail" style="width: 120px; height: 120px; border-radius: 50%;"><i class="bi bi-person-circle text-secondary" style="font-size: 3rem;"></i></div><p class="small text-success mt-1"><?php _e('image_will_be_removed'); ?></p>';

        // Clear file input and preview
        fileInput.value = '';
        previewContainer.style.display = 'none';
    }
}
</script>
</body>
</html>