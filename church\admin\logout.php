<?php
// Include session manager which will handle starting the session
require_once 'includes/session-manager.php';

// Check if this is a timeout logout
$timeout = isset($_GET['timeout']) && $_GET['timeout'] == 1;

// Unset all session variables
$_SESSION = array();

// Destroy the session
session_destroy();

// Redirect to login page with timeout parameter if needed
if ($timeout) {
    header("Location: login.php?timeout=1");
} else {
    header("Location: login.php");
}
exit();
?> 