<?php
/**
 * Simple PDF Generator
 * A basic PDF generation class for gift cards when DomPDF is not available
 */

class SimplePDF {
    private $content = '';
    private $title = '';
    
    public function __construct() {
        // Initialize basic PDF structure
    }
    
    public function setTitle($title) {
        $this->title = $title;
    }
    
    public function addHTML($html) {
        $this->content = $html;
    }
    
    public function output($filename = 'document.pdf', $destination = 'D') {
        // Since we can't generate real PDFs without a library,
        // we'll create a high-quality HTML that can be printed to PDF
        $this->outputPrintableHTML($filename);
    }
    
    private function outputPrintableHTML($filename) {
        // Clean the filename
        $filename = str_replace('.pdf', '.html', $filename);
        
        $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>' . htmlspecialchars($this->title) . '</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        
        .no-print {
            display: block;
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .no-print h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .no-print p {
            margin: 5px 0;
            color: #424242;
        }
        
        .print-btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
        }
        
        .print-btn:hover {
            background: #1976d2;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
            }
        }
        
        /* Gift card specific styles */
        .gift-card-container {
            max-width: 600px;
            margin: 0 auto;
            border: 3px solid #667eea;
            border-radius: 15px;
            overflow: hidden;
            background: white;
        }
        
        .gift-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .gift-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }
        
        .gift-header .org-name {
            font-size: 1.2em;
            margin-top: 10px;
        }
        
        .gift-content {
            padding: 40px;
        }
        
        .gift-title {
            font-size: 2em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .gift-message {
            font-size: 1.2em;
            line-height: 1.6;
            color: #555;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .gift-details {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
        }
        
        .detail-item {
            text-align: center;
        }
        
        .detail-label {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .detail-value {
            color: #333;
            font-size: 1.1em;
        }
    </style>
    <script>
        function printPage() {
            window.print();
        }
        
        function downloadPDF() {
            // For modern browsers, try to trigger save as PDF
            if (window.chrome) {
                window.print();
            } else {
                alert("Please use your browser\'s print function (Ctrl+P or Cmd+P) and select \'Save as PDF\' as the destination.");
                window.print();
            }
        }
        
        // Auto-focus print dialog after page loads
        window.onload = function() {
            setTimeout(function() {
                // Don\'t auto-print, let user choose
            }, 500);
        };
    </script>
</head>
<body>
    <div class="no-print">
        <h3>🎁 Gift Card Ready for Download</h3>
        <p>Your gift card is ready! Use the buttons below to print or save as PDF.</p>
        <button class="print-btn" onclick="printPage()">🖨️ Print</button>
        <button class="print-btn" onclick="downloadPDF()">📄 Save as PDF</button>
        <p><small>Tip: In the print dialog, select "Save as PDF" as your destination to create a PDF file.</small></p>
    </div>
    
    ' . $this->content . '
    
</body>
</html>';

        // Set headers for HTML download
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . strlen($html));
        
        echo $html;
        exit();
    }
}
?>
