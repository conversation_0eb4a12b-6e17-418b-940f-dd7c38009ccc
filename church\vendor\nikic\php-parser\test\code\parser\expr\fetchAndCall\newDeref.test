New expression dereferencing
-----
<?php

(new A)->b;
(new A)->b();
(new A)['b'];
(new A)['b']['c'];
-----
array(
    0: Stmt_Expression(
        expr: Expr_PropertyFetch(
            var: Expr_New(
                class: Name(
                    name: A
                )
                args: array(
                )
            )
            name: Identifier(
                name: b
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Expr_New(
                class: Name(
                    name: A
                )
                args: array(
                )
            )
            name: Identifier(
                name: b
            )
            args: array(
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_ArrayDimFetch(
            var: Expr_New(
                class: Name(
                    name: A
                )
                args: array(
                )
            )
            dim: Scalar_String(
                value: b
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_ArrayDimFetch(
            var: Expr_ArrayDimFetch(
                var: Expr_New(
                    class: Name(
                        name: A
                    )
                    args: array(
                    )
                )
                dim: Scalar_String(
                    value: b
                )
            )
            dim: Scalar_String(
                value: c
            )
        )
    )
)
