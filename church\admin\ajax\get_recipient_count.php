<?php
/**
 * AJAX endpoint to get recipient count for a schedule
 * This is used by the email sending progress modal
 */

session_start();
require_once '../../config.php';
require_once '../../includes/auth_check.php';

// Check if schedule ID is provided
if (!isset($_GET['schedule_id']) || !is_numeric($_GET['schedule_id'])) {
    echo json_encode(['success' => false, 'error' => 'Invalid schedule ID']);
    exit;
}

$schedule_id = (int)$_GET['schedule_id'];

try {
    // Get total pending recipients for this schedule
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as pending_count 
        FROM email_schedule_recipients 
        WHERE schedule_id = ? AND status = 'pending'
    ");
    $stmt->execute([$schedule_id]);
    $pendingCount = $stmt->fetch(PDO::FETCH_ASSOC)['pending_count'];
    
    // Check if we need to expand group recipients
    $stmt = $pdo->prepare("
        SELECT 
            recipient_type, recipient_id 
        FROM 
            email_schedule_recipients 
        WHERE 
            schedule_id = ? AND status = 'pending' AND recipient_type = 'group'
    ");
    $stmt->execute([$schedule_id]);
    $groupRecipients = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Calculate total recipients including expanded groups
    $totalRecipients = $pendingCount;
    
    // Subtract group recipients and add their actual members count
    $totalRecipients -= count($groupRecipients);
    
    foreach ($groupRecipients as $group) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as member_count 
            FROM contact_group_members cgm
            JOIN contacts c ON cgm.contact_id = c.id
            WHERE cgm.group_id = ? AND c.email IS NOT NULL
        ");
        $stmt->execute([$group['recipient_id']]);
        $memberCount = $stmt->fetch(PDO::FETCH_ASSOC)['member_count'];
        $totalRecipients += $memberCount;
    }
    
    echo json_encode([
        'success' => true, 
        'pending' => $pendingCount,
        'total' => $totalRecipients
    ]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
} 