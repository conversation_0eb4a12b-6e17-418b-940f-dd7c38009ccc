Special function variables
-----
<?php

function a() {
    global $a, ${'b'}, $$c;
    static $c, $d = 'e';
}
-----
array(
    0: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: a
        )
        params: array(
        )
        returnType: null
        stmts: array(
            0: Stmt_Global(
                vars: array(
                    0: Expr_Variable(
                        name: a
                    )
                    1: Expr_Variable(
                        name: Scalar_String(
                            value: b
                        )
                    )
                    2: Expr_Variable(
                        name: Expr_Variable(
                            name: c
                        )
                    )
                )
            )
            1: Stmt_Static(
                vars: array(
                    0: StaticVar(
                        var: Expr_Variable(
                            name: c
                        )
                        default: null
                    )
                    1: StaticVar(
                        var: Expr_Variable(
                            name: d
                        )
                        default: Scalar_String(
                            value: e
                        )
                    )
                )
            )
        )
    )
)
