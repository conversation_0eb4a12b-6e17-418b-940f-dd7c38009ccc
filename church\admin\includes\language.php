<?php
/**
 * Multi-language Support System
 * Handles translation loading and text rendering
 */

class LanguageManager {
    private $currentLanguage = 'en';
    private $translations = [];
    private $fallbackLanguage = 'en';
    private $languageDir;
    
    public function __construct($languageDir = null) {
        $this->languageDir = $languageDir ?: __DIR__ . '/../languages/';
        $this->detectLanguage();
        $this->loadTranslations();
    }
    
    /**
     * Detect user's preferred language
     */
    private function detectLanguage() {
        // 1. Check user preference from database
        if (isset($_SESSION['admin_id'])) {
            $userLang = $this->getUserLanguagePreference($_SESSION['admin_id']);
            if ($userLang) {
                $this->currentLanguage = $userLang;
                return;
            }
        }
        
        // 2. Check session
        if (isset($_SESSION['language'])) {
            $this->currentLanguage = $_SESSION['language'];
            return;
        }
        
        // 3. Check cookie
        if (isset($_COOKIE['language'])) {
            $this->currentLanguage = $_COOKIE['language'];
            return;
        }
        
        // 4. Check browser language
        if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $browserLang = $this->parseBrowserLanguage($_SERVER['HTTP_ACCEPT_LANGUAGE']);
            if ($browserLang) {
                $this->currentLanguage = $browserLang;
                return;
            }
        }
        
        // 5. Check system setting
        global $pdo;
        if (isset($pdo)) {
            try {
                $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'language'");
                $stmt->execute();
                $result = $stmt->fetch();
                if ($result) {
                    $this->currentLanguage = $result['setting_value'];
                    return;
                }
            } catch (Exception $e) {
                // Ignore database errors
            }
        }
        
        // Default to English
        $this->currentLanguage = 'en';
    }
    
    /**
     * Get user's language preference from database
     */
    private function getUserLanguagePreference($userId) {
        global $pdo;
        if (!isset($pdo)) return null;
        
        try {
            $stmt = $pdo->prepare("SELECT preference_value FROM user_preferences WHERE admin_id = ? AND preference_key = 'language_preference'");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();
            return $result ? $result['preference_value'] : null;
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * Parse browser Accept-Language header
     */
    private function parseBrowserLanguage($acceptLanguage) {
        $languages = explode(',', $acceptLanguage);
        foreach ($languages as $lang) {
            $lang = trim(explode(';', $lang)[0]);
            $lang = strtolower(substr($lang, 0, 2));
            
            if ($this->isLanguageSupported($lang)) {
                return $lang;
            }
        }
        return null;
    }
    
    /**
     * Check if language is supported
     */
    private function isLanguageSupported($lang) {
        $supportedLanguages = ['en', 'es', 'fr', 'de', 'it', 'pt', 'nl', 'ru', 'zh', 'ja', 'ko', 'ar'];
        return in_array($lang, $supportedLanguages);
    }
    
    /**
     * Load translations for current language
     */
    private function loadTranslations() {
        // Load fallback language first
        $this->loadLanguageFile($this->fallbackLanguage);
        
        // Load current language (will override fallback)
        if ($this->currentLanguage !== $this->fallbackLanguage) {
            $this->loadLanguageFile($this->currentLanguage);
        }
    }
    
    /**
     * Load language file
     */
    private function loadLanguageFile($language) {
        $file = $this->languageDir . $language . '.php';
        if (file_exists($file)) {
            $translations = include $file;
            if (is_array($translations)) {
                $this->translations = array_merge($this->translations, $translations);
            }
        }
    }
    
    /**
     * Get translated text
     */
    public function get($key, $params = []) {
        $text = $this->translations[$key] ?? $key;
        
        // Replace parameters
        if (!empty($params)) {
            foreach ($params as $param => $value) {
                $text = str_replace('{' . $param . '}', $value, $text);
            }
        }
        
        return $text;
    }
    
    /**
     * Echo translated text
     */
    public function echo($key, $params = []) {
        echo $this->get($key, $params);
    }
    
    /**
     * Get current language
     */
    public function getCurrentLanguage() {
        return $this->currentLanguage;
    }
    
    /**
     * Set language
     */
    public function setLanguage($language) {
        if (!$this->isLanguageSupported($language)) {
            return false;
        }
        
        $this->currentLanguage = $language;
        $this->translations = [];
        $this->loadTranslations();
        
        // Save to session
        $_SESSION['language'] = $language;
        
        // Save to cookie (30 days)
        setcookie('language', $language, time() + (30 * 24 * 60 * 60), '/');
        
        // Save to user preferences if logged in
        if (isset($_SESSION['admin_id'])) {
            $this->saveUserLanguagePreference($_SESSION['admin_id'], $language);
        }
        
        return true;
    }
    
    /**
     * Save user language preference
     */
    private function saveUserLanguagePreference($userId, $language) {
        global $pdo;
        if (!isset($pdo)) return;
        
        try {
            // Check if preference exists
            $stmt = $pdo->prepare("SELECT id FROM user_preferences WHERE admin_id = ? AND preference_key = 'language_preference'");
            $stmt->execute([$userId]);
            
            if ($stmt->fetch()) {
                // Update existing
                $stmt = $pdo->prepare("UPDATE user_preferences SET preference_value = ? WHERE admin_id = ? AND preference_key = 'language_preference'");
                $stmt->execute([$language, $userId]);
            } else {
                // Insert new
                $stmt = $pdo->prepare("INSERT INTO user_preferences (admin_id, preference_key, preference_value, preference_type) VALUES (?, 'language_preference', ?, 'string')");
                $stmt->execute([$userId, $language]);
            }
        } catch (Exception $e) {
            // Ignore errors
        }
    }
    
    /**
     * Get available languages
     */
    public function getAvailableLanguages() {
        return [
            'en' => 'English',
            'es' => 'Español',
            'fr' => 'Français',
            'de' => 'Deutsch',
            'it' => 'Italiano',
            'pt' => 'Português',
            'nl' => 'Nederlands',
            'ru' => 'Русский',
            'zh' => '中文',
            'ja' => '日本語',
            'ko' => '한국어',
            'ar' => 'العربية'
        ];
    }
    
    /**
     * Get language direction (LTR/RTL)
     */
    public function getLanguageDirection($language = null) {
        $language = $language ?: $this->currentLanguage;
        $rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        return in_array($language, $rtlLanguages) ? 'rtl' : 'ltr';
    }
    
    /**
     * Format number according to language
     */
    public function formatNumber($number, $decimals = 0) {
        $locales = [
            'en' => 'en_US',
            'es' => 'es_ES',
            'fr' => 'fr_FR',
            'de' => 'de_DE',
            'it' => 'it_IT',
            'pt' => 'pt_BR',
            'nl' => 'nl_NL',
            'ru' => 'ru_RU',
            'zh' => 'zh_CN',
            'ja' => 'ja_JP',
            'ko' => 'ko_KR',
            'ar' => 'ar_SA'
        ];
        
        $locale = $locales[$this->currentLanguage] ?? 'en_US';
        
        if (class_exists('NumberFormatter')) {
            $formatter = new NumberFormatter($locale, NumberFormatter::DECIMAL);
            $formatter->setAttribute(NumberFormatter::FRACTION_DIGITS, $decimals);
            return $formatter->format($number);
        }
        
        // Fallback
        return number_format($number, $decimals);
    }
    
    /**
     * Format date according to language
     */
    public function formatDate($date, $format = 'medium') {
        $timestamp = is_string($date) ? strtotime($date) : $date;
        
        $formats = [
            'en' => [
                'short' => 'M j, Y',
                'medium' => 'M j, Y g:i A',
                'long' => 'F j, Y g:i A T'
            ],
            'es' => [
                'short' => 'j/n/Y',
                'medium' => 'j/n/Y H:i',
                'long' => 'j \d\e F \d\e Y H:i T'
            ],
            'fr' => [
                'short' => 'j/n/Y',
                'medium' => 'j/n/Y H:i',
                'long' => 'j F Y H:i T'
            ]
        ];
        
        $langFormats = $formats[$this->currentLanguage] ?? $formats['en'];
        $dateFormat = $langFormats[$format] ?? $langFormats['medium'];
        
        return date($dateFormat, $timestamp);
    }
}

// Initialize global language manager
if (!isset($GLOBALS['lang'])) {
    $GLOBALS['lang'] = new LanguageManager();
}
$lang = $GLOBALS['lang'];

// Helper functions
function __($key, $params = []) {
    global $lang;
    return $lang->get($key, $params);
}

function _e($key, $params = []) {
    global $lang;
    $lang->echo($key, $params);
}

function get_current_language() {
    global $lang;
    return $lang->getCurrentLanguage();
}

function set_language($language) {
    global $lang;
    return $lang->setLanguage($language);
}

function get_language_direction() {
    global $lang;
    return $lang->getLanguageDirection();
}
?>
