Constant fetches
-----
<?php

A;
A::B;
A::class;
$a::B;
$a::class;
-----
array(
    0: Stmt_Expression(
        expr: Expr_ConstFetch(
            name: Name(
                name: A
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_ClassConstFetch(
            class: Name(
                name: A
            )
            name: Identifier(
                name: B
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_ClassConstFetch(
            class: Name(
                name: A
            )
            name: Identifier(
                name: class
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_ClassConstFetch(
            class: Expr_Variable(
                name: a
            )
            name: Identifier(
                name: B
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_ClassConstFetch(
            class: Expr_Variable(
                name: a
            )
            name: Identifier(
                name: class
            )
        )
    )
)
