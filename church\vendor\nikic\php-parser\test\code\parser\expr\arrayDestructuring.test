Array destructuring
-----
<?php

[$a, $b] = [$c, $d];
[, $a, , , $b, ,] = $foo;
[, [[$a, , $x]], $b] = $bar;
['a' => $b, 'b' => $a] = $baz;
-----
array(
    0: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_List(
                items: array(
                    0: ArrayItem(
                        key: null
                        value: Expr_Variable(
                            name: a
                        )
                        byRef: false
                        unpack: false
                    )
                    1: ArrayItem(
                        key: null
                        value: Expr_Variable(
                            name: b
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            expr: Expr_Array(
                items: array(
                    0: ArrayItem(
                        key: null
                        value: Expr_Variable(
                            name: c
                        )
                        byRef: false
                        unpack: false
                    )
                    1: ArrayItem(
                        key: null
                        value: Expr_Variable(
                            name: d
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_List(
                items: array(
                    0: null
                    1: ArrayItem(
                        key: null
                        value: Expr_Variable(
                            name: a
                        )
                        byRef: false
                        unpack: false
                    )
                    2: null
                    3: null
                    4: ArrayItem(
                        key: null
                        value: Expr_Variable(
                            name: b
                        )
                        byRef: false
                        unpack: false
                    )
                    5: null
                )
            )
            expr: Expr_Variable(
                name: foo
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_List(
                items: array(
                    0: null
                    1: ArrayItem(
                        key: null
                        value: Expr_List(
                            items: array(
                                0: ArrayItem(
                                    key: null
                                    value: Expr_List(
                                        items: array(
                                            0: ArrayItem(
                                                key: null
                                                value: Expr_Variable(
                                                    name: a
                                                )
                                                byRef: false
                                                unpack: false
                                            )
                                            1: null
                                            2: ArrayItem(
                                                key: null
                                                value: Expr_Variable(
                                                    name: x
                                                )
                                                byRef: false
                                                unpack: false
                                            )
                                        )
                                    )
                                    byRef: false
                                    unpack: false
                                )
                            )
                        )
                        byRef: false
                        unpack: false
                    )
                    2: ArrayItem(
                        key: null
                        value: Expr_Variable(
                            name: b
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            expr: Expr_Variable(
                name: bar
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_Assign(
            var: Expr_List(
                items: array(
                    0: ArrayItem(
                        key: Scalar_String(
                            value: a
                        )
                        value: Expr_Variable(
                            name: b
                        )
                        byRef: false
                        unpack: false
                    )
                    1: ArrayItem(
                        key: Scalar_String(
                            value: b
                        )
                        value: Expr_Variable(
                            name: a
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            expr: Expr_Variable(
                name: baz
            )
        )
    )
)
