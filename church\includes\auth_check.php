<?php
/**
 * Authentication Check
 * 
 * This file checks if a user is authenticated and redirects to the login page if not.
 * It's used by admin pages to ensure only authenticated users can access them.
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include configuration
require_once __DIR__ . '/../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    // Determine the admin URL for redirection
    $admin_url = defined('ADMIN_URL') ? ADMIN_URL : '../admin';
    
    // Store the requested URL for redirection after login
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    
    // Redirect to login page
    header("Location: {$admin_url}/login.php");
    exit();
}

// Check if session has expired
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 3600)) {
    // Session expired (1 hour of inactivity)
    session_unset();
    session_destroy();
    
    // Determine the admin URL for redirection
    $admin_url = defined('ADMIN_URL') ? ADMIN_URL : '../admin';
    
    // Redirect to login page with session expired message
    header("Location: {$admin_url}/login.php?session_expired=1");
    exit();
}

// Update last activity time
$_SESSION['last_activity'] = time();

// Check for required permissions if specified
if (isset($required_permission) && !empty($required_permission)) {
    // Include the permissions check function if not already included
    if (!function_exists('has_permission')) {
        require_once __DIR__ . '/permissions.php';
    }
    
    // Check if user has the required permission
    if (!has_permission($_SESSION['admin_id'], $required_permission)) {
        // Redirect to access denied page
        header("Location: " . ADMIN_URL . "/access_denied.php");
        exit();
    }
}
