<?php
/**
 * Get Request Comments AJAX Endpoint
 *
 * Returns comments for a specific request
 */

// Set headers first before any output
header('Content-Type: application/json');

// Prevent any output before JSON
ob_start();

session_start();

// Try different paths to find config.php
$config_paths = [
    '../../config.php',
    '../../../config.php',
    dirname(__DIR__, 2) . '/config.php',
    __DIR__ . '/../../config.php'
];

$config_loaded = false;
foreach ($config_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $config_loaded = true;
        break;
    }
}

if (!$config_loaded) {
    ob_end_clean();
    echo json_encode(['success' => false, 'error' => 'Configuration file not found']);
    exit;
}

// Clear any output that might have been generated
ob_end_clean();

// Enable error logging for debugging but suppress display
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('display_errors', 0);

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    error_log("Comments access denied: User not authenticated");
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit;
}

$requestId = $_GET['request_id'] ?? null;

if (!$requestId || !is_numeric($requestId)) {
    error_log("Comments error: Invalid request ID provided: " . ($requestId ?? 'null'));
    echo json_encode(['success' => false, 'error' => 'Valid request ID required']);
    exit;
}

try {
    // First check if user can view this request
    $stmt = $pdo->prepare("
        SELECT pr.privacy_level, pr.member_id, pr.title, pr.status,
               COALESCE(pr.allow_comments, 1) as allow_comments
        FROM prayer_requests pr
        WHERE pr.id = ?
    ");
    $stmt->execute([$requestId]);
    $request = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$request) {
        error_log("Comments error: Request not found for ID: " . $requestId);
        echo json_encode(['success' => false, 'error' => 'Request not found']);
        exit;
    }

    // Log request details for debugging
    error_log("Comments request - ID: $requestId, Privacy: {$request['privacy_level']}, Owner: {$request['member_id']}, Current User: {$_SESSION['user_id']}");

    // Check if user can view this request based on privacy level
    $canView = false;
    $accessReason = '';

    if ($request['member_id'] == $_SESSION['user_id']) {
        $canView = true; // Owner can always view
        $accessReason = 'owner';
    } elseif ($request['privacy_level'] === 'public') {
        $canView = true; // Public requests visible to all
        $accessReason = 'public';
    } elseif ($request['privacy_level'] === 'members') {
        $canView = true; // Members can view member requests
        $accessReason = 'members';
    } elseif ($request['privacy_level'] === 'private') {
        // Private requests only visible to owner (already checked above)
        $canView = false;
        $accessReason = 'private_denied';
    }

    if (!$canView) {
        error_log("Comments access denied - Request ID: $requestId, Privacy: {$request['privacy_level']}, User: {$_SESSION['user_id']}, Reason: $accessReason");
        echo json_encode(['success' => false, 'error' => 'You do not have permission to view comments for this request']);
        exit;
    }

    error_log("Comments access granted - Request ID: $requestId, Reason: $accessReason");

    // Check if comments are allowed for this request
    if (!$request['allow_comments']) {
        error_log("Comments disabled for request ID: $requestId");
        echo json_encode(['success' => false, 'error' => 'Comments are disabled for this request']);
        exit;
    }

    // Get comments for the request
    $stmt = $pdo->prepare("
        SELECT 
            prr.*,
            CASE 
                WHEN prr.is_admin_response = 1 OR prr.admin_id IS NOT NULL THEN 
                    COALESCE(a.full_name, a.username, 'Administrator')
                ELSE 
                    COALESCE(m.full_name, CONCAT(m.first_name, ' ', m.last_name), 'Member')
            END as commenter_name,
            CASE 
                WHEN prr.is_admin_response = 1 OR prr.admin_id IS NOT NULL THEN 'admin'
                ELSE 'member'
            END as commenter_type
        FROM prayer_responses prr
        LEFT JOIN members m ON prr.member_id = m.id
        LEFT JOIN admins a ON prr.admin_id = a.id
        WHERE prr.prayer_request_id = ?
        ORDER BY prr.created_at ASC
    ");
    $stmt->execute([$requestId]);
    $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format comments for display
    foreach ($comments as &$comment) {
        $comment['formatted_time'] = formatCommentTime($comment['created_at']);
        $comment['can_delete'] = ($comment['member_id'] == $_SESSION['user_id'] || 
                                 $comment['admin_id'] == $_SESSION['admin_id'] ?? null);
    }
    
    echo json_encode([
        'success' => true,
        'comments' => $comments
    ]);
    
} catch (Exception $e) {
    error_log("Error getting request comments: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load comments'
    ]);
}

function formatCommentTime($datetime) {
    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;
    
    if ($diff < 60) {
        return 'Just now';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } else {
        return date('M j, Y', $time);
    }
}
?>
