Exit
-----
<?php
exit;
exit();
exit('Die!');
die;
die();
die('Exit!');

exit(status: 42);
exit(...$args);
exit($a, $b);
\exit($a);
exit(...);
DIE($a, $b);
-----
array(
    0: Stmt_Expression(
        expr: Expr_Exit(
            expr: null
        )
    )
    1: Stmt_Expression(
        expr: Expr_Exit(
            expr: null
        )
    )
    2: Stmt_Expression(
        expr: Expr_Exit(
            expr: Scalar_String(
                value: Die!
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_Exit(
            expr: null
        )
    )
    4: Stmt_Expression(
        expr: Expr_Exit(
            expr: null
        )
    )
    5: Stmt_Expression(
        expr: Expr_Exit(
            expr: Scalar_String(
                value: Exit!
            )
        )
    )
    6: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: exit
            )
            args: array(
                0: Arg(
                    name: Identifier(
                        name: status
                    )
                    value: Scalar_Int(
                        value: 42
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    7: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: exit
            )
            args: array(
                0: Arg(
                    name: null
                    value: Expr_Variable(
                        name: args
                    )
                    byRef: false
                    unpack: true
                )
            )
        )
    )
    8: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: exit
            )
            args: array(
                0: Arg(
                    name: null
                    value: Expr_Variable(
                        name: a
                    )
                    byRef: false
                    unpack: false
                )
                1: Arg(
                    name: null
                    value: Expr_Variable(
                        name: b
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    9: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name_FullyQualified(
                name: exit
            )
            args: array(
                0: Arg(
                    name: null
                    value: Expr_Variable(
                        name: a
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    10: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: exit
            )
            args: array(
                0: VariadicPlaceholder(
                )
            )
        )
    )
    11: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: DIE
            )
            args: array(
                0: Arg(
                    name: null
                    value: Expr_Variable(
                        name: a
                    )
                    byRef: false
                    unpack: false
                )
                1: Arg(
                    name: null
                    value: Expr_Variable(
                        name: b
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
)
