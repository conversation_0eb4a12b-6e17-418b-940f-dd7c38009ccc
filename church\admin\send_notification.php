<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';
require_once '../includes/notification_functions.php';

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $messageText = trim($_POST['message'] ?? '');
    $notificationType = $_POST['notification_type'] ?? 'announcement';
    $priority = $_POST['priority'] ?? 'normal';
    $actionUrl = trim($_POST['action_url'] ?? '');
    $recipients = $_POST['recipients'] ?? [];
    $sendToAll = isset($_POST['send_to_all']);
    $expiresAt = $_POST['expires_at'] ?? '';
    
    if (empty($title) || empty($messageText)) {
        $error = 'Title and message are required.';
    } elseif (!$sendToAll && empty($recipients)) {
        $error = 'Please select recipients or choose to send to all members.';
    } else {
        try {
            $adminId = $_SESSION['admin_id'];
            $recipientIds = [];
            
            if ($sendToAll) {
                // Get all active members
                $stmt = $pdo->query("SELECT id FROM members WHERE status = 'active'");
                $recipientIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            } else {
                $recipientIds = array_map('intval', $recipients);
            }
            
            if (!empty($recipientIds)) {
                $result = sendBulkNotification(
                    $pdo, 
                    $recipientIds, 
                    $title, 
                    $messageText, 
                    $notificationType, 
                    $adminId, 
                    'admin', 
                    $actionUrl ?: null, 
                    $priority
                );
                
                if ($result['success_count'] > 0) {
                    $message = "Notification sent successfully to {$result['success_count']} member(s).";
                    if (!empty($result['errors'])) {
                        $message .= " " . count($result['errors']) . " failed.";
                    }
                } else {
                    $error = 'Failed to send notifications.';
                }
            } else {
                $error = 'No valid recipients found.';
            }
        } catch (Exception $e) {
            $error = 'Error sending notification: ' . $e->getMessage();
        }
    }
}

// Get all members for recipient selection
$stmt = $pdo->query("
    SELECT id, first_name, last_name, full_name, email 
    FROM members 
    WHERE status = 'active' 
    ORDER BY first_name, last_name
");
$members = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get site settings
$sitename = get_site_setting('site_name', get_organization_name() . ' - Send Notification');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send Notification - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-megaphone"></i> Send Notification</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <form method="post">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="title" name="title" required maxlength="255" value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="priority" class="form-label">Priority</label>
                                        <select class="form-select" id="priority" name="priority">
                                            <option value="low" <?php echo ($_POST['priority'] ?? '') === 'low' ? 'selected' : ''; ?>>Low</option>
                                            <option value="normal" <?php echo ($_POST['priority'] ?? 'normal') === 'normal' ? 'selected' : ''; ?>>Normal</option>
                                            <option value="high" <?php echo ($_POST['priority'] ?? '') === 'high' ? 'selected' : ''; ?>>High</option>
                                            <option value="urgent" <?php echo ($_POST['priority'] ?? '') === 'urgent' ? 'selected' : ''; ?>>Urgent</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="notification_type" class="form-label">Type</label>
                                        <select class="form-select" id="notification_type" name="notification_type">
                                            <option value="announcement" <?php echo ($_POST['notification_type'] ?? 'announcement') === 'announcement' ? 'selected' : ''; ?>>Announcement</option>
                                            <option value="message" <?php echo ($_POST['notification_type'] ?? '') === 'message' ? 'selected' : ''; ?>>Message</option>
                                            <option value="event" <?php echo ($_POST['notification_type'] ?? '') === 'event' ? 'selected' : ''; ?>>Event</option>
                                            <option value="system" <?php echo ($_POST['notification_type'] ?? '') === 'system' ? 'selected' : ''; ?>>System</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="expires_at" class="form-label">Expires At (Optional)</label>
                                        <input type="datetime-local" class="form-control" id="expires_at" name="expires_at" value="<?php echo htmlspecialchars($_POST['expires_at'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="message" name="message" rows="4" required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="action_url" class="form-label">Action URL (Optional)</label>
                                <input type="url" class="form-control" id="action_url" name="action_url" placeholder="https://example.com/page" value="<?php echo htmlspecialchars($_POST['action_url'] ?? ''); ?>">
                                <div class="form-text">Optional link for users to take action related to this notification.</div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="send_to_all" name="send_to_all" <?php echo isset($_POST['send_to_all']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="send_to_all">
                                        Send to all active members
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3" id="recipients_section" style="<?php echo isset($_POST['send_to_all']) ? 'display: none;' : ''; ?>">
                                <label for="recipients" class="form-label">Recipients</label>
                                <select class="form-select" id="recipients" name="recipients[]" multiple>
                                    <?php foreach ($members as $member): ?>
                                        <option value="<?php echo $member['id']; ?>" <?php echo in_array($member['id'], $_POST['recipients'] ?? []) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($member['full_name'] ?: ($member['first_name'] . ' ' . $member['last_name'])); ?> 
                                            (<?php echo htmlspecialchars($member['email']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Select specific members to receive this notification.</div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="dashboard.php" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-send"></i> Send Notification
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Select2 for recipients
            $('#recipients').select2({
                theme: 'bootstrap-5',
                placeholder: 'Select recipients...',
                allowClear: true
            });
            
            // Toggle recipients section based on "send to all" checkbox
            $('#send_to_all').change(function() {
                if (this.checked) {
                    $('#recipients_section').hide();
                    $('#recipients').prop('required', false);
                } else {
                    $('#recipients_section').show();
                    $('#recipients').prop('required', true);
                }
            });
        });
    </script>
</body>
</html>
