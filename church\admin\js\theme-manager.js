/**
 * Theme Manager - Dark Mode and Theme System
 * Handles theme switching, user preferences, and system detection
 */

class ThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.storageKey = 'church-admin-theme';
        this.systemPreference = 'light';
        
        this.init();
    }
    
    init() {
        // Detect system preference
        this.detectSystemPreference();
        
        // Load saved theme or use system preference
        this.loadTheme();
        
        // Create theme toggle button
        this.createToggleButton();
        
        // Listen for system preference changes
        this.listenForSystemChanges();
        
        // Apply theme immediately to prevent flash
        this.applyTheme(this.currentTheme);
        
        console.log('Theme Manager initialized. Current theme:', this.currentTheme);
    }
    
    detectSystemPreference() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            this.systemPreference = 'dark';
        } else {
            this.systemPreference = 'light';
        }
    }
    
    loadTheme() {
        // Check for saved preference
        const savedTheme = localStorage.getItem(this.storageKey);
        
        if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
            this.currentTheme = savedTheme;
        } else {
            // Default to auto (follow system)
            this.currentTheme = 'auto';
        }
        
        // If auto, use system preference
        if (this.currentTheme === 'auto') {
            this.currentTheme = this.systemPreference;
        }
    }
    
    saveTheme(theme) {
        localStorage.setItem(this.storageKey, theme);
        
        // Also save to user preferences via AJAX
        this.saveToUserPreferences(theme);
    }
    
    saveToUserPreferences(theme) {
        // Save theme preference to database
        fetch(ADMIN_URL + '/ajax/save_user_preference.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                key: 'theme_preference',
                value: theme,
                type: 'string'
            })
        }).catch(error => {
            console.warn('Failed to save theme preference to database:', error);
        });
    }
    
    applyTheme(theme) {
        // Remove existing theme classes
        document.documentElement.removeAttribute('data-theme');
        document.body.classList.remove('theme-light', 'theme-dark');
        
        // Apply new theme
        if (theme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.body.classList.add('theme-dark');
        } else {
            document.documentElement.setAttribute('data-theme', 'light');
            document.body.classList.add('theme-light');
        }
        
        // Update toggle button
        this.updateToggleButton(theme);
        
        // Dispatch theme change event
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: theme }
        }));
    }
    
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }
    
    setTheme(theme) {
        if (!['light', 'dark', 'auto'].includes(theme)) {
            console.warn('Invalid theme:', theme);
            return;
        }
        
        let actualTheme = theme;
        
        // If auto, use system preference
        if (theme === 'auto') {
            actualTheme = this.systemPreference;
        }
        
        this.currentTheme = actualTheme;
        this.applyTheme(actualTheme);
        this.saveTheme(theme); // Save the preference (including 'auto')
        
        console.log('Theme changed to:', theme, '(actual:', actualTheme, ')');
    }
    
    createToggleButton() {
        // Check if button already exists
        if (document.getElementById('themeToggle')) {
            return;
        }
        
        const button = document.createElement('button');
        button.id = 'themeToggle';
        button.className = 'theme-toggle';
        button.setAttribute('aria-label', 'Toggle dark mode');
        button.setAttribute('title', 'Toggle dark mode');
        button.innerHTML = '<i class="bi bi-moon-fill"></i>';
        
        // Add click handler
        button.addEventListener('click', () => {
            this.toggleTheme();
        });
        
        // Add keyboard support
        button.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggleTheme();
            }
        });
        
        // Add to page
        document.body.appendChild(button);
    }
    
    updateToggleButton(theme) {
        const button = document.getElementById('themeToggle');
        if (!button) return;
        
        const icon = button.querySelector('i');
        if (!icon) return;
        
        if (theme === 'dark') {
            icon.className = 'bi bi-sun-fill';
            button.setAttribute('title', 'Switch to light mode');
            button.setAttribute('aria-label', 'Switch to light mode');
        } else {
            icon.className = 'bi bi-moon-fill';
            button.setAttribute('title', 'Switch to dark mode');
            button.setAttribute('aria-label', 'Switch to dark mode');
        }
    }
    
    listenForSystemChanges() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            mediaQuery.addEventListener('change', (e) => {
                this.systemPreference = e.matches ? 'dark' : 'light';
                
                // If user has auto preference, update theme
                const savedTheme = localStorage.getItem(this.storageKey);
                if (savedTheme === 'auto' || !savedTheme) {
                    this.currentTheme = this.systemPreference;
                    this.applyTheme(this.currentTheme);
                }
            });
        }
    }
    
    // Public API methods
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    getSystemPreference() {
        return this.systemPreference;
    }
    
    getSavedPreference() {
        return localStorage.getItem(this.storageKey) || 'auto';
    }
    
    // Theme preset methods
    setLightTheme() {
        this.setTheme('light');
    }
    
    setDarkTheme() {
        this.setTheme('dark');
    }
    
    setAutoTheme() {
        this.setTheme('auto');
    }
    
    // Accessibility helpers
    announceThemeChange(theme) {
        // Create announcement for screen readers
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = `Theme changed to ${theme} mode`;
        
        document.body.appendChild(announcement);
        
        // Remove after announcement
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }
}

// Initialize theme manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize theme manager
    window.themeManager = new ThemeManager();
    
    // Add theme options to user settings if available
    addThemeOptionsToSettings();
});

// Add theme options to settings page
function addThemeOptionsToSettings() {
    const settingsForm = document.querySelector('#userSettingsForm, #appearanceForm');
    if (!settingsForm) return;
    
    // Look for existing theme section or create one
    let themeSection = document.querySelector('.theme-preferences');
    
    if (!themeSection) {
        themeSection = document.createElement('div');
        themeSection.className = 'theme-preferences mb-4';
        themeSection.innerHTML = `
            <h5>Theme Preferences</h5>
            <div class="form-group">
                <label for="themeSelect" class="form-label">Theme Mode</label>
                <select class="form-select" id="themeSelect" name="theme_preference">
                    <option value="auto">Auto (Follow System)</option>
                    <option value="light">Light Mode</option>
                    <option value="dark">Dark Mode</option>
                </select>
                <div class="form-text">Choose your preferred theme or let the system decide automatically.</div>
            </div>
        `;
        
        // Insert before submit button
        const submitButton = settingsForm.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.parentNode.insertBefore(themeSection, submitButton);
        } else {
            settingsForm.appendChild(themeSection);
        }
        
        // Set current value
        const themeSelect = themeSection.querySelector('#themeSelect');
        const currentPreference = window.themeManager.getSavedPreference();
        themeSelect.value = currentPreference;
        
        // Add change handler
        themeSelect.addEventListener('change', function() {
            window.themeManager.setTheme(this.value);
        });
    }
}

// Export for use in other scripts
window.ThemeManager = ThemeManager;
