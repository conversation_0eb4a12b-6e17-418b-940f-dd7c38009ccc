Removing from list nodes
-----
<?php $foo; $bar; $baz;
-----
array_splice($stmts, 1, 1, []);
-----
<?php $foo; $baz;
-----
<?php
function foo(
    $a,
    $b,
    $c
) {}
-----
array_pop($stmts[0]->params);
-----
<?php
function foo(
    $a,
    $b
) {}
-----
<?php
function foo(
    $a,
    $b,
    $c
) {}
-----
array_pop($stmts[0]->params);
$stmts[0]->params[] = new Node\Param(new Expr\Variable('x'));
$stmts[0]->params[] = new Node\Param(new Expr\Variable('y'));
-----
<?php
function foo(
    $a,
    $b,
    $x,
    $y
) {}
-----
<?php
function test(): A
|B
|C {}
-----
array_pop($stmts[0]->returnType->types);
-----
<?php
function test(): A
|B {}
-----
<?php $a; $b; $c;
-----
array_splice($stmts, 0, 1, []);
-----
<?php $b; $c;
-----
<?php $a; $b; $c;
-----
array_splice($stmts, 0, 2, []);
-----
<?php $c;
-----
<?php
{ $x; }
$y;
-----
array_splice($stmts, 0, 1, []);
-----
<?php
$y;
-----
<?php
$x;
{ $y; }
-----
array_splice($stmts, 0, 1, []);
-----
<?php
{ $y; }
-----
<?php
$x;
{ $y; }
-----
array_pop($stmts);
-----
<?php
$x;
-----
<?php
{ $x; }
$y;
-----
array_pop($stmts);
-----
<?php
{ $x; }
-----
<?php
// Foo
$x;
$y;
-----
array_splice($stmts, 0, 1, []);
-----
<?php
$y;
-----
<?php

try {

} catch (\InvalidArgumentException $e) {

} catch (\Exception $e) {

} catch (\LogicException $e) {

} catch (\Throwable $e) {

}
-----
$catch = $stmts[0]->catches[2];
unset($stmts[0]->catches[2]);
$stmts[0]->catches = array_values($stmts[0]->catches);
array_splice($stmts[0]->catches, 1, 0, [$catch]);
-----
<?php

try {

} catch (\InvalidArgumentException $e) {

} catch (\LogicException $e) {

} catch (\Exception $e) {

} catch (\Throwable $e) {

}
-----
<?php

try {

} catch (\InvalidArgumentException $e) {

} catch (\Exception $e) {

} catch (\LogicException $e) {

} catch (\Throwable $e) {

}
-----
unset($stmts[0]->catches[2]);
$stmts[0]->catches = array_values($stmts[0]->catches);
-----
<?php

try {

} catch (\InvalidArgumentException $e) {

} catch (\Exception $e) {

} catch (\Throwable $e) {

}

-----
<?php
class Foo
{
    private $id;

    public function getId()
    {
    }

    public function getFoo()
    {
    }
}
-----
$stmts[0]->stmts[2] = new Node\Stmt\ClassMethod('getBar');
$stmts[0]->stmts[3] = new Node\Stmt\ClassMethod('getBaz');
-----
<?php
class Foo
{
    private $id;

    public function getId()
    {
    }
    function getBar()
    {
    }
    function getBaz()
    {
    }
}
-----
<?php
class Test {
    use A, B;
}
-----
unset($stmts[0]->stmts[0]->traits[0]);
-----
<?php
class Test {
    use B;
}
