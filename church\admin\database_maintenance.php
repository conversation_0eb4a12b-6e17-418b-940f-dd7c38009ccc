<?php
require_once '../config/database.php';
require_once 'includes/session-manager.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit();
}

$success_messages = [];
$error_messages = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        switch ($_POST['action']) {
            case 'fix_structure':
                // Disable foreign key checks temporarily
                $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
                
                // Create missing tables
                $tables_created = 0;
                
                // Email logs table
                try {
                    $pdo->exec("CREATE TABLE IF NOT EXISTS email_logs (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        member_id INT NOT NULL,
                        email_type VARCHAR(100) NOT NULL,
                        subject VARCHAR(255) NOT NULL,
                        message TEXT,
                        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        status ENUM('sent', 'failed', 'pending') DEFAULT 'sent',
                        error_message TEXT NULL,
                        INDEX idx_member_id (member_id),
                        INDEX idx_sent_at (sent_at),
                        INDEX idx_status (status),
                        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
                    $tables_created++;
                } catch (PDOException $e) {
                    $error_messages[] = "Error creating email_logs: " . $e->getMessage();
                }
                
                // Email tracking table
                try {
                    $pdo->exec("CREATE TABLE IF NOT EXISTS email_tracking (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        member_id INT NOT NULL,
                        email_id VARCHAR(255) NOT NULL,
                        opened_at TIMESTAMP NULL,
                        clicked_at TIMESTAMP NULL,
                        bounced_at TIMESTAMP NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_member_id (member_id),
                        INDEX idx_email_id (email_id),
                        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
                    $tables_created++;
                } catch (PDOException $e) {
                    $error_messages[] = "Error creating email_tracking: " . $e->getMessage();
                }
                
                // Volunteer opportunities table
                try {
                    $pdo->exec("CREATE TABLE IF NOT EXISTS volunteer_opportunities (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        title VARCHAR(255) NOT NULL,
                        description TEXT,
                        requirements TEXT,
                        location VARCHAR(255),
                        start_date DATE,
                        end_date DATE,
                        time_commitment VARCHAR(100),
                        contact_person_id INT NULL,
                        max_volunteers INT DEFAULT NULL,
                        current_volunteers INT DEFAULT 0,
                        status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_contact_person (contact_person_id),
                        INDEX idx_status (status),
                        INDEX idx_start_date (start_date),
                        FOREIGN KEY (contact_person_id) REFERENCES members(id) ON DELETE SET NULL
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
                    $tables_created++;
                } catch (PDOException $e) {
                    $error_messages[] = "Error creating volunteer_opportunities: " . $e->getMessage();
                }
                
                // Volunteer applications table
                try {
                    $pdo->exec("CREATE TABLE IF NOT EXISTS volunteer_applications (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        opportunity_id INT NOT NULL,
                        member_id INT NOT NULL,
                        application_message TEXT,
                        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        reviewed_at TIMESTAMP NULL,
                        reviewed_by INT NULL,
                        INDEX idx_opportunity_id (opportunity_id),
                        INDEX idx_member_id (member_id),
                        INDEX idx_status (status),
                        UNIQUE KEY unique_application (opportunity_id, member_id),
                        FOREIGN KEY (opportunity_id) REFERENCES volunteer_opportunities(id) ON DELETE CASCADE,
                        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
                    $tables_created++;
                } catch (PDOException $e) {
                    $error_messages[] = "Error creating volunteer_applications: " . $e->getMessage();
                }
                
                // Event RSVPs table
                try {
                    $pdo->exec("CREATE TABLE IF NOT EXISTS event_rsvps (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        event_id INT NOT NULL,
                        member_id INT NOT NULL,
                        status ENUM('attending', 'not_attending', 'maybe') DEFAULT 'attending',
                        guest_count INT DEFAULT 0,
                        notes TEXT,
                        rsvp_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_event_id (event_id),
                        INDEX idx_member_id (member_id),
                        INDEX idx_status (status),
                        UNIQUE KEY unique_event_member (event_id, member_id),
                        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
                    $tables_created++;
                } catch (PDOException $e) {
                    $error_messages[] = "Error creating event_rsvps: " . $e->getMessage();
                }
                
                // Re-enable foreign key checks
                $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
                
                $success_messages[] = "Database structure fixed. $tables_created tables created/verified.";
                break;
                
            case 'clean_orphans':
                $cleaned = 0;
                
                // Clean orphaned volunteer opportunities
                try {
                    $stmt = $pdo->prepare("
                        UPDATE volunteer_opportunities 
                        SET contact_person_id = NULL 
                        WHERE contact_person_id IS NOT NULL 
                        AND contact_person_id NOT IN (SELECT id FROM members)
                    ");
                    $stmt->execute();
                    $cleaned += $stmt->rowCount();
                } catch (PDOException $e) {
                    $error_messages[] = "Error cleaning orphaned records: " . $e->getMessage();
                }
                
                $success_messages[] = "Cleaned $cleaned orphaned records.";
                break;
        }
        
    } catch (PDOException $e) {
        $error_messages[] = "Database error: " . $e->getMessage();
    }
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Database Maintenance</h1>
            </div>
            
            <?php if (!empty($success_messages)): ?>
                <div class="alert alert-success">
                    <?php foreach ($success_messages as $message): ?>
                        <div><?php echo htmlspecialchars($message); ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_messages)): ?>
                <div class="alert alert-danger">
                    <?php foreach ($error_messages as $message): ?>
                        <div><?php echo htmlspecialchars($message); ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Fix Database Structure</h5>
                        </div>
                        <div class="card-body">
                            <p>This will create missing tables and fix foreign key constraints that are causing deletion errors.</p>
                            <form method="POST">
                                <input type="hidden" name="action" value="fix_structure">
                                <button type="submit" class="btn btn-primary">Fix Database Structure</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Clean Orphaned Records</h5>
                        </div>
                        <div class="card-body">
                            <p>This will clean up orphaned records that reference non-existent members.</p>
                            <form method="POST">
                                <input type="hidden" name="action" value="clean_orphans">
                                <button type="submit" class="btn btn-warning">Clean Orphaned Records</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
