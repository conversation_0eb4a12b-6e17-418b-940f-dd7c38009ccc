Precedence of concatenation in PHP 7 and PHP 8
-----
<?php
1 + 2 . 3 + 4;
1 << 2 . 3 << 4;
-----
!!version=8.0
array(
    0: Stmt_Expression(
        expr: Expr_BinaryOp_Concat(
            left: Expr_BinaryOp_Plus(
                left: Scalar_Int(
                    value: 1
                )
                right: Scalar_Int(
                    value: 2
                )
            )
            right: Expr_BinaryOp_Plus(
                left: Scalar_Int(
                    value: 3
                )
                right: Scalar_Int(
                    value: 4
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_BinaryOp_Concat(
            left: Expr_BinaryOp_ShiftLeft(
                left: Scalar_Int(
                    value: 1
                )
                right: Scalar_Int(
                    value: 2
                )
            )
            right: Expr_BinaryOp_ShiftLeft(
                left: Scalar_Int(
                    value: 3
                )
                right: Scalar_Int(
                    value: 4
                )
            )
        )
    )
)
-----
<?php
1 + 2 . 3 + 4;
1 << 2 . 3 << 4;
-----
!!version=7.4
array(
    0: Stmt_Expression(
        expr: Expr_BinaryOp_Plus(
            left: Expr_BinaryOp_Concat(
                left: Expr_BinaryOp_Plus(
                    left: Scalar_Int(
                        value: 1
                    )
                    right: Scalar_Int(
                        value: 2
                    )
                )
                right: Scalar_Int(
                    value: 3
                )
            )
            right: Scalar_Int(
                value: 4
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_BinaryOp_ShiftLeft(
            left: Expr_BinaryOp_ShiftLeft(
                left: Scalar_Int(
                    value: 1
                )
                right: Expr_BinaryOp_Concat(
                    left: Scalar_Int(
                        value: 2
                    )
                    right: Scalar_Int(
                        value: 3
                    )
                )
            )
            right: Scalar_Int(
                value: 4
            )
        )
    )
)
