<?php
/**
 * Enhanced Donation Processing
 * 
 * Processes enhanced donations with gift features, organization information, and automatic notifications
 */

require_once 'config.php';
require_once 'includes/email_functions.php';

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die("Invalid request method");
}

// Get organization information for email notifications
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('organization_name', 'organization_type', 'sender_name', 'sender_email')");
$stmt->execute();
$org_settings = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $org_settings[$row['setting_key']] = $row['setting_value'];
}

$organization_name = $org_settings['organization_name'] ?? get_organization_name();
$organization_type = $org_settings['organization_type'] ?? 'church';
$system_sender_name = $org_settings['sender_name'] ?? $organization_name;
$system_sender_email = $org_settings['sender_email'] ?? '<EMAIL>';

// Get payment settings
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM payment_settings");
$stmt->execute();
$payment_settings = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $payment_settings[$row['setting_key']] = $row['setting_value'];
}

// Validate and sanitize input data
$donation_type = isset($_POST['donation_type']) ? trim($_POST['donation_type']) : '';
$donor_name = isset($_POST['donor_name']) ? trim($_POST['donor_name']) : '';
$donor_email = isset($_POST['donor_email']) ? filter_var($_POST['donor_email'], FILTER_VALIDATE_EMAIL) : '';
$amount = isset($_POST['amount']) ? filter_var($_POST['amount'], FILTER_VALIDATE_FLOAT) : '';
$currency = isset($_POST['currency']) ? trim($_POST['currency']) : '';
$payment_method = isset($_POST['payment_method']) ? trim($_POST['payment_method']) : '';
$message = isset($_POST['message']) ? trim($_POST['message']) : '';

// Enhanced donation fields
$sender_organization = isset($_POST['sender_organization']) ? trim($_POST['sender_organization']) : '';
$sender_department = isset($_POST['sender_department']) ? trim($_POST['sender_department']) : '';
$recipient_id = isset($_POST['recipient_id']) ? filter_var($_POST['recipient_id'], FILTER_VALIDATE_INT) : null;
$gift_type = isset($_POST['gift_type']) ? trim($_POST['gift_type']) : 'monetary';
$gift_template_id = isset($_POST['gift_template_id']) ? filter_var($_POST['gift_template_id'], FILTER_VALIDATE_INT) : null;
$sender_message = isset($_POST['sender_message']) ? trim($_POST['sender_message']) : '';
$delivery_method = isset($_POST['delivery_method']) ? trim($_POST['delivery_method']) : 'immediate';
$scheduled_delivery_date = isset($_POST['scheduled_delivery_date']) ? trim($_POST['scheduled_delivery_date']) : null;
$scheduled_delivery_time = isset($_POST['scheduled_delivery_time']) ? trim($_POST['scheduled_delivery_time']) : null;
$anonymous_gift = isset($_POST['anonymous_gift']) ? 1 : 0;

// Validation
if (!$donor_name || !$donor_email || !$amount || !$currency || !$payment_method) {
    die("Missing required fields");
}

if ($amount < ($payment_settings['minimum_donation_amount'] ?? 5)) {
    die("Amount below minimum donation");
}

// Validate donation type specific requirements
if ($donation_type === 'birthday_gift') {
    if (!$recipient_id) {
        die("Birthday gift requires a recipient");
    }
    
    // Verify recipient exists and has an upcoming birthday
    $stmt = $pdo->prepare("SELECT id, full_name, email, birth_date FROM members WHERE id = ? AND birth_date IS NOT NULL");
    $stmt->execute([$recipient_id]);
    $recipient = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$recipient) {
        die("Invalid recipient or no birthday information");
    }
}

// Validate payment method
$valid_payment_methods = ['paypal', 'stripe'];
if (!in_array($payment_method, $valid_payment_methods)) {
    die("Invalid payment method");
}

// Start transaction
try {
    $pdo->beginTransaction();
    
    // Create donation record with enhanced fields
    $stmt = $pdo->prepare("INSERT INTO donations (
        donor_name, donor_email, amount, currency, payment_method,
        donation_type, recipient_id, message, payment_status,
        gift_type, delivery_method, scheduled_delivery_date, scheduled_delivery_time,
        sender_message, anonymous_gift, sender_organization, sender_department,
        gift_template_id, delivery_status
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')");
    
    $stmt->execute([
        $donor_name, $donor_email, $amount, $currency, $payment_method,
        $donation_type, $recipient_id, $message,
        $gift_type, $delivery_method, $scheduled_delivery_date, $scheduled_delivery_time,
        $sender_message, $anonymous_gift, $sender_organization, $sender_department,
        $gift_template_id
    ]);
    
    $donation_id = $pdo->lastInsertId();
    
    // Create birthday gift record if applicable
    if ($donation_type === 'birthday_gift') {
        // Determine delivery preference based on delivery method
        $delivery_preference = 'immediate';
        if ($delivery_method === 'scheduled') {
            if ($scheduled_delivery_date) {
                $delivery_preference = 'custom_time';
            } else {
                $delivery_preference = 'birthday_morning';
            }
        }
        
        $stmt = $pdo->prepare("INSERT INTO birthday_gifts (
            donation_id, celebrant_id, sender_id, gift_type, gift_template_id,
            custom_message, delivery_preference, delivery_time, is_surprise
        ) VALUES (?, ?, NULL, ?, ?, ?, ?, ?, ?)");
        
        $delivery_time = $scheduled_delivery_time ?: '09:00:00';
        
        $stmt->execute([
            $donation_id, $recipient_id, $gift_type, $gift_template_id,
            $sender_message, $delivery_preference, $delivery_time, $anonymous_gift
        ]);
    }
    
    // Process payment based on method
    if ($payment_method === 'paypal') {
        // Check if PayPal SDK is available
        if (!file_exists('vendor/autoload.php')) {
            throw new Exception("PayPal SDK not found. Please contact the administrator to install PayPal integration.");
        }

        require_once 'vendor/autoload.php';

        // Check if PayPal classes exist
        if (!class_exists('\\PayPalCheckoutSdk\\Core\\SandboxEnvironment')) {
            throw new Exception("PayPal SDK not properly installed. Please contact the administrator.");
        }

        $paypal_client_id = $payment_settings['paypal_client_id'] ?? '';
        $paypal_client_secret = $payment_settings['paypal_client_secret'] ?? '';
        $paypal_sandbox = isset($payment_settings['paypal_sandbox_mode']) && $payment_settings['paypal_sandbox_mode'] === '1';

        // Check if we have valid PayPal credentials
        if (!$paypal_client_id || !$paypal_client_secret ||
            $paypal_client_id === 'your_paypal_client_id' ||
            $paypal_client_secret === 'your_paypal_client_secret') {

            // Demo mode - simulate successful payment for testing
            error_log("PayPal credentials not configured. Running in demo mode.");

            // Store demo transaction details
            $demo_transaction_id = 'DEMO_' . uniqid() . '_' . time();
            $stmt = $pdo->prepare("INSERT INTO payment_transactions (
                donation_id, payment_provider, provider_transaction_id,
                amount, currency, payment_status, payment_method_details
            ) VALUES (?, ?, ?, ?, ?, ?, ?)");

            $stmt->execute([
                $donation_id,
                'paypal_demo',
                $demo_transaction_id,
                $amount,
                $currency,
                'completed',
                json_encode(['demo_mode' => true, 'message' => 'Demo payment - no real money processed'])
            ]);

            $pdo->commit();

            // Redirect to success page for demo
            header("Location: donation_success.php?id=" . $donation_id . "&demo=1");
            exit();
        }

        try {
            $environment = $paypal_sandbox ?
                new \PayPalCheckoutSdk\Core\SandboxEnvironment($paypal_client_id, $paypal_client_secret) :
                new \PayPalCheckoutSdk\Core\ProductionEnvironment($paypal_client_id, $paypal_client_secret);

            $client = new \PayPalCheckoutSdk\Core\PayPalHttpClient($environment);

            // Create order
            $request = new \PayPalCheckoutSdk\Orders\OrdersCreateRequest();
            $request->prefer('return=representation');

            $description = $donation_type === 'birthday_gift' ?
                "Birthday Gift for " . ($recipient['full_name'] ?? 'Member') :
                "Donation to " . $organization_name;

            $request->body = [
                'intent' => 'CAPTURE',
                'purchase_units' => [[
                    'reference_id' => $donation_id,
                    'amount' => [
                        'currency_code' => $currency,
                        'value' => number_format($amount, 2, '.', '')
                    ],
                    'description' => $description
                ]],
                'application_context' => [
                    'return_url' => SITE_URL . '/complete_enhanced_payment.php',
                    'cancel_url' => SITE_URL . '/cancel_payment.php'
                ]
            ];

            $response = $client->execute($request);
        } catch (\PayPalHttp\HttpException $e) {
            error_log("PayPal HTTP Error: " . $e->getMessage());
            throw new Exception("PayPal payment setup failed. Please try again or contact support.");
        } catch (Exception $e) {
            error_log("PayPal Error: " . $e->getMessage());
            throw new Exception("PayPal payment setup failed: " . $e->getMessage());
        }
        
        // Store transaction details
        $stmt = $pdo->prepare("INSERT INTO payment_transactions (
            donation_id, payment_provider, provider_transaction_id,
            amount, currency, payment_status, payment_method_details
        ) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        $stmt->execute([
            $donation_id,
            'paypal',
            $response->result->id,
            $amount,
            $currency,
            'pending',
            json_encode($response->result)
        ]);
        
        $pdo->commit();
        
        // Redirect to PayPal
        foreach ($response->result->links as $link) {
            if ($link->rel === 'approve') {
                header('Location: ' . $link->href);
                exit();
            }
        }
        
    } elseif ($payment_method === 'stripe') {
        // Stripe processing
        require_once 'vendor/autoload.php';
        
        $stripe_secret_key = $payment_settings['stripe_secret_key'] ?? '';
        if (!$stripe_secret_key) {
            throw new Exception("Stripe configuration missing");
        }
        
        \Stripe\Stripe::setApiKey($stripe_secret_key);
        
        // Create payment intent
        $payment_intent = \Stripe\PaymentIntent::create([
            'amount' => $amount * 100, // Convert to cents
            'currency' => strtolower($currency),
            'payment_method_types' => ['card'],
            'metadata' => [
                'donation_id' => $donation_id,
                'donation_type' => $donation_type,
                'donor_name' => $donor_name,
                'donor_email' => $donor_email,
                'organization' => $sender_organization ?: $organization_name
            ]
        ]);
        
        // Store transaction details
        $stmt = $pdo->prepare("INSERT INTO payment_transactions (
            donation_id, payment_provider, provider_transaction_id,
            amount, currency, payment_status, payment_method_details
        ) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        $stmt->execute([
            $donation_id,
            'stripe',
            $payment_intent->id,
            $amount,
            $currency,
            'pending',
            json_encode($payment_intent)
        ]);
        
        $pdo->commit();
        
        // Redirect to Stripe checkout or return client secret for frontend processing
        header("Location: stripe_checkout.php?donation_id=" . $donation_id . "&client_secret=" . $payment_intent->client_secret);
        exit();
    }
    
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Database Error in enhanced donation processing: " . $e->getMessage());
    die("Error processing donation. Please try again later.");
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Error in enhanced donation processing: " . $e->getMessage());
    die("Error processing payment: " . $e->getMessage());
}

?>
