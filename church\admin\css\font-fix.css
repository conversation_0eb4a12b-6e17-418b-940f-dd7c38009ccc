/* Font fix for CSP issues */
@font-face {
    font-family: 'fcicons';
    src: url('../font-access.php') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* Ensure Bootstrap icons are loaded correctly */
@font-face {
    font-family: 'bootstrap-icons';
    src: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff') format('woff');
}

/* Font Awesome icons fallback */
@font-face {
    font-family: 'FontAwesome';
    src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-solid-900.woff2') format('woff2'),
         url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-solid-900.woff') format('woff');
    font-weight: 900;
    font-style: normal;
} 