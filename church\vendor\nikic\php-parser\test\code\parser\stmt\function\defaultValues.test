Default values (static scalar tests)
-----
<?php

function a(
    $b = null,
    $c = 'foo',
    $d = A::B,
    $f = +1,
    $g = -1.0,
    $h = array(),
    $i = [],
    $j = ['foo'],
    $k = ['foo', 'bar' => 'baz']
) {}
-----
array(
    0: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: a
        )
        params: array(
            0: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: b
                )
                default: Expr_ConstFetch(
                    name: Name(
                        name: null
                    )
                )
                hooks: array(
                )
            )
            1: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: c
                )
                default: Scalar_String(
                    value: foo
                )
                hooks: array(
                )
            )
            2: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: d
                )
                default: Expr_ClassConstFetch(
                    class: Name(
                        name: A
                    )
                    name: Identifier(
                        name: B
                    )
                )
                hooks: array(
                )
            )
            3: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: f
                )
                default: Expr_UnaryPlus(
                    expr: Scalar_Int(
                        value: 1
                    )
                )
                hooks: array(
                )
            )
            4: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: g
                )
                default: Expr_UnaryMinus(
                    expr: Scalar_Float(
                        value: 1
                    )
                )
                hooks: array(
                )
            )
            5: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: h
                )
                default: Expr_Array(
                    items: array(
                    )
                )
                hooks: array(
                )
            )
            6: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: i
                )
                default: Expr_Array(
                    items: array(
                    )
                )
                hooks: array(
                )
            )
            7: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: j
                )
                default: Expr_Array(
                    items: array(
                        0: ArrayItem(
                            key: null
                            value: Scalar_String(
                                value: foo
                            )
                            byRef: false
                            unpack: false
                        )
                    )
                )
                hooks: array(
                )
            )
            8: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: k
                )
                default: Expr_Array(
                    items: array(
                        0: ArrayItem(
                            key: null
                            value: Scalar_String(
                                value: foo
                            )
                            byRef: false
                            unpack: false
                        )
                        1: ArrayItem(
                            key: Scalar_String(
                                value: bar
                            )
                            value: Scalar_String(
                                value: baz
                            )
                            byRef: false
                            unpack: false
                        )
                    )
                )
                hooks: array(
                )
            )
        )
        returnType: null
        stmts: array(
        )
    )
)
