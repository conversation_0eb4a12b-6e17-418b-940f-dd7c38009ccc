/**
 * Promotional Materials CSS
 * 
 * Responsive styles for event header banners and promotional materials
 */

/* Header Banner Containers */
.card-img-top-container {
    position: relative;
    overflow: hidden;
    border-top-left-radius: var(--bs-card-border-radius, 0.375rem);
    border-top-right-radius: var(--bs-card-border-radius, 0.375rem);
}

.card-img-top-container img {
    transition: transform 0.3s ease;
}

.card-img-top-container:hover img {
    transform: scale(1.05);
}

/* Event Hero Banner Styles */
.event-hero {
    min-height: 400px;
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

@media (max-width: 768px) {
    .event-hero {
        min-height: 300px;
        background-attachment: scroll;
    }
}

/* Responsive Banner Heights */
.banner-sm {
    height: 150px;
}

.banner-md {
    height: 200px;
}

.banner-lg {
    height: 300px;
}

.banner-xl {
    height: 400px;
}

/* Mobile Responsive Adjustments */
@media (max-width: 576px) {
    .banner-sm { height: 120px; }
    .banner-md { height: 150px; }
    .banner-lg { height: 200px; }
    .banner-xl { height: 250px; }
}

/* PDF Banner Styles */
.pdf-banner-container {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px dashed #dee2e6;
    border-radius: var(--bs-border-radius, 0.375rem);
    transition: all 0.3s ease;
}

.pdf-banner-container:hover {
    border-color: var(--bs-primary, #0d6efd);
    background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
}

.pdf-banner-icon {
    font-size: 3rem;
    color: var(--bs-danger, #dc3545);
    margin-bottom: 1rem;
}

/* Thumbnail Styles */
.thumbnail-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--bs-border-radius, 0.375rem);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.thumbnail-container:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.thumbnail-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.thumbnail-container:hover .thumbnail-overlay {
    opacity: 1;
}

/* File Upload Area Styles */
.file-upload-area {
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.file-upload-area:hover {
    border-color: var(--bs-primary, #0d6efd) !important;
    background-color: rgba(13, 110, 253, 0.05) !important;
}

.file-upload-area.dragover {
    border-color: var(--bs-success, #198754) !important;
    background-color: rgba(25, 135, 84, 0.1) !important;
}

/* Header Banner Selection Grid */
.header-banner-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
}

@media (max-width: 768px) {
    .header-banner-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.5rem;
    }
}

.header-banner-option {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: var(--bs-border-radius, 0.375rem);
    overflow: hidden;
}

.header-banner-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.header-banner-option.selected {
    border: 2px solid var(--bs-primary, #0d6efd);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Promotional Materials Grid */
.promotional-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

@media (max-width: 768px) {
    .promotional-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

/* Loading States */
.loading-banner {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Print Styles */
@media print {
    .file-upload-area,
    .header-banner-grid,
    .promotional-grid {
        display: none;
    }
    
    .card-img-top-container {
        height: auto !important;
        max-height: 300px;
    }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
.file-upload-area:focus-within,
.header-banner-option:focus-within,
.thumbnail-container:focus-within {
    outline: 2px solid var(--bs-primary, #0d6efd);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .pdf-banner-container {
        border-width: 3px;
    }
    
    .thumbnail-overlay {
        background: rgba(0,0,0,0.8);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .card-img-top-container img,
    .file-upload-area,
    .header-banner-option,
    .thumbnail-container {
        transition: none;
    }
    
    .loading-banner {
        animation: none;
    }
}
