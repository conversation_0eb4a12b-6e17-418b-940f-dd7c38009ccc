<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../config.php';

// Get opportunity ID
$opportunity_id = isset($_GET['opportunity_id']) ? (int)$_GET['opportunity_id'] : 0;

if (!$opportunity_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid opportunity ID']);
    exit();
}

try {
    // Get opportunity title
    $stmt = $pdo->prepare("SELECT title FROM volunteer_opportunities WHERE id = ?");
    $stmt->execute([$opportunity_id]);
    $opportunity = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$opportunity) {
        echo json_encode(['success' => false, 'message' => 'Opportunity not found']);
        exit();
    }
    
    // Get applications for this opportunity
    $stmt = $pdo->prepare("
        SELECT
            va.*,
            m.first_name,
            m.last_name,
            m.email,
            COALESCE(m.full_name, CONCAT(m.first_name, ' ', m.last_name), m.email) as applicant_name,
            m.email as applicant_email
        FROM volunteer_applications va
        LEFT JOIN members m ON va.member_id = m.id
        WHERE va.opportunity_id = ?
        ORDER BY va.applied_date DESC
    ");
    $stmt->execute([$opportunity_id]);
    $applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'opportunity_title' => $opportunity['title'],
        'applications' => $applications
    ]);
    
} catch (PDOException $e) {
    error_log("Get opportunity applications error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
