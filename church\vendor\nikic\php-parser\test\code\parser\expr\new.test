New
-----
<?php

new A;
new A($b);

// class name variations
new $a();
new $a['b']();
new A::$b();
// DNCR object access
new $a->b();
new $a->b->c();
new $a->b['c']();

// test regression introduces by new dereferencing syntax
(new A);
-----
array(
    0: Stmt_Expression(
        expr: Expr_New(
            class: Name(
                name: A
            )
            args: array(
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_New(
            class: Name(
                name: A
            )
            args: array(
                0: Arg(
                    name: null
                    value: Expr_Variable(
                        name: b
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_New(
            class: Expr_Variable(
                name: a
            )
            args: array(
            )
        )
        comments: array(
            0: // class name variations
        )
    )
    3: Stmt_Expression(
        expr: Expr_New(
            class: Expr_ArrayDimFetch(
                var: Expr_Variable(
                    name: a
                )
                dim: Scalar_String(
                    value: b
                )
            )
            args: array(
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_New(
            class: Expr_StaticPropertyFetch(
                class: Name(
                    name: A
                )
                name: VarLikeIdentifier(
                    name: b
                )
            )
            args: array(
            )
        )
    )
    5: Stmt_Expression(
        expr: Expr_New(
            class: Expr_PropertyFetch(
                var: Expr_Variable(
                    name: a
                )
                name: Identifier(
                    name: b
                )
            )
            args: array(
            )
        )
        comments: array(
            0: // DNCR object access
        )
    )
    6: Stmt_Expression(
        expr: Expr_New(
            class: Expr_PropertyFetch(
                var: Expr_PropertyFetch(
                    var: Expr_Variable(
                        name: a
                    )
                    name: Identifier(
                        name: b
                    )
                )
                name: Identifier(
                    name: c
                )
            )
            args: array(
            )
        )
    )
    7: Stmt_Expression(
        expr: Expr_New(
            class: Expr_ArrayDimFetch(
                var: Expr_PropertyFetch(
                    var: Expr_Variable(
                        name: a
                    )
                    name: Identifier(
                        name: b
                    )
                )
                dim: Scalar_String(
                    value: c
                )
            )
            args: array(
            )
        )
    )
    8: Stmt_Expression(
        expr: Expr_New(
            class: Name(
                name: A
            )
            args: array(
            )
        )
        comments: array(
            0: // test regression introduces by new dereferencing syntax
        )
    )
)
