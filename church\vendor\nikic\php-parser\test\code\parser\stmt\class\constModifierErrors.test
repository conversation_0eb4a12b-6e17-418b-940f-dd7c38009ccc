Invalid class constant modifiers
-----
<?php
class A {
    static const X = 1;
}
-----
Cannot use 'static' as constant modifier from 3:5 to 3:10
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_ClassConst(
                attrGroups: array(
                )
                flags: STATIC (8)
                type: null
                consts: array(
                    0: Const(
                        name: Identifier(
                            name: X
                        )
                        value: Scalar_Int(
                            value: 1
                        )
                    )
                )
            )
        )
    )
)
-----
<?php
class A {
    abstract const X = 1;
}
-----
Cannot use 'abstract' as constant modifier from 3:5 to 3:12
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_ClassConst(
                attrGroups: array(
                )
                flags: ABSTRACT (16)
                type: null
                consts: array(
                    0: Const(
                        name: Identifier(
                            name: X
                        )
                        value: <PERSON>alar_Int(
                            value: 1
                        )
                    )
                )
            )
        )
    )
)
-----
<?php
class A {
    readonly const X = 1;
}
-----
Cannot use 'readonly' as constant modifier from 3:5 to 3:12
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_ClassConst(
                attrGroups: array(
                )
                flags: READONLY (64)
                type: null
                consts: array(
                    0: Const(
                        name: Identifier(
                            name: X
                        )
                        value: Scalar_Int(
                            value: 1
                        )
                    )
                )
            )
        )
    )
)
-----
<?php
class A {
    public public const X = 1;
}
-----
Multiple access type modifiers are not allowed from 3:12 to 3:17
array(
    0: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_ClassConst(
                attrGroups: array(
                )
                flags: PUBLIC (1)
                type: null
                consts: array(
                    0: Const(
                        name: Identifier(
                            name: X
                        )
                        value: Scalar_Int(
                            value: 1
                        )
                    )
                )
            )
        )
    )
)
