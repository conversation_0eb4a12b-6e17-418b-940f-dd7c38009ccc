<?php
/**
 * Test script to validate birthday notification template fixes
 * This script tests the image handling improvements for birthday notifications
 */

require_once 'config.php';

echo "<h1>Birthday Notification Template Fixes - Test Results</h1>\n";

// Test 1: Template Replacement Function with Birthday Notification
echo "<h2>Test 1: Template Replacement Function</h2>\n";

$testMemberData = [
    'full_name' => '<PERSON>',
    'first_name' => '<PERSON>',
    'email' => '<EMAIL>',
    'birthday_member_name' => '<PERSON>',
    'birthday_member_full_name' => '<PERSON>',
    'birthday_member_email' => '<EMAIL>',
    'birthday_member_photo_url' => 'https://example.com/uploads/jane.jpg',
    '_is_birthday_notification' => true,
    '_birthday_member_original_image_path' => 'uploads/jane.jpg'
];

$testTemplate = 'Happy Birthday {birthday_member_name}! Here is their photo: {member_image_url}';

echo "<p><strong>Test Data:</strong></p>\n";
echo "<pre>" . print_r($testMemberData, true) . "</pre>\n";

echo "<p><strong>Template:</strong> " . htmlspecialchars($testTemplate) . "</p>\n";

$result = replaceTemplatePlaceholders($testTemplate, $testMemberData);

echo "<p><strong>Result:</strong> " . htmlspecialchars($result) . "</p>\n";

// Check if celebrant's image URL is used
if (strpos($result, 'https://example.com/uploads/jane.jpg') !== false) {
    echo "<p style='color: green;'>✓ PASS: Celebrant's image URL is correctly used</p>\n";
} else {
    echo "<p style='color: red;'>✗ FAIL: Celebrant's image URL not found in result</p>\n";
}

// Test 2: Birthday Notification Detection
echo "<h2>Test 2: Birthday Notification Detection</h2>\n";

$testCases = [
    ['_is_birthday_notification' => true],
    ['birthday_member_name' => 'Jane'],
    ['birthday_member_full_name' => 'Jane Smith'],
    ['birthday_member_photo_url' => 'https://example.com/jane.jpg'],
    ['regular_member' => true] // Should not be detected as birthday notification
];

foreach ($testCases as $index => $testCase) {
    echo "<p><strong>Test Case " . ($index + 1) . ":</strong> " . json_encode($testCase) . "</p>\n";
    
    $testResult = replaceTemplatePlaceholders('{member_image_url}', $testCase);
    
    // The function should detect birthday notifications for cases 1-4 but not case 5
    $shouldBeBirthday = $index < 4;
    
    if ($shouldBeBirthday) {
        echo "<p style='color: blue;'>Expected: Birthday notification detected</p>\n";
    } else {
        echo "<p style='color: blue;'>Expected: Regular email (not birthday notification)</p>\n";
    }
}

// Test 3: Image URL Construction
echo "<h2>Test 3: Image URL Construction</h2>\n";

$imageTestCases = [
    [
        'name' => 'Birthday notification with full URL',
        'data' => [
            '_is_birthday_notification' => true,
            'birthday_member_photo_url' => 'https://example.com/uploads/jane.jpg'
        ],
        'expected' => 'https://example.com/uploads/jane.jpg'
    ],
    [
        'name' => 'Birthday notification with relative path',
        'data' => [
            '_is_birthday_notification' => true,
            'birthday_member_image' => 'uploads/jane.jpg'
        ],
        'expected_contains' => 'uploads/jane.jpg'
    ],
    [
        'name' => 'Regular email with member image',
        'data' => [
            'member_image_url' => 'https://example.com/uploads/john.jpg'
        ],
        'expected' => 'https://example.com/uploads/john.jpg'
    ]
];

foreach ($imageTestCases as $testCase) {
    echo "<p><strong>" . $testCase['name'] . ":</strong></p>\n";
    
    $result = replaceTemplatePlaceholders('{member_image_url}', $testCase['data']);
    
    echo "<p>Result: " . htmlspecialchars($result) . "</p>\n";
    
    if (isset($testCase['expected'])) {
        if ($result === $testCase['expected']) {
            echo "<p style='color: green;'>✓ PASS: Exact match</p>\n";
        } else {
            echo "<p style='color: red;'>✗ FAIL: Expected '" . $testCase['expected'] . "'</p>\n";
        }
    } elseif (isset($testCase['expected_contains'])) {
        if (strpos($result, $testCase['expected_contains']) !== false) {
            echo "<p style='color: green;'>✓ PASS: Contains expected content</p>\n";
        } else {
            echo "<p style='color: red;'>✗ FAIL: Does not contain '" . $testCase['expected_contains'] . "'</p>\n";
        }
    }
    
    echo "<hr>\n";
}

// Test 4: Log File Check
echo "<h2>Test 4: Debug Logging</h2>\n";

$logFiles = [
    'logs/email_debug.log',
    'logs/birthday_debug.log',
    'logs/birthday_image_embedding.log'
];

foreach ($logFiles as $logFile) {
    $fullPath = __DIR__ . '/' . $logFile;
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        echo "<p style='color: green;'>✓ Log file exists: $logFile (Size: $size bytes)</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ Log file not found: $logFile (Will be created when needed)</p>\n";
    }
}

echo "<h2>Summary</h2>\n";
echo "<p>The birthday notification template fixes have been implemented with the following improvements:</p>\n";
echo "<ul>\n";
echo "<li>Enhanced birthday notification detection using multiple indicators</li>\n";
echo "<li>Improved celebrant image handling to ensure the birthday member's photo is used</li>\n";
echo "<li>Better email embedding logic with robust URL-to-CID replacement</li>\n";
echo "<li>Consistent flag setting across all birthday notification senders</li>\n";
echo "<li>Comprehensive debug logging for troubleshooting</li>\n";
echo "</ul>\n";

echo "<p><strong>Next Steps:</strong></p>\n";
echo "<ol>\n";
echo "<li>Test with actual birthday notification emails</li>\n";
echo "<li>Check the debug logs for any issues</li>\n";
echo "<li>Verify images display correctly in email clients</li>\n";
echo "<li>Ensure no unwanted attachments are added</li>\n";
echo "</ol>\n";

?>
