<?php
/**
 * Theme API Endpoint
 * Serves appearance settings as CSS for frontend pages
 */

// Set CORS headers to allow cross-origin requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Set content type to CSS
header('Content-Type: text/css');

// Add cache control headers
header('Cache-Control: public, max-age=300'); // Cache for 5 minutes

// Include config
require_once '../config.php';

try {
    // Get all appearance settings
    $stmt = $pdo->prepare("SELECT setting_name, setting_value FROM appearance_settings");
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

    // Generate CSS
    echo ":root {\n";

    foreach ($settings as $name => $value) {
        $cssVar = '--' . str_replace('_', '-', $name);
        // Add 'px' suffix for width and spacing settings
        if (strpos($name, 'width') !== false || strpos($name, 'spacing') !== false) {
            echo "  $cssVar: {$value}px;\n";
        } else {
            echo "  $cssVar: $value;\n";
        }
    }

    // Add Bootstrap CSS variables for compatibility
    $primaryColor = $settings['primary_color'] ?? '#fd7e14';
    $secondaryColor = $settings['secondary_color'] ?? '#fd7e14';

    echo "\n  /* Bootstrap color overrides */\n";
    echo "  --bs-primary: $primaryColor;\n";
    echo "  --bs-secondary: $secondaryColor;\n";

    echo "}\n\n";
    
    // Add themed styles for frontend
    echo "/* Frontend Theme Styles */\n";
    echo ".navbar-brand {\n";
    echo "  color: var(--primary-color, #0d6efd) !important;\n";
    echo "}\n\n";
    
    echo ".btn-primary {\n";
    echo "  background-color: var(--primary-color, #0d6efd) !important;\n";
    echo "  border-color: var(--primary-color, #0d6efd) !important;\n";
    echo "}\n\n";
    
    echo ".btn-secondary {\n";
    echo "  background-color: var(--secondary-color, #6c757d) !important;\n";
    echo "  border-color: var(--secondary-color, #6c757d) !important;\n";
    echo "}\n\n";
    
    echo "body {\n";
    echo "  background-color: var(--background-color, #ffffff) !important;\n";
    echo "  color: var(--text-color, #212529) !important;\n";
    echo "  font-family: var(--primary-font, 'Inter'), sans-serif !important;\n";
    echo "  font-size: var(--base-font-size, 16)px !important;\n";
    echo "  line-height: var(--line-height, 1.5) !important;\n";
    echo "}\n\n";
    
    echo "a {\n";
    echo "  color: var(--link-color, #0d6efd) !important;\n";
    echo "}\n\n";
    
    echo ".text-primary {\n";
    echo "  color: var(--primary-color, #0d6efd) !important;\n";
    echo "}\n\n";
    
    echo ".text-secondary {\n";
    echo "  color: var(--secondary-color, #6c757d) !important;\n";
    echo "}\n\n";
    
    echo ".bg-primary {\n";
    echo "  background-color: var(--primary-color, #0d6efd) !important;\n";
    echo "}\n\n";
    
    echo ".bg-secondary {\n";
    echo "  background-color: var(--secondary-color, #6c757d) !important;\n";
    echo "}\n\n";

} catch (Exception $e) {
    // If there's an error, output empty CSS
    echo "/* Error loading appearance settings: " . $e->getMessage() . " */\n";
}
?>
