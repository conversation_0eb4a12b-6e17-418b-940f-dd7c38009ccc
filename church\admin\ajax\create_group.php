<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Include the configuration file
require_once '../../config.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $name = trim($input['name'] ?? '');
    $description = trim($input['description'] ?? '');
    
    // Validate input
    if (empty($name)) {
        throw new Exception('Group name is required');
    }
    
    if (strlen($name) > 100) {
        throw new Exception('Group name must be 100 characters or less');
    }
    
    if (strlen($description) > 500) {
        throw new Exception('Description must be 500 characters or less');
    }
    
    // Check if group name already exists
    $stmt = $pdo->prepare("SELECT id FROM contact_groups WHERE name = ?");
    $stmt->execute([$name]);
    if ($stmt->fetch()) {
        throw new Exception('A group with this name already exists');
    }
    
    // Insert new group
    $stmt = $pdo->prepare("
        INSERT INTO contact_groups (name, description, created_at)
        VALUES (?, ?, NOW())
    ");

    $stmt->execute([$name, $description]);
    
    $groupId = $pdo->lastInsertId();
    
    echo json_encode([
        'success' => true,
        'message' => 'Group created successfully',
        'group_id' => $groupId
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
