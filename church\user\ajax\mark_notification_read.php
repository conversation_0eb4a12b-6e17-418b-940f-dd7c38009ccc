<?php
/**
 * Mark Notification as Read AJAX Endpoint
 * 
 * Marks a specific notification as read
 */

session_start();
require_once '../../config.php';
require_once '../../includes/notification_functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit;
}

$userId = $_SESSION['user_id'];

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['notification_id'])) {
    echo json_encode(['success' => false, 'error' => 'Notification ID required']);
    exit;
}

$notificationId = (int)$input['notification_id'];

try {
    $success = markNotificationAsRead($pdo, $notificationId, $userId);
    
    echo json_encode([
        'success' => $success,
        'message' => $success ? 'Notification marked as read' : 'Failed to mark notification as read'
    ]);
    
} catch (Exception $e) {
    error_log("Error marking notification as read: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to mark notification as read'
    ]);
}
?>
