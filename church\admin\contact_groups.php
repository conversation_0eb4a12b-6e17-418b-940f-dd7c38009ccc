<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include language system
require_once 'includes/language.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Create tables if they don't exist
try {
    $sql_file = __DIR__ . '/sql/contact_groups_tables.sql';
    if (file_exists($sql_file)) {
        $sql = file_get_contents($sql_file);
        if ($sql !== false && !empty(trim($sql))) {
            $conn->exec($sql);
        }
    }
} catch (PDOException $e) {
    error_log("Error creating contact groups tables: " . $e->getMessage());
}

// Handle file upload to group
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['contact_file']) && isset($_POST['group_id'])) {
    $file = $_FILES['contact_file'];
    $fileName = $file['name'];
    $fileType = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    $group_id = $_POST['group_id'];
    
    // Check file type
    if ($fileType != "csv" && $fileType != "txt") {
        $error = "Only CSV and TXT files are allowed.";
    } else {
        try {
            $handle = fopen($file['tmp_name'], "r");
            $conn->beginTransaction();
            $success_count = 0;
            $error_count = 0;
            
            while (($data = fgetcsv($handle, 1000, "|")) !== FALSE) {
                if (count($data) >= 1) {
                    $email = trim($data[0]);
                    $name = isset($data[1]) ? trim($data[1]) : '';
                    
                    // Validate email
                    if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        // If name is empty, use email prefix
                        if (empty($name)) {
                            $name = ucfirst(explode('@', $email)[0]);
                        }
                        
                        try {
                            // Insert or get contact
                            $stmt = $conn->prepare("INSERT INTO contacts (email, name) VALUES (?, ?) 
                                                  ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id), name = VALUES(name)");
                            $stmt->execute([$email, $name]);
                            $contact_id = $conn->lastInsertId();
                            
                            // Add to group
                            $stmt = $conn->prepare("INSERT IGNORE INTO contact_group_members (contact_id, group_id) VALUES (?, ?)");
                            $stmt->execute([$contact_id, $group_id]);
                            $success_count++;
                        } catch (PDOException $e) {
                            $error_count++;
                        }
                    } else {
                        $error_count++;
                    }
                }
            }
            
            fclose($handle);
            $conn->commit();
            
            $message = "Successfully imported $success_count contacts to group." . 
                      ($error_count > 0 ? " Failed to import $error_count entries." : "");
        } catch (PDOException $e) {
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            $error = "Error: " . $e->getMessage();
        }
    }
}

// Get all groups with contact count
try {
    $stmt = $conn->query("SELECT g.*, COUNT(DISTINCT m.contact_id) as contact_count 
                         FROM contact_groups g 
                         LEFT JOIN contact_group_members m ON g.id = m.group_id 
                         GROUP BY g.id 
                         ORDER BY g.name");
    $groups = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = "Error fetching groups: " . $e->getMessage();
}

// Get all contacts not in selected group (for add contacts modal)
$available_contacts = [];
if (isset($_GET['group_id'])) {
    try {
        $stmt = $conn->prepare("SELECT c.* FROM contacts c 
                              WHERE c.id NOT IN (
                                  SELECT contact_id FROM contact_group_members WHERE group_id = ?
                              ) 
                              ORDER BY c.name");
        $stmt->execute([$_GET['group_id']]);
        $available_contacts = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = "Error fetching available contacts: " . $e->getMessage();
    }
}

// Set page variables
$page_title = __('contact_groups');
$page_header = __('contact_groups');
$page_description = __('manage_contact_groups_description');

// Include header
include 'includes/header.php';

// Display messages
if (!empty($message)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

if (!empty($error)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}
?>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-folder me-2"></i><?php _e('contact_groups'); ?>
                </h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createGroupModal">
                    <i class="bi bi-plus-circle me-1"></i>Create New Group
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 50px;">#</th>
                                <th>Group Name</th>
                                <th>Description</th>
                                <th>Contacts</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($groups as $index => $group): ?>
                            <tr>
                                <td><?php echo $index + 1; ?></td>
                                <td><?php echo htmlspecialchars($group['name']); ?></td>
                                <td><?php echo htmlspecialchars($group['description'] ?? ''); ?></td>
                                <td>
                                    <span class="badge bg-primary"><?php echo $group['contact_count']; ?> contacts</span>
                                </td>
                                <td><?php echo date('M j, Y', strtotime($group['created_at'])); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-info view-members" 
                                            data-id="<?php echo $group['id']; ?>"
                                            data-name="<?php echo htmlspecialchars($group['name']); ?>">
                                        <i class="bi bi-people"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-success upload-contacts" 
                                            data-id="<?php echo $group['id']; ?>"
                                            data-name="<?php echo htmlspecialchars($group['name']); ?>">
                                        <i class="bi bi-upload"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-primary add-contacts" 
                                            data-id="<?php echo $group['id']; ?>"
                                            data-name="<?php echo htmlspecialchars($group['name']); ?>">
                                        <i class="bi bi-person-plus"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger delete-group"
                                            data-id="<?php echo $group['id']; ?>">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Group Modal -->
<div class="modal fade" id="createGroupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php _e('create_new_group'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createGroupForm">
                    <div class="mb-3">
                        <label for="group_name" class="form-label">Group Name</label>
                        <input type="text" class="form-control" id="group_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="group_description" class="form-label">Description</label>
                        <textarea class="form-control" id="group_description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveGroup">Create Group</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container for Notifications -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1080;">
    <div id="notificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="bi bi-info-circle me-2"></i>
            <strong class="me-auto" id="toastTitle">Notification</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastMessage"></div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php _e('confirm_action'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirmationMessage">Are you sure you want to perform this action?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmActionBtn">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Upload Contacts Modal -->
<div class="modal fade" id="uploadContactsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo sprintf(__('upload_contacts_to_group'), '<span class="group-name"></span>'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="post" enctype="multipart/form-data" id="uploadContactsForm">
                    <input type="hidden" name="group_id" id="upload_group_id">
                    <div class="mb-3">
                        <label for="contact_file" class="form-label">Upload Contact File</label>
                        <input type="file" class="form-control" id="contact_file" name="contact_file" accept=".csv,.txt" required>
                        <div class="form-text">
                            Upload a CSV or TXT file with contacts in the format: <code>email|name</code><br>
                            If name is not provided, it will be generated from the email address.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Upload Contacts</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- View/Edit Members Modal -->
<div class="modal fade" id="viewMembersModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <div>
                    <h5 class="modal-title"><?php echo sprintf(__('members_of_group'), '<span class="group-name"></span>'); ?></h5>
                    <small class="text-muted members-count"></small>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <div class="d-flex justify-content-end mb-3">
                        <select class="form-select form-select-sm w-auto" id="memberLimitSelect">
                            <option value="10">10 per page</option>
                            <option value="25">25 per page</option>
                            <option value="50">50 per page</option>
                        </select>
                    </div>
                    <table class="table" id="groupMembersTable">
                        <thead>
                            <tr>
                                <th style="width: 50px;">#</th>
                                <th>
                                    <a href="#" class="text-decoration-none text-dark sort-link" data-sort="name">
                                        Name <i class="bi bi-arrow-down-up"></i>
                                    </a>
                                </th>
                                <th>
                                    <a href="#" class="text-decoration-none text-dark sort-link" data-sort="email">
                                        Email <i class="bi bi-arrow-down-up"></i>
                                    </a>
                                </th>
                                <th>
                                    <a href="#" class="text-decoration-none text-dark sort-link" data-sort="created_at">
                                        Added Date <i class="bi bi-arrow-down-up"></i>
                                    </a>
                                </th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Will be populated via AJAX -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Members Pagination -->
                <div class="members-pagination mt-3">
                    <!-- Will be populated via JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Contacts Modal -->
<div class="modal fade" id="addContactsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo sprintf(__('add_contacts_to_group'), '<span class="group-name"></span>'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary me-1" id="selectAllAvailableBtn">
                            Select All
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary me-1 select-first-n" data-count="50">
                            Select 50
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary me-1 select-first-n" data-count="100">
                            Select 100
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary me-1 select-first-n" data-count="250">
                            Select 250
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" id="clearAllSelections">
                            Clear
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table" id="availableContactsTable">
                        <thead>
                            <tr>
                                <th style="width: 50px;">
                                    <input type="checkbox" class="form-check-input" id="selectAllAvailableContacts">
                                </th>
                                <th style="width: 50px;">#</th>
                                <th>
                                    <a href="#" class="text-decoration-none text-dark sort-available-link" data-sort="name">
                                        Name <i class="bi bi-arrow-down-up"></i>
                                    </a>
                                </th>
                                <th>
                                    <a href="#" class="text-decoration-none text-dark sort-available-link" data-sort="email">
                                        Email <i class="bi bi-arrow-down-up"></i>
                                    </a>
                                </th>
                                <th>
                                    <a href="#" class="text-decoration-none text-dark sort-available-link" data-sort="created_at">
                                        Added Date <i class="bi bi-arrow-down-up"></i>
                                    </a>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Will be populated via AJAX -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination controls -->
                <div class="mt-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="pagination-info">
                            <!-- Will show pagination info via JavaScript -->
                        </div>
                        <div>
                            <select class="form-select form-select-sm" id="availableContactsLimit">
                                <option value="20">20 per page</option>
                                <option value="40" selected>40 per page</option>
                                <option value="60">60 per page</option>
                                <option value="100">100 per page</option>
                            </select>
                        </div>
                    </div>
                    <div class="available-contacts-pagination mt-2">
                        <!-- Will be populated via JavaScript -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="addSelectedContacts">Add Selected Contacts</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>

<!-- Error handler to catch jQuery errors -->
<script>
window.addEventListener('error', function(event) {
    if (event.error && event.error.message && event.error.message.includes('$ is not defined')) {
        console.error('jQuery not loaded properly. This might be due to script loading order or CSP issues.');
        // Try to re-load jQuery
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js';
        document.head.appendChild(script);
    }
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Notification function
    function showNotification(message, type = 'success') {
        const toast = document.getElementById('notificationToast');
        const toastInstance = new bootstrap.Toast(toast);
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');
        
        // Set toast appearance based on type
        toast.classList.remove('text-bg-success', 'text-bg-danger', 'text-bg-warning', 'text-bg-info');
        
        switch(type) {
            case 'success':
                toast.classList.add('text-bg-success');
                toastTitle.textContent = 'Success';
                break;
            case 'error':
                toast.classList.add('text-bg-danger');
                toastTitle.textContent = 'Error';
                break;
            case 'warning':
                toast.classList.add('text-bg-warning');
                toastTitle.textContent = 'Warning';
                break;
            default:
                toast.classList.add('text-bg-info');
                toastTitle.textContent = 'Information';
        }
        
        // Set message
        toastMessage.textContent = message;
        
        // Show toast
        toastInstance.show();
    }
    
    // Create Group
    document.getElementById('saveGroup').addEventListener('click', function() {
        const name = document.getElementById('group_name').value;
        const description = document.getElementById('group_description').value;
        
        fetch('ajax/create_group.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: name,
                description: description
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Group created successfully!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(data.error || 'Error creating group', 'error');
            }
        });
    });
    
    // Upload Contacts to Group
    document.querySelectorAll('.upload-contacts').forEach(button => {
        button.addEventListener('click', function() {
            const groupId = this.dataset.id;
            const groupName = this.dataset.name;
            
            document.getElementById('upload_group_id').value = groupId;
            document.querySelectorAll('.group-name').forEach(span => {
                span.textContent = groupName;
            });
            
            new bootstrap.Modal(document.getElementById('uploadContactsModal')).show();
        });
    });
    
    let currentGroupId = null;
    let currentPage = 1;
    let currentSort = 'name';
    let currentOrder = 'ASC';
    let currentLimit = 10;
    
    // View Group Members
    document.querySelectorAll('.view-members').forEach(button => {
        button.addEventListener('click', function() {
            currentGroupId = this.dataset.id;
            const groupName = this.dataset.name;
            
            document.querySelectorAll('.group-name').forEach(span => {
                span.textContent = groupName;
            });
            
            loadGroupMembers();
            new bootstrap.Modal(document.getElementById('viewMembersModal')).show();
        });
    });
    
    // Handle sort clicks
    document.querySelectorAll('.sort-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const sortField = this.dataset.sort;
            
            if (currentSort === sortField) {
                currentOrder = currentOrder === 'ASC' ? 'DESC' : 'ASC';
            } else {
                currentSort = sortField;
                currentOrder = 'ASC';
            }
            
            loadGroupMembers();
        });
    });
    
    // Handle items per page change
    document.getElementById('memberLimitSelect').addEventListener('change', function() {
        currentLimit = parseInt(this.value);
        currentPage = 1; // Reset to first page
        loadGroupMembers();
    });
    
    function loadGroupMembers() {
        fetch(`ajax/get_group_members.php?group_id=${currentGroupId}&page=${currentPage}&sort=${currentSort}&order=${currentOrder}&limit=${currentLimit}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const tbody = document.querySelector('#groupMembersTable tbody');
                    tbody.innerHTML = '';
                    
                    // Update members count
                    document.querySelector('.members-count').textContent = 
                        `Showing ${data.pagination.start} to ${data.pagination.end} of ${data.pagination.total} members`;
                    
                    // Update sort indicators
                    document.querySelectorAll('.sort-link').forEach(link => {
                        const icon = link.querySelector('i');
                        if (link.dataset.sort === currentSort) {
                            icon.className = `bi bi-arrow-${currentOrder === 'ASC' ? 'up' : 'down'}`;
                        } else {
                            icon.className = 'bi bi-arrow-down-up';
                        }
                    });
                    
                    // Populate table
                    data.members.forEach((member, index) => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td>${data.pagination.start + index}</td>
                            <td>${member.name}</td>
                            <td>${member.email}</td>
                            <td>${new Date(member.created_at).toLocaleDateString()}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger remove-from-group"
                                        data-group-id="${currentGroupId}" data-contact-id="${member.id}">
                                    <i class="bi bi-person-x"></i>
                                </button>
                            </td>
                        `;
                        tbody.appendChild(tr);
                    });
                    
                    // Update pagination
                    updateMembersPagination(data.pagination);
                }
            });
    }
    
    function updateMembersPagination(pagination) {
        const container = document.querySelector('.members-pagination');
        if (pagination.pages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let html = `
            <nav aria-label="Group members pagination">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="pagination-info">
                        Page ${pagination.current} of ${pagination.pages}
                    </div>
                    <ul class="pagination mb-0">
                        <li class="page-item ${pagination.current <= 1 ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="1">
                                <i class="bi bi-chevron-double-left"></i>
                            </a>
                        </li>
                        <li class="page-item ${pagination.current <= 1 ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${pagination.current - 1}">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>`;
        
        // Calculate page range
        const start = Math.max(1, Math.min(pagination.current - 2, pagination.pages - 4));
        const end = Math.min(pagination.pages, Math.max(5, pagination.current + 2));
        
        for (let i = start; i <= end; i++) {
            html += `
                <li class="page-item ${pagination.current == i ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>`;
        }
        
        html += `
                        <li class="page-item ${pagination.current >= pagination.pages ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${pagination.current + 1}">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                        <li class="page-item ${pagination.current >= pagination.pages ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${pagination.pages}">
                                <i class="bi bi-chevron-double-right"></i>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>`;
        
        container.innerHTML = html;
        
        // Add click handlers to pagination links
        container.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                if (!this.parentElement.classList.contains('disabled')) {
                    currentPage = parseInt(this.dataset.page);
                    loadGroupMembers();
                }
            });
        });
    }
    
    // Add Contacts to Group
    let currentAvailablePage = 1;
    let currentAvailableSort = 'name';
    let currentAvailableOrder = 'ASC';
    let currentAvailableLimit = 40;
    let currentAddGroupId = null;
    
    document.querySelectorAll('.add-contacts').forEach(button => {
        button.addEventListener('click', function() {
            currentAddGroupId = this.dataset.id;
            const groupName = this.dataset.name;
            
            document.querySelectorAll('.group-name').forEach(span => {
                span.textContent = groupName;
            });
            
            loadAvailableContacts();
        });
    });
    
    // Variables to store all available contacts
    let allAvailableContacts = [];
    
    // Handle sort clicks for available contacts
    document.querySelectorAll('.sort-available-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const sortField = this.dataset.sort;
            
            if (currentAvailableSort === sortField) {
                currentAvailableOrder = currentAvailableOrder === 'ASC' ? 'DESC' : 'ASC';
            } else {
                currentAvailableSort = sortField;
                currentAvailableOrder = 'ASC';
            }
            
            loadAvailableContacts();
        });
    });
    
    // Handle items per page change for available contacts
    document.getElementById('availableContactsLimit').addEventListener('change', function() {
        currentAvailableLimit = parseInt(this.value);
        currentAvailablePage = 1; // Reset to first page
        loadAvailableContacts();
    });
    
    // Select All Available Contacts (header checkbox)
    document.getElementById('selectAllAvailableContacts').addEventListener('change', function() {
        // Check all visible checkboxes
        document.querySelectorAll('.available-contact-checkbox').forEach(cb => {
            cb.checked = this.checked;
        });
        
        if (this.checked) {
            // Get IDs of ALL available contacts for bulk operation
            const allIds = allAvailableContacts.map(contact => contact.id.toString());
            
            // Store the selected IDs for use when adding contacts
            window.selectedContactIds = allIds;
            
            showNotification(`Selected all ${allAvailableContacts.length} contacts across all pages`, 'info');
        } else {
            // Clear selection
            window.selectedContactIds = [];
        }
    });
    
    // Handle individual checkbox changes
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('available-contact-checkbox')) {
            // When an individual checkbox changes, update the stored selection
            const contactId = e.target.value;
            
            // Initialize selectedContactIds if not exists
            if (!window.selectedContactIds) {
                window.selectedContactIds = [];
            }
            
            if (e.target.checked) {
                // Add to selection if not already there
                if (!window.selectedContactIds.includes(contactId)) {
                    window.selectedContactIds.push(contactId);
                }
            } else {
                // Remove from selection
                window.selectedContactIds = window.selectedContactIds.filter(id => id !== contactId);
            }
            
            // Update the "Select All" header checkbox based on visible checkboxes
            const allVisible = document.querySelectorAll('.available-contact-checkbox');
            const allChecked = document.querySelectorAll('.available-contact-checkbox:checked');
            document.getElementById('selectAllAvailableContacts').checked = 
                allVisible.length > 0 && allVisible.length === allChecked.length;
        }
    });
    
    function loadAvailableContacts() {
        // Request both paginated and all contacts
        const url = `ajax/get_available_contacts.php?group_id=${currentAddGroupId}&page=${currentAvailablePage}&sort=${currentAvailableSort}&order=${currentAvailableOrder}&limit=${currentAvailableLimit}&get_all=true`;
        
        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const tbody = document.querySelector('#availableContactsTable tbody');
                    tbody.innerHTML = '';
                    
                    // Store all contacts for bulk operations
                    allAvailableContacts = data.all_contacts || [];
                    
                    // Update pagination info
                    document.querySelector('.pagination-info').textContent = 
                        `Showing ${data.pagination.start} to ${data.pagination.end} of ${data.pagination.total} available contacts`;
                    
                    // Update sort indicators
                    document.querySelectorAll('.sort-available-link').forEach(link => {
                        const icon = link.querySelector('i');
                        if (link.dataset.sort === currentAvailableSort) {
                            icon.className = `bi bi-arrow-${currentAvailableOrder === 'ASC' ? 'up' : 'down'}`;
                        } else {
                            icon.className = 'bi bi-arrow-down-up';
                        }
                    });
                    
                    // Populate table
                    data.contacts.forEach((contact, index) => {
                        // Check if this contact is in the stored selection
                        const isSelected = window.selectedContactIds && 
                                         window.selectedContactIds.includes(contact.id.toString());
                        
                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td>
                                <input type="checkbox" class="form-check-input available-contact-checkbox" 
                                       value="${contact.id}" ${isSelected ? 'checked' : ''}>
                            </td>
                            <td>${data.pagination.start + index}</td>
                            <td>${contact.name}</td>
                            <td>${contact.email}</td>
                            <td>${new Date(contact.created_at).toLocaleDateString()}</td>
                        `;
                        tbody.appendChild(tr);
                    });
                    
                    // Update pagination
                    updateAvailableContactsPagination(data.pagination);
                    
                    // Show the modal after everything is loaded
                    new bootstrap.Modal(document.getElementById('addContactsModal')).show();
                } else {
                    showNotification(data.error || 'Error loading available contacts', 'error');
                }
            })
            .catch(error => {
                console.error('Error loading available contacts:', error);
                showNotification('Error loading available contacts. Please try again.', 'error');
            });
    }
    
    function updateAvailableContactsPagination(pagination) {
        const container = document.querySelector('.available-contacts-pagination');
        if (pagination.pages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let html = `
            <nav aria-label="Available contacts pagination">
                <ul class="pagination justify-content-center mb-0">
                    <li class="page-item ${pagination.current <= 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="1">
                            <i class="bi bi-chevron-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item ${pagination.current <= 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${pagination.current - 1}">
                            <i class="bi bi-chevron-left"></i>
                        </a>
                    </li>`;
        
        // Calculate visible page numbers
        const start_page = Math.max(1, Math.min(pagination.current - 2, pagination.pages - 4));
        const end_page = Math.min(pagination.pages, Math.max(5, pagination.current + 2));
        
        for (let i = start_page; i <= end_page; i++) {
            html += `
                <li class="page-item ${pagination.current == i ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>`;
        }
        
        html += `
                    <li class="page-item ${pagination.current >= pagination.pages ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${pagination.current + 1}">
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                    <li class="page-item ${pagination.current >= pagination.pages ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${pagination.pages}">
                            <i class="bi bi-chevron-double-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>`;
        
        container.innerHTML = html;
        
        // Add event listeners to pagination links
        container.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                if (!this.parentNode.classList.contains('disabled')) {
                    currentAvailablePage = parseInt(this.dataset.page);
                    loadAvailableContacts();
                }
            });
        });
    }
    
    // Bulk selection buttons
    document.getElementById('selectAllAvailableBtn').addEventListener('click', function() {
        // Get all displayed checkboxes
        const checkboxes = document.querySelectorAll('.available-contact-checkbox');
        
        // Check all displayed checkboxes
        checkboxes.forEach(cb => {
            cb.checked = true;
        });
        
        // Update header checkbox
        document.getElementById('selectAllAvailableContacts').checked = true;
        
        // Get IDs of ALL available contacts for bulk operation
        const allIds = allAvailableContacts.map(contact => contact.id.toString());
        
        // Store the selected IDs for use when adding contacts
        window.selectedContactIds = allIds;
        
        showNotification(`Selected all ${allAvailableContacts.length} contacts (${checkboxes.length} visible on this page)`, 'info');
    });
    
    document.getElementById('clearAllSelections').addEventListener('click', function() {
        // Uncheck all visible checkboxes
        document.querySelectorAll('.available-contact-checkbox').forEach(cb => {
            cb.checked = false;
        });
        
        // Uncheck header checkbox
        document.getElementById('selectAllAvailableContacts').checked = false;
        
        // Clear stored selection
        window.selectedContactIds = [];
        
        showNotification('Cleared all selections', 'info');
    });
    
    // Select first N contacts
    document.querySelectorAll('.select-first-n').forEach(button => {
        button.addEventListener('click', function() {
            const count = parseInt(this.dataset.count);
            
            if (count > allAvailableContacts.length) {
                showNotification(`Only ${allAvailableContacts.length} contacts available (less than ${count})`, 'warning');
            }
            
            // First clear all visible checkboxes
            document.querySelectorAll('.available-contact-checkbox').forEach(cb => {
                cb.checked = false;
            });
            
            // Get IDs of the first N contacts
            const idsToSelect = allAvailableContacts
                .slice(0, Math.min(count, allAvailableContacts.length))
                .map(contact => contact.id.toString());
            
            // Check visible checkboxes that match the IDs
            document.querySelectorAll('.available-contact-checkbox').forEach(cb => {
                if (idsToSelect.includes(cb.value)) {
                    cb.checked = true;
                }
            });
            
            // Update header checkbox based on visible selections
            const checkboxes = document.querySelectorAll('.available-contact-checkbox');
            const checkedCount = document.querySelectorAll('.available-contact-checkbox:checked').length;
            document.getElementById('selectAllAvailableContacts').checked = 
                checkboxes.length > 0 && checkedCount === checkboxes.length;
                
            // Show information about selection across pages
            if (idsToSelect.length > 0) {
                if (checkedCount < idsToSelect.length) {
                    showNotification(`Selected ${idsToSelect.length} contacts, but only ${checkedCount} are visible on this page. All selected contacts will be added when you click "Add Selected Contacts"`, 'info');
                } else {
                    showNotification(`Selected ${idsToSelect.length} contacts`, 'info');
                }
            } else {
                showNotification('No contacts to select', 'warning');
            }
            
            // Store the selected IDs for use when adding contacts
            window.selectedContactIds = idsToSelect;
        });
    });
    
    // Add Selected Contacts to Group
    document.getElementById('addSelectedContacts').addEventListener('click', function() {
        // Get selection from the stored IDs for bulk operations if available
        let selectedContacts = window.selectedContactIds || [];
        
        // If nothing was pre-selected, get from the checkboxes
        if (!selectedContacts.length) {
            selectedContacts = Array.from(document.querySelectorAll('.available-contact-checkbox:checked'))
                                      .map(cb => cb.value);
        }
        
        if (selectedContacts.length === 0) {
            showNotification('Please select at least one contact to add.', 'warning');
            return;
        }
        
        // Close the contacts modal first
        const addContactsModal = bootstrap.Modal.getInstance(document.getElementById('addContactsModal'));
        if (addContactsModal) {
            addContactsModal.hide();
        }
        
        // Show confirmation with count
        document.getElementById('confirmationMessage').textContent = 
            `Are you sure you want to add ${selectedContacts.length} contacts to this group?`;
        
        // Set up confirmation action
        const confirmBtn = document.getElementById('confirmActionBtn');
        const confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'));
        
        // Remove any existing event listener
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        // Add new event listener for adding contacts
        newConfirmBtn.addEventListener('click', function() {
            confirmationModal.hide();
            
            fetch('ajax/add_contacts_to_group.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    group_id: currentAddGroupId,
                    contact_ids: selectedContacts
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(`Successfully added ${selectedContacts.length} contacts to group`, 'success');
                    // Clear the selection
                    window.selectedContactIds = [];
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(data.error || 'Error adding contacts to group', 'error');
                }
            });
        });
        
        confirmationModal.show();
    });
    
    // Remove Contact from Group
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-from-group')) {
            const button = e.target.closest('.remove-from-group');
            const groupId = button.dataset.groupId;
            const contactId = button.dataset.contactId;
            
            // Set confirmation message
            document.getElementById('confirmationMessage').textContent = 
                'Are you sure you want to remove this contact from the group?';
            
            // Set up confirmation action
            const confirmBtn = document.getElementById('confirmActionBtn');
            const confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'));
            
            // Remove any existing event listener
            const newConfirmBtn = confirmBtn.cloneNode(true);
            confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
            
            // Add new event listener
            newConfirmBtn.addEventListener('click', function() {
                confirmationModal.hide();
                
                fetch('ajax/remove_from_group.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        group_id: groupId,
                        contact_id: contactId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Contact removed from group successfully', 'success');
                        // Wait a moment for the notification to show before reloading members
                        setTimeout(() => loadGroupMembers(), 1000);
                    } else {
                        showNotification(data.error || 'Error removing contact from group', 'error');
                    }
                });
            });
            
            confirmationModal.show();
        }
    });
    
    // Delete Group
    document.querySelectorAll('.delete-group').forEach(button => {
        button.addEventListener('click', function() {
            const groupId = this.dataset.id;
            
            // Set confirmation message
            document.getElementById('confirmationMessage').textContent = 
                'Are you sure you want to delete this group? This will not delete the contacts.';
            
            // Set up confirmation action
            const confirmBtn = document.getElementById('confirmActionBtn');
            const confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'));
            
            // Remove any existing event listener
            const newConfirmBtn = confirmBtn.cloneNode(true);
            confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
            
            // Add new event listener
            newConfirmBtn.addEventListener('click', function() {
                confirmationModal.hide();
                
                fetch('ajax/delete_group.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: groupId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Group deleted successfully', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showNotification(data.error || 'Error deleting group', 'error');
                    }
                });
            });
            
            confirmationModal.show();
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?> 