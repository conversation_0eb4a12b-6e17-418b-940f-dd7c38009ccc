Throw expression
-----
<?php
test(throw $x);
$a ?? throw new Exception;
-----
array(
    0: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: test
            )
            args: array(
                0: Arg(
                    name: null
                    value: Expr_Throw(
                        expr: Expr_Variable(
                            name: x
                        )
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_BinaryOp_Coalesce(
            left: Expr_Variable(
                name: a
            )
            right: Expr_Throw(
                expr: Expr_New(
                    class: Name(
                        name: Exception
                    )
                    args: array(
                    )
                )
            )
        )
    )
)
