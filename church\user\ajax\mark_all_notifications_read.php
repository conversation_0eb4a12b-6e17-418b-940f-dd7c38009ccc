<?php
/**
 * Mark All Notifications as Read AJAX Endpoint
 * 
 * Marks all notifications as read for the current user
 */

session_start();
require_once '../../config.php';
require_once '../../includes/notification_functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit;
}

$userId = $_SESSION['user_id'];

try {
    $success = markAllNotificationsAsRead($pdo, $userId);
    
    echo json_encode([
        'success' => $success,
        'message' => $success ? 'All notifications marked as read' : 'Failed to mark notifications as read'
    ]);
    
} catch (Exception $e) {
    error_log("Error marking all notifications as read: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to mark all notifications as read'
    ]);
}
?>
