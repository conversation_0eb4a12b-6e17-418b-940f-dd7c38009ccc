
name: "Performance benchmark"

on:
  pull_request:
    branches:
      - "*.x"
  push:
    branches:
      - "*.x"

env:
  fail-fast: true
  COMPOSER_ROOT_VERSION: "1.4"

jobs:
  phpbench:
    name: "PHPBench"
    runs-on: "ubuntu-22.04"

    strategy:
      matrix:
        php-version:
          - "8.1"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"
        with:
          fetch-depth: 2

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php-version }}"
          coverage: "pcov"
          ini-values: "zend.assertions=1"

      - name: "Cache dependencies installed with composer"
        uses: "actions/cache@v3"
        with:
          path: "~/.composer/cache"
          key: "php-${{ matrix.php-version }}-composer-locked-${{ hashFiles('composer.lock') }}"
          restore-keys: "php-${{ matrix.php-version }}-composer-locked-"

      - name: "Install dependencies with composer"
        run: "composer update --no-interaction --no-progress"

      - name: "Run PHPBench"
        run: "php ./vendor/bin/phpbench run --iterations=3 --warmup=1 --report=aggregate"
