Nullable types
-----
<?php

function test(?Foo $bar, ?string $foo) : ?Baz {
}
-----
array(
    0: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: test
        )
        params: array(
            0: Param(
                attrGroups: array(
                )
                flags: 0
                type: NullableType(
                    type: Name(
                        name: Foo
                    )
                )
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: bar
                )
                default: null
                hooks: array(
                )
            )
            1: Param(
                attrGroups: array(
                )
                flags: 0
                type: NullableType(
                    type: Identifier(
                        name: string
                    )
                )
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: foo
                )
                default: null
                hooks: array(
                )
            )
        )
        returnType: NullableType(
            type: Name(
                name: Baz
            )
        )
        stmts: array(
        )
    )
)
