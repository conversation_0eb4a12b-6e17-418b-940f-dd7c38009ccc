Anonymous classes
-----
<?php

new class {
    public function test() {}
};
new class extends A implements B, C {};
new class() {
    public $foo;
};
new class($a, $b) extends A {
    use T;
};

class A {
    public function test() {
        return new class($this) extends A {
            const A = 'B';
        };
    }
}
-----
array(
    0: Stmt_Expression(
        expr: Expr_New(
            class: Stmt_Class(
                attrGroups: array(
                )
                flags: 0
                name: null
                extends: null
                implements: array(
                )
                stmts: array(
                    0: Stmt_ClassMethod(
                        attrGroups: array(
                        )
                        flags: PUBLIC (1)
                        byRef: false
                        name: Identifier(
                            name: test
                        )
                        params: array(
                        )
                        returnType: null
                        stmts: array(
                        )
                    )
                )
            )
            args: array(
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_New(
            class: Stmt_Class(
                attrGroups: array(
                )
                flags: 0
                name: null
                extends: Name(
                    name: A
                )
                implements: array(
                    0: Name(
                        name: B
                    )
                    1: Name(
                        name: C
                    )
                )
                stmts: array(
                )
            )
            args: array(
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_New(
            class: Stmt_Class(
                attrGroups: array(
                )
                flags: 0
                name: null
                extends: null
                implements: array(
                )
                stmts: array(
                    0: Stmt_Property(
                        attrGroups: array(
                        )
                        flags: PUBLIC (1)
                        type: null
                        props: array(
                            0: PropertyItem(
                                name: VarLikeIdentifier(
                                    name: foo
                                )
                                default: null
                            )
                        )
                        hooks: array(
                        )
                    )
                )
            )
            args: array(
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_New(
            class: Stmt_Class(
                attrGroups: array(
                )
                flags: 0
                name: null
                extends: Name(
                    name: A
                )
                implements: array(
                )
                stmts: array(
                    0: Stmt_TraitUse(
                        traits: array(
                            0: Name(
                                name: T
                            )
                        )
                        adaptations: array(
                        )
                    )
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Expr_Variable(
                        name: a
                    )
                    byRef: false
                    unpack: false
                )
                1: Arg(
                    name: null
                    value: Expr_Variable(
                        name: b
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    4: Stmt_Class(
        attrGroups: array(
        )
        flags: 0
        name: Identifier(
            name: A
        )
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_ClassMethod(
                attrGroups: array(
                )
                flags: PUBLIC (1)
                byRef: false
                name: Identifier(
                    name: test
                )
                params: array(
                )
                returnType: null
                stmts: array(
                    0: Stmt_Return(
                        expr: Expr_New(
                            class: Stmt_Class(
                                attrGroups: array(
                                )
                                flags: 0
                                name: null
                                extends: Name(
                                    name: A
                                )
                                implements: array(
                                )
                                stmts: array(
                                    0: Stmt_ClassConst(
                                        attrGroups: array(
                                        )
                                        flags: 0
                                        type: null
                                        consts: array(
                                            0: Const(
                                                name: Identifier(
                                                    name: A
                                                )
                                                value: Scalar_String(
                                                    value: B
                                                )
                                            )
                                        )
                                    )
                                )
                            )
                            args: array(
                                0: Arg(
                                    name: null
                                    value: Expr_Variable(
                                        name: this
                                    )
                                    byRef: false
                                    unpack: false
                                )
                            )
                        )
                    )
                )
            )
        )
    )
)
