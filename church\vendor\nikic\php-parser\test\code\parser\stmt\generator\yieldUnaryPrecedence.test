Yield with unary operator argument
-----
<?php

function gen() {
    yield +1;
    yield -1;
    yield * -1;
}
-----
array(
    0: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: gen
        )
        params: array(
        )
        returnType: null
        stmts: array(
            0: Stmt_Expression(
                expr: Expr_Yield(
                    key: null
                    value: Expr_UnaryPlus(
                        expr: Scalar_Int(
                            value: 1
                        )
                    )
                )
            )
            1: Stmt_Expression(
                expr: Expr_Yield(
                    key: null
                    value: Expr_UnaryMinus(
                        expr: Scalar_Int(
                            value: 1
                        )
                    )
                )
            )
            2: Stmt_Expression(
                expr: Expr_BinaryOp_Mul(
                    left: Expr_Yield(
                        key: null
                        value: null
                    )
                    right: Expr_UnaryMinus(
                        expr: Scalar_Int(
                            value: 1
                        )
                    )
                )
            )
        )
    )
)
