UVS indirect calls
-----
<?php

id('var_dump')(1);
id('id')('var_dump')(2);
id()()('var_dump')(4);
id(['udef', 'id'])[1]()('var_dump')(5);
(function($x) { return $x; })('id')('var_dump')(8);
($f = function($x = null) use (&$f) {
    return $x ?: $f;
})()()()('var_dump')(9);
[$obj, 'id']()('id')($id)('var_dump')(10);
'id'()('id')('var_dump')(12);
('i' . 'd')()('var_dump')(13);
'\id'('var_dump')(14);
-----
array(
    0: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Name(
                    name: id
                )
                args: array(
                    0: Arg(
                        name: null
                        value: Scalar_String(
                            value: var_dump
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Scalar_Int(
                        value: 1
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Name(
                        name: id
                    )
                    args: array(
                        0: Arg(
                            name: null
                            value: Scalar_String(
                                value: id
                            )
                            byRef: false
                            unpack: false
                        )
                    )
                )
                args: array(
                    0: Arg(
                        name: null
                        value: Scalar_String(
                            value: var_dump
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Scalar_Int(
                        value: 2
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Expr_FuncCall(
                        name: Name(
                            name: id
                        )
                        args: array(
                        )
                    )
                    args: array(
                    )
                )
                args: array(
                    0: Arg(
                        name: null
                        value: Scalar_String(
                            value: var_dump
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Scalar_Int(
                        value: 4
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Expr_ArrayDimFetch(
                        var: Expr_FuncCall(
                            name: Name(
                                name: id
                            )
                            args: array(
                                0: Arg(
                                    name: null
                                    value: Expr_Array(
                                        items: array(
                                            0: ArrayItem(
                                                key: null
                                                value: Scalar_String(
                                                    value: udef
                                                )
                                                byRef: false
                                                unpack: false
                                            )
                                            1: ArrayItem(
                                                key: null
                                                value: Scalar_String(
                                                    value: id
                                                )
                                                byRef: false
                                                unpack: false
                                            )
                                        )
                                    )
                                    byRef: false
                                    unpack: false
                                )
                            )
                        )
                        dim: Scalar_Int(
                            value: 1
                        )
                    )
                    args: array(
                    )
                )
                args: array(
                    0: Arg(
                        name: null
                        value: Scalar_String(
                            value: var_dump
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Scalar_Int(
                        value: 5
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Expr_Closure(
                        attrGroups: array(
                        )
                        static: false
                        byRef: false
                        params: array(
                            0: Param(
                                attrGroups: array(
                                )
                                flags: 0
                                type: null
                                byRef: false
                                variadic: false
                                var: Expr_Variable(
                                    name: x
                                )
                                default: null
                                hooks: array(
                                )
                            )
                        )
                        uses: array(
                        )
                        returnType: null
                        stmts: array(
                            0: Stmt_Return(
                                expr: Expr_Variable(
                                    name: x
                                )
                            )
                        )
                    )
                    args: array(
                        0: Arg(
                            name: null
                            value: Scalar_String(
                                value: id
                            )
                            byRef: false
                            unpack: false
                        )
                    )
                )
                args: array(
                    0: Arg(
                        name: null
                        value: Scalar_String(
                            value: var_dump
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Scalar_Int(
                        value: 8
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    5: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Expr_FuncCall(
                        name: Expr_FuncCall(
                            name: Expr_Assign(
                                var: Expr_Variable(
                                    name: f
                                )
                                expr: Expr_Closure(
                                    attrGroups: array(
                                    )
                                    static: false
                                    byRef: false
                                    params: array(
                                        0: Param(
                                            attrGroups: array(
                                            )
                                            flags: 0
                                            type: null
                                            byRef: false
                                            variadic: false
                                            var: Expr_Variable(
                                                name: x
                                            )
                                            default: Expr_ConstFetch(
                                                name: Name(
                                                    name: null
                                                )
                                            )
                                            hooks: array(
                                            )
                                        )
                                    )
                                    uses: array(
                                        0: ClosureUse(
                                            var: Expr_Variable(
                                                name: f
                                            )
                                            byRef: true
                                        )
                                    )
                                    returnType: null
                                    stmts: array(
                                        0: Stmt_Return(
                                            expr: Expr_Ternary(
                                                cond: Expr_Variable(
                                                    name: x
                                                )
                                                if: null
                                                else: Expr_Variable(
                                                    name: f
                                                )
                                            )
                                        )
                                    )
                                )
                            )
                            args: array(
                            )
                        )
                        args: array(
                        )
                    )
                    args: array(
                    )
                )
                args: array(
                    0: Arg(
                        name: null
                        value: Scalar_String(
                            value: var_dump
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Scalar_Int(
                        value: 9
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    6: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Expr_FuncCall(
                        name: Expr_FuncCall(
                            name: Expr_Array(
                                items: array(
                                    0: ArrayItem(
                                        key: null
                                        value: Expr_Variable(
                                            name: obj
                                        )
                                        byRef: false
                                        unpack: false
                                    )
                                    1: ArrayItem(
                                        key: null
                                        value: Scalar_String(
                                            value: id
                                        )
                                        byRef: false
                                        unpack: false
                                    )
                                )
                            )
                            args: array(
                            )
                        )
                        args: array(
                            0: Arg(
                                name: null
                                value: Scalar_String(
                                    value: id
                                )
                                byRef: false
                                unpack: false
                            )
                        )
                    )
                    args: array(
                        0: Arg(
                            name: null
                            value: Expr_Variable(
                                name: id
                            )
                            byRef: false
                            unpack: false
                        )
                    )
                )
                args: array(
                    0: Arg(
                        name: null
                        value: Scalar_String(
                            value: var_dump
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Scalar_Int(
                        value: 10
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    7: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Expr_FuncCall(
                        name: Scalar_String(
                            value: id
                        )
                        args: array(
                        )
                    )
                    args: array(
                        0: Arg(
                            name: null
                            value: Scalar_String(
                                value: id
                            )
                            byRef: false
                            unpack: false
                        )
                    )
                )
                args: array(
                    0: Arg(
                        name: null
                        value: Scalar_String(
                            value: var_dump
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Scalar_Int(
                        value: 12
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    8: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Expr_BinaryOp_Concat(
                        left: Scalar_String(
                            value: i
                        )
                        right: Scalar_String(
                            value: d
                        )
                    )
                    args: array(
                    )
                )
                args: array(
                    0: Arg(
                        name: null
                        value: Scalar_String(
                            value: var_dump
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Scalar_Int(
                        value: 13
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    9: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Scalar_String(
                    value: \id
                )
                args: array(
                    0: Arg(
                        name: null
                        value: Scalar_String(
                            value: var_dump
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    name: null
                    value: Scalar_Int(
                        value: 14
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
)
