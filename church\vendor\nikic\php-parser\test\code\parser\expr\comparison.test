Comparison operators
-----
<?php
$a < $b;
$a <= $b;
$a > $b;
$a >= $b;
$a == $b;
$a === $b;
$a != $b;
$a !== $b;
$a <=> $b;
$a instanceof B;
$a instanceof $b;
-----
array(
    0: Stmt_Expression(
        expr: Expr_BinaryOp_Smaller(
            left: Expr_Variable(
                name: a
            )
            right: Expr_Variable(
                name: b
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_BinaryOp_SmallerOrEqual(
            left: Expr_Variable(
                name: a
            )
            right: Expr_Variable(
                name: b
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_BinaryOp_Greater(
            left: Expr_Variable(
                name: a
            )
            right: Expr_Variable(
                name: b
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_BinaryOp_GreaterOrEqual(
            left: Expr_Variable(
                name: a
            )
            right: Expr_Variable(
                name: b
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_BinaryOp_Equal(
            left: Expr_Variable(
                name: a
            )
            right: Expr_Variable(
                name: b
            )
        )
    )
    5: Stmt_Expression(
        expr: Expr_BinaryOp_Identical(
            left: Expr_Variable(
                name: a
            )
            right: Expr_Variable(
                name: b
            )
        )
    )
    6: Stmt_Expression(
        expr: Expr_BinaryOp_NotEqual(
            left: Expr_Variable(
                name: a
            )
            right: Expr_Variable(
                name: b
            )
        )
    )
    7: Stmt_Expression(
        expr: Expr_BinaryOp_NotIdentical(
            left: Expr_Variable(
                name: a
            )
            right: Expr_Variable(
                name: b
            )
        )
    )
    8: Stmt_Expression(
        expr: Expr_BinaryOp_Spaceship(
            left: Expr_Variable(
                name: a
            )
            right: Expr_Variable(
                name: b
            )
        )
    )
    9: Stmt_Expression(
        expr: Expr_Instanceof(
            expr: Expr_Variable(
                name: a
            )
            class: Name(
                name: B
            )
        )
    )
    10: Stmt_Expression(
        expr: Expr_Instanceof(
            expr: Expr_Variable(
                name: a
            )
            class: Expr_Variable(
                name: b
            )
        )
    )
)
