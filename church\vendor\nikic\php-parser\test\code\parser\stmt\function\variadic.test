Variadic functions
-----
<?php
function test($a, ... $b) {}
function test($a, &... $b) {}
function test($a, Type ... $b) {}
function test($a, Type &... $b) {}
-----
array(
    0: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: test
        )
        params: array(
            0: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: a
                )
                default: null
                hooks: array(
                )
            )
            1: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: true
                var: Expr_Variable(
                    name: b
                )
                default: null
                hooks: array(
                )
            )
        )
        returnType: null
        stmts: array(
        )
    )
    1: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: test
        )
        params: array(
            0: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: a
                )
                default: null
                hooks: array(
                )
            )
            1: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: true
                variadic: true
                var: Expr_Variable(
                    name: b
                )
                default: null
                hooks: array(
                )
            )
        )
        returnType: null
        stmts: array(
        )
    )
    2: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: test
        )
        params: array(
            0: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: a
                )
                default: null
                hooks: array(
                )
            )
            1: Param(
                attrGroups: array(
                )
                flags: 0
                type: Name(
                    name: Type
                )
                byRef: false
                variadic: true
                var: Expr_Variable(
                    name: b
                )
                default: null
                hooks: array(
                )
            )
        )
        returnType: null
        stmts: array(
        )
    )
    3: Stmt_Function(
        attrGroups: array(
        )
        byRef: false
        name: Identifier(
            name: test
        )
        params: array(
            0: Param(
                attrGroups: array(
                )
                flags: 0
                type: null
                byRef: false
                variadic: false
                var: Expr_Variable(
                    name: a
                )
                default: null
                hooks: array(
                )
            )
            1: Param(
                attrGroups: array(
                )
                flags: 0
                type: Name(
                    name: Type
                )
                byRef: true
                variadic: true
                var: Expr_Variable(
                    name: b
                )
                default: null
                hooks: array(
                )
            )
        )
        returnType: null
        stmts: array(
        )
    )
)
