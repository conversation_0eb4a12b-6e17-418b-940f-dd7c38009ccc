Adding statement to Class Method containing Nop
-----
<?php
class Foo {
    public function __construct()
    {
        // I'm just a comment
    }
}
-----
$stmts[0]->stmts[0]->stmts[] = new Stmt\Expression(new Node\Expr\Variable('foo'));
-----
<?php
class Foo {
    public function __construct()
    {
        // I'm just a comment
        $foo;
    }
}
-----
<?php
class Foo {
    public function __construct()
    {
        /* I'm just a comment */
    }
}
-----
$stmts[0]->stmts[0]->stmts[] = new Stmt\Expression(new Node\Expr\Variable('foo'));
-----
<?php
class Foo {
    public function __construct()
    {
        /* I'm just a comment */
        $foo;
    }
}
-----
<?php
class Foo {
    public function __construct()
    {
        /* I'm just a comment */
    }
}
-----
$stmts[0]->stmts[0]->stmts[0]->setAttribute('comments', [new Comment("/* I'm a new comment */")]);
-----
<?php
class Foo {
    public function __construct()
    {
        /* I'm a new comment */

    }
}
-----
<?php
class Foo {
    public function __construct()
    {
        // I'm just a comment
    }
}
-----
$stmts[0]->stmts[0]->stmts[0]->setAttribute('comments', [new Comment("// I'm a new comment")]);
-----
<?php
class Foo {
    public function __construct()
    {
        // I'm a new comment

    }
}
