<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get SMS settings
function getSMSSettings($pdo) {
    try {
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM sms_settings");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
        }
        return $result;
    } catch (PDOException $e) {
        return [];
    }
}

$smsSettings = getSMSSettings($pdo);
$smsEnabled = !empty($smsSettings['sms_enabled']) && $smsSettings['sms_enabled'] === '1';

// Function to parse uploaded phone number files
function parsePhoneNumberFile($file) {
    $phoneNumbers = [];
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception("File upload error occurred.");
    }

    // Check file size (max 2MB)
    if ($file['size'] > 2 * 1024 * 1024) {
        throw new Exception("File size too large. Maximum size is 2MB.");
    }

    $tempFile = $file['tmp_name'];

    switch ($extension) {
        case 'csv':
            $phoneNumbers = parseCSVFile($tempFile);
            break;
        case 'txt':
            $phoneNumbers = parseTXTFile($tempFile);
            break;
        case 'xlsx':
            $phoneNumbers = parseXLSXFile($tempFile);
            break;
        default:
            throw new Exception("Unsupported file format. Please upload CSV, TXT, or XLSX files only.");
    }

    return $phoneNumbers;
}

function parseCSVFile($filePath) {
    $phoneNumbers = [];
    if (($handle = fopen($filePath, "r")) !== FALSE) {
        $isFirstRow = true;
        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
            // Skip header row if it looks like headers
            if ($isFirstRow && (stripos($data[0], 'phone') !== false || stripos($data[0], 'number') !== false)) {
                $isFirstRow = false;
                continue;
            }
            $isFirstRow = false;

            // Extract phone number and optional name
            $phone = trim($data[0]);
            $name = isset($data[1]) ? trim($data[1]) : '';

            if (!empty($phone) && validatePhoneNumber($phone)) {
                $phoneNumbers[] = ['phone' => $phone, 'name' => $name];
            }
        }
        fclose($handle);
    }
    return $phoneNumbers;
}

function parseTXTFile($filePath) {
    $phoneNumbers = [];
    $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

    foreach ($lines as $line) {
        $phone = trim($line);
        if (!empty($phone) && validatePhoneNumber($phone)) {
            $phoneNumbers[] = ['phone' => $phone, 'name' => ''];
        }
    }
    return $phoneNumbers;
}

function parseXLSXFile($filePath) {
    // For XLSX support, you would need a library like PhpSpreadsheet
    // For now, we'll provide a basic implementation that suggests CSV conversion
    throw new Exception("XLSX support requires additional libraries. Please convert your file to CSV format and try again.");
}

function validatePhoneNumber($phone) {
    // Remove common formatting characters
    $cleanPhone = preg_replace('/[^\d+]/', '', $phone);

    // Basic validation: should be at least 10 digits
    return strlen($cleanPhone) >= 10 && strlen($cleanPhone) <= 15;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_sms'])) {
    if (!$smsEnabled) {
        $error = "SMS service is not enabled. Please configure SMS settings first.";
    } else {
        try {
            $recipients = $_POST['recipients'] ?? '';
            $message = trim($_POST['message']);
            $templateId = intval($_POST['template_id'] ?? 0);
            $scheduleDate = $_POST['schedule_date'] ?? '';
            $scheduleTime = $_POST['schedule_time'] ?? '';
            $uploadedPhones = [];

            // Handle file upload if provided
            if (isset($_FILES['phone_file']) && $_FILES['phone_file']['error'] !== UPLOAD_ERR_NO_FILE) {
                $uploadedPhones = parsePhoneNumberFile($_FILES['phone_file']);
                if (empty($uploadedPhones)) {
                    throw new Exception("No valid phone numbers found in the uploaded file.");
                }
            }

            if (empty($recipients) && empty($uploadedPhones)) {
                throw new Exception("Please select recipients or upload a phone number file.");
            }

            if (empty($message)) {
                throw new Exception("Please enter a message.");
            }
            
            // Validate message length (SMS limit is typically 160 characters)
            if (strlen($message) > 160) {
                $warning = "Message is " . strlen($message) . " characters. It may be sent as multiple SMS messages.";
            }
            
            // Get recipient list
            $recipientList = [];

            // Add recipients from dropdown selection
            if (!empty($recipients)) {
                if ($recipients === 'all_members') {
                    $stmt = $pdo->prepare("SELECT id, full_name, phone_number as phone FROM members WHERE status = 'active' AND phone_number IS NOT NULL AND phone_number != ''");
                    $stmt->execute();
                    $recipientList = $stmt->fetchAll(PDO::FETCH_ASSOC);
                } elseif ($recipients === 'all_contacts') {
                    $stmt = $pdo->prepare("SELECT id, name as full_name, phone_number as phone FROM contacts WHERE status = 'active' AND phone_number IS NOT NULL AND phone_number != ''");
                    $stmt->execute();
                    $recipientList = $stmt->fetchAll(PDO::FETCH_ASSOC);
                } elseif ($recipients === 'birthday_today') {
                    $stmt = $pdo->prepare("SELECT id, full_name, phone_number as phone FROM members WHERE status = 'active' AND phone_number IS NOT NULL AND phone_number != '' AND (DATE_FORMAT(date_of_birth, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d') OR DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d'))");
                    $stmt->execute();
                    $recipientList = $stmt->fetchAll(PDO::FETCH_ASSOC);
                } elseif (strpos($recipients, 'group_') === 0) {
                    $groupId = intval(str_replace('group_', '', $recipients));
                    $stmt = $pdo->prepare("SELECT m.id, m.full_name, m.phone_number as phone FROM members m JOIN contact_group_members cgm ON m.id = cgm.member_id WHERE cgm.group_id = ? AND m.status = 'active' AND m.phone_number IS NOT NULL AND m.phone_number != ''");
                    $stmt->execute([$groupId]);
                    $recipientList = $stmt->fetchAll(PDO::FETCH_ASSOC);
                } elseif (strpos($recipients, 'contact_group_') === 0) {
                    $groupId = intval(str_replace('contact_group_', '', $recipients));
                    $stmt = $pdo->prepare("SELECT c.id, c.name as full_name, c.phone_number as phone FROM contacts c JOIN contact_group_members cgm ON c.id = cgm.contact_id WHERE cgm.group_id = ? AND c.status = 'active' AND c.phone_number IS NOT NULL AND c.phone_number != ''");
                    $stmt->execute([$groupId]);
                    $recipientList = $stmt->fetchAll(PDO::FETCH_ASSOC);
                }
            }

            // Add recipients from uploaded file
            if (!empty($uploadedPhones)) {
                foreach ($uploadedPhones as $index => $phoneData) {
                    $recipientList[] = [
                        'id' => 'upload_' . $index, // Unique ID for uploaded numbers
                        'full_name' => $phoneData['name'] ?: 'Uploaded Contact',
                        'phone' => $phoneData['phone']
                    ];
                }
            }
            
            if (empty($recipientList)) {
                throw new Exception("No valid recipients found with phone numbers.");
            }
            
            // Schedule or send immediately
            $scheduledAt = null;
            if (!empty($scheduleDate) && !empty($scheduleTime)) {
                $scheduledAt = $scheduleDate . ' ' . $scheduleTime;
                if (strtotime($scheduledAt) <= time()) {
                    throw new Exception("Scheduled time must be in the future.");
                }
            }
            
            // Create SMS campaign
            $stmt = $pdo->prepare("INSERT INTO sms_campaigns (campaign_name, message, template_id, total_recipients, status, scheduled_at, created_by, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
            $campaignName = "Bulk SMS - " . date('Y-m-d H:i');
            $status = $scheduledAt ? 'scheduled' : 'sending';
            $stmt->execute([$campaignName, $message, $templateId > 0 ? $templateId : null, count($recipientList), $status, $scheduledAt, $_SESSION['admin_id']]);
            $campaignId = $pdo->lastInsertId();
            
            // Add recipients to campaign
            $stmt = $pdo->prepare("INSERT INTO sms_campaign_recipients (campaign_id, member_id, phone_number, status) VALUES (?, ?, ?, 'pending')");
            foreach ($recipientList as $recipient) {
                $stmt->execute([$campaignId, $recipient['id'], $recipient['phone']]);
            }
            
            if ($scheduledAt) {
                $success = "SMS campaign scheduled successfully for " . date('M j, Y g:i A', strtotime($scheduledAt)) . " with " . count($recipientList) . " recipients.";
            } else {
                // Send immediately (in a real implementation, this would be queued)
                $success = "SMS campaign created successfully with " . count($recipientList) . " recipients. Messages are being sent.";
                
                // Update campaign status
                $stmt = $pdo->prepare("UPDATE sms_campaigns SET status = 'completed', sent_at = NOW() WHERE id = ?");
                $stmt->execute([$campaignId]);
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get SMS templates
$smsTemplates = [];
try {
    $stmt = $pdo->prepare("SELECT id, template_name, message_content FROM sms_templates WHERE status = 'active' ORDER BY template_name");
    $stmt->execute();
    $smsTemplates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Templates table might not exist yet
}

// Get contact groups (member groups)
$contactGroups = [];
try {
    $stmt = $pdo->prepare("SELECT cg.id, cg.group_name, COUNT(cgm.member_id) as member_count FROM contact_groups cg LEFT JOIN contact_group_members cgm ON cg.id = cgm.group_id LEFT JOIN members m ON cgm.member_id = m.id WHERE cg.status = 'active' AND (m.phone_number IS NOT NULL AND m.phone_number != '' AND m.status = 'active') GROUP BY cg.id ORDER BY cg.group_name");
    $stmt->execute();
    $contactGroups = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Contact groups might not exist
}

// Get external contact groups (from contacts table)
$externalContactGroups = [];
try {
    $stmt = $pdo->prepare("SELECT cg.id, cg.name as group_name, COUNT(cgm.contact_id) as contact_count FROM contact_groups cg LEFT JOIN contact_group_members cgm ON cg.id = cgm.group_id LEFT JOIN contacts c ON cgm.contact_id = c.id WHERE c.status = 'active' AND (c.phone_number IS NOT NULL AND c.phone_number != '') GROUP BY cg.id HAVING contact_count > 0 ORDER BY cg.name");
    $stmt->execute();
    $externalContactGroups = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // External contact groups might not exist
}

// Get member counts
$memberCounts = [];
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_members FROM members WHERE status = 'active' AND phone_number IS NOT NULL AND phone_number != ''");
    $stmt->execute();
    $memberCounts['total'] = $stmt->fetch()['total_members'];

    $stmt = $pdo->prepare("SELECT COUNT(*) as birthday_today FROM members WHERE status = 'active' AND phone_number IS NOT NULL AND phone_number != '' AND (DATE_FORMAT(date_of_birth, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d') OR DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d'))");
    $stmt->execute();
    $memberCounts['birthday'] = $stmt->fetch()['birthday_today'];
} catch (PDOException $e) {
    $memberCounts = ['total' => 0, 'birthday' => 0];
}

// Get external contact counts
$contactCounts = [];
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_contacts FROM contacts WHERE status = 'active' AND phone_number IS NOT NULL AND phone_number != ''");
    $stmt->execute();
    $contactCounts['total'] = $stmt->fetch()['total_contacts'];
} catch (PDOException $e) {
    $contactCounts['total'] = 0;
}

// Set page variables
$page_title = 'Bulk SMS';
$page_header = 'Bulk SMS';
$page_description = 'Send SMS messages to members and contacts.';

// Include header
include 'includes/header.php';
?>

<style>
.character-counter {
    font-size: 0.875rem;
    color: #6c757d;
}
.character-counter.warning {
    color: #fd7e14;
    font-weight: bold;
}
.character-counter.danger {
    color: #dc3545;
    font-weight: bold;
}
.recipient-info {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 0.5rem;
}
.sms-preview {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
    padding: 1rem;
    border-radius: 0.375rem;
    font-family: monospace;
    white-space: pre-wrap;
}
</style>

<!-- Page Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="bi bi-chat-text-fill me-2"></i><?php echo $page_header; ?>
    </h1>
    <div>
        <a href="sms_campaigns.php" class="btn btn-outline-primary">
            <i class="bi bi-megaphone me-2"></i>View Campaigns
        </a>
        <a href="sms_templates.php" class="btn btn-outline-secondary">
            <i class="bi bi-file-text me-2"></i>Manage Templates
        </a>
        <a href="sms_integration.php" class="btn btn-outline-info">
            <i class="bi bi-gear me-2"></i>SMS Settings
        </a>
    </div>
</div>

<?php if (!$smsEnabled): ?>
    <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <strong>SMS Service Not Configured</strong><br>
        Please configure your SMS settings first to enable SMS functionality.
        <a href="sms_integration.php" class="btn btn-sm btn-outline-warning ms-2">Configure SMS</a>
    </div>
<?php endif; ?>

<?php if (isset($error)): ?>
    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
<?php endif; ?>

<?php if (isset($success)): ?>
    <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
<?php endif; ?>

<?php if (isset($warning)): ?>
    <div class="alert alert-warning"><?php echo htmlspecialchars($warning); ?></div>
<?php endif; ?>

<div class="row">
    <!-- SMS Compose Form -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-pencil-square me-2"></i>Compose SMS
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="smsForm" enctype="multipart/form-data">
                    <!-- Recipients Selection -->
                    <div class="mb-3">
                        <label for="recipients" class="form-label">Recipients</label>
                        <select class="form-select" id="recipients" name="recipients" onchange="updateRecipientInfo()">
                            <option value="">Select Recipients</option>

                            <!-- Organization Members -->
                            <optgroup label="Organization Members">
                                <option value="all_members">All Members (<?php echo number_format($memberCounts['total']); ?> members)</option>
                                <?php if ($memberCounts['birthday'] > 0): ?>
                                    <option value="birthday_today">Birthday Today (<?php echo number_format($memberCounts['birthday']); ?> members)</option>
                                <?php endif; ?>
                                <?php foreach ($contactGroups as $group): ?>
                                    <option value="group_<?php echo $group['id']; ?>">
                                        <?php echo htmlspecialchars($group['group_name']); ?> (<?php echo number_format($group['member_count']); ?> members)
                                    </option>
                                <?php endforeach; ?>
                            </optgroup>

                            <!-- External Contacts -->
                            <?php if ($contactCounts['total'] > 0 || !empty($externalContactGroups)): ?>
                            <optgroup label="External Contacts">
                                <?php if ($contactCounts['total'] > 0): ?>
                                    <option value="all_contacts">All External Contacts (<?php echo number_format($contactCounts['total']); ?> contacts)</option>
                                <?php endif; ?>
                                <?php foreach ($externalContactGroups as $group): ?>
                                    <option value="contact_group_<?php echo $group['id']; ?>">
                                        <?php echo htmlspecialchars($group['group_name']); ?> (<?php echo number_format($group['contact_count']); ?> contacts)
                                    </option>
                                <?php endforeach; ?>
                            </optgroup>
                            <?php endif; ?>
                        </select>
                        <div id="recipientInfo" class="recipient-info" style="display: none;">
                            <small class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                <span id="recipientCount">0</span> recipients will receive this SMS
                            </small>
                        </div>
                    </div>

                    <!-- File Upload Option -->
                    <div class="mb-3">
                        <div class="card border-secondary">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="bi bi-upload me-2"></i>Or Upload Phone Numbers
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="phone_file" class="form-label">Upload Phone Numbers File</label>
                                    <input type="file" class="form-control" id="phone_file" name="phone_file"
                                           accept=".csv,.txt,.xlsx" onchange="handleFileUpload()">
                                    <div class="form-text">
                                        <strong>Supported formats:</strong>
                                        <ul class="mb-0 mt-1">
                                            <li><strong>CSV:</strong> Phone numbers in first column, optional names in second column</li>
                                            <li><strong>TXT:</strong> One phone number per line</li>
                                            <li><strong>XLSX:</strong> Excel files (convert to CSV for best results)</li>
                                        </ul>
                                        <small class="text-muted">Maximum file size: 2MB</small>
                                    </div>
                                </div>
                                <div id="filePreview" class="alert alert-info" style="display: none;">
                                    <i class="bi bi-file-earmark-text me-2"></i>
                                    <span id="fileInfo"></span>
                                </div>
                            </div>
                        </div>
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            You can select recipients from the dropdown above OR upload a file, or use both methods together.
                        </small>
                    </div>

                    <!-- Template Selection -->
                    <?php if (!empty($smsTemplates)): ?>
                        <div class="mb-3">
                            <label for="template_id" class="form-label">Use Template (Optional)</label>
                            <select class="form-select" id="template_id" name="template_id" onchange="loadTemplate()">
                                <option value="">Select a template...</option>
                                <?php foreach ($smsTemplates as $template): ?>
                                    <option value="<?php echo $template['id']; ?>" data-content="<?php echo htmlspecialchars($template['message_content']); ?>">
                                        <?php echo htmlspecialchars($template['template_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    <?php endif; ?>

                    <!-- Message Content -->
                    <div class="mb-3">
                        <label for="message" class="form-label">Message *</label>
                        <textarea class="form-control" id="message" name="message" rows="4" required 
                                  placeholder="Enter your SMS message..." onkeyup="updateCharacterCount()"></textarea>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted">SMS messages are typically limited to 160 characters</small>
                            <span id="characterCount" class="character-counter">0 characters</span>
                        </div>
                    </div>

                    <!-- Scheduling Options -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="scheduleMessage" onchange="toggleScheduling()">
                            <label class="form-check-label" for="scheduleMessage">
                                Schedule for later
                            </label>
                        </div>
                    </div>

                    <div id="schedulingOptions" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="schedule_date" class="form-label">Schedule Date</label>
                                <input type="date" class="form-control" id="schedule_date" name="schedule_date" 
                                       min="<?php echo date('Y-m-d'); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="schedule_time" class="form-label">Schedule Time</label>
                                <input type="time" class="form-control" id="schedule_time" name="schedule_time">
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" name="send_sms" class="btn btn-primary btn-lg" <?php echo !$smsEnabled ? 'disabled' : ''; ?>>
                            <i class="bi bi-send me-2"></i>
                            <span id="submitButtonText">Send SMS Now</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- SMS Preview & Info -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-eye me-2"></i>SMS Preview
                </h5>
            </div>
            <div class="card-body">
                <div class="sms-preview" id="smsPreview">
                    Your SMS message will appear here...
                </div>
                
                <div class="mt-3">
                    <h6>SMS Guidelines:</h6>
                    <ul class="small text-muted">
                        <li>Keep messages under 160 characters for single SMS</li>
                        <li>Longer messages will be split into multiple SMS</li>
                        <li>Include clear call-to-action if needed</li>
                        <li>Avoid special characters that may not display properly</li>
                        <li>Test with a small group first for important campaigns</li>
                    </ul>
                </div>

                <?php if ($smsEnabled): ?>
                    <div class="mt-3 p-2 bg-light rounded">
                        <small class="text-success">
                            <i class="bi bi-check-circle me-1"></i>
                            SMS service is configured and ready
                        </small>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Quick Stats</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary"><?php echo number_format($memberCounts['total']); ?></h4>
                            <small class="text-muted">Members with Phone</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning"><?php echo number_format($memberCounts['birthday']); ?></h4>
                        <small class="text-muted">Birthdays Today</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateCharacterCount() {
    const message = document.getElementById('message').value;
    const count = message.length;
    const counter = document.getElementById('characterCount');
    
    counter.textContent = count + ' characters';
    
    if (count > 160) {
        const smsCount = Math.ceil(count / 160);
        counter.textContent = count + ' characters (' + smsCount + ' SMS)';
        counter.className = 'character-counter danger';
    } else if (count > 140) {
        counter.className = 'character-counter warning';
    } else {
        counter.className = 'character-counter';
    }
    
    // Update preview
    document.getElementById('smsPreview').textContent = message || 'Your SMS message will appear here...';
}

function loadTemplate() {
    const select = document.getElementById('template_id');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value && selectedOption.dataset.content) {
        document.getElementById('message').value = selectedOption.dataset.content;
        updateCharacterCount();
    }
}

function handleFileUpload() {
    const fileInput = document.getElementById('phone_file');
    const filePreview = document.getElementById('filePreview');
    const fileInfo = document.getElementById('fileInfo');
    const recipientsSelect = document.getElementById('recipients');

    if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        const fileName = file.name;
        const fileSize = (file.size / 1024).toFixed(1) + ' KB';
        const fileExtension = fileName.split('.').pop().toLowerCase();

        // Validate file type
        const allowedTypes = ['csv', 'txt', 'xlsx'];
        if (!allowedTypes.includes(fileExtension)) {
            alert('Please upload a CSV, TXT, or XLSX file only.');
            fileInput.value = '';
            filePreview.style.display = 'none';
            return;
        }

        // Validate file size (2MB limit)
        if (file.size > 2 * 1024 * 1024) {
            alert('File size must be less than 2MB.');
            fileInput.value = '';
            filePreview.style.display = 'none';
            return;
        }

        // Show file info
        fileInfo.innerHTML = `<strong>${fileName}</strong> (${fileSize}) ready for upload`;
        filePreview.style.display = 'block';

        // Make recipients dropdown optional when file is uploaded
        recipientsSelect.required = false;

        // Show preview for CSV and TXT files
        if (fileExtension === 'csv' || fileExtension === 'txt') {
            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                const lines = content.split('\n').filter(line => line.trim());
                const phoneCount = lines.length;

                if (phoneCount > 0) {
                    fileInfo.innerHTML += `<br><small class="text-muted">Found approximately ${phoneCount} phone numbers</small>`;
                }
            };
            reader.readAsText(file);
        }
    } else {
        filePreview.style.display = 'none';
        recipientsSelect.required = true;
    }
}

function updateRecipientInfo() {
    const select = document.getElementById('recipients');
    const info = document.getElementById('recipientInfo');
    const countSpan = document.getElementById('recipientCount');

    if (select.value) {
        const selectedText = select.options[select.selectedIndex].text;
        const match = selectedText.match(/\((\d+(?:,\d+)*)\s+(?:members|contacts)\)/);

        if (match) {
            countSpan.textContent = match[1];
            info.style.display = 'block';
        }
    } else {
        info.style.display = 'none';
    }
}

function toggleScheduling() {
    const checkbox = document.getElementById('scheduleMessage');
    const options = document.getElementById('schedulingOptions');
    const submitText = document.getElementById('submitButtonText');
    
    if (checkbox.checked) {
        options.style.display = 'block';
        submitText.textContent = 'Schedule SMS';
    } else {
        options.style.display = 'none';
        submitText.textContent = 'Send SMS Now';
    }
}

// Form validation
function validateForm() {
    const recipients = document.getElementById('recipients').value;
    const fileInput = document.getElementById('phone_file');
    const message = document.getElementById('message').value.trim();

    // Check if either recipients are selected OR file is uploaded
    if (!recipients && fileInput.files.length === 0) {
        alert('Please select recipients from the dropdown or upload a phone numbers file.');
        return false;
    }

    if (!message) {
        alert('Please enter a message.');
        return false;
    }

    return true;
}

// Initialize character count
document.addEventListener('DOMContentLoaded', function() {
    updateCharacterCount();

    // Add form validation
    document.getElementById('smsForm').addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
