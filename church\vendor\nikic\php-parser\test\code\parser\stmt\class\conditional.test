Conditional class definition
-----
<?php

if (true) {
    class A {}
}
-----
array(
    0: Stmt_If(
        cond: Expr_ConstFetch(
            name: Name(
                name: true
            )
        )
        stmts: array(
            0: Stmt_Class(
                attrGroups: array(
                )
                flags: 0
                name: Identifier(
                    name: A
                )
                extends: null
                implements: array(
                )
                stmts: array(
                )
            )
        )
        elseifs: array(
        )
        else: null
    )
)
