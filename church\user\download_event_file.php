<?php
/**
 * Secure Event File Download Handler
 * 
 * Handles secure downloads of event materials for authenticated users
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    http_response_code(403);
    exit('Access denied');
}

$userId = $_SESSION['user_id'];

// Get file ID from URL
$fileId = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
if (!$fileId) {
    http_response_code(400);
    exit('Invalid file ID');
}

try {
    // Get file details and verify it belongs to an active event
    $stmt = $pdo->prepare("
        SELECT ef.*, e.title as event_title, e.is_active
        FROM event_files ef
        JOIN events e ON ef.event_id = e.id
        WHERE ef.id = ? AND e.is_active = 1
    ");
    $stmt->execute([$fileId]);
    $file = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$file) {
        http_response_code(404);
        exit('File not found');
    }
    
    // Check if file exists on disk
    $filePath = '../' . $file['file_path'];
    if (!file_exists($filePath)) {
        http_response_code(404);
        exit('File not found on disk');
    }
    
    // Set appropriate headers for download
    header('Content-Type: ' . $file['file_type']);
    header('Content-Length: ' . $file['file_size']);
    header('Content-Disposition: attachment; filename="' . $file['file_name'] . '"');
    header('Cache-Control: private, must-revalidate');
    header('Pragma: private');
    header('Expires: 0');
    
    // Output file content
    readfile($filePath);
    
    // Log the download for analytics (optional)
    try {
        $stmt = $pdo->prepare("
            INSERT INTO file_downloads (file_id, user_id, download_date, ip_address)
            VALUES (?, ?, NOW(), ?)
        ");
        $stmt->execute([$fileId, $userId, $_SERVER['REMOTE_ADDR']]);
    } catch (PDOException $e) {
        // Log download tracking error but don't fail the download
        error_log("Download tracking error: " . $e->getMessage());
    }
    
} catch (PDOException $e) {
    error_log("File download error: " . $e->getMessage());
    http_response_code(500);
    exit('Database error');
} catch (Exception $e) {
    error_log("File download error: " . $e->getMessage());
    http_response_code(500);
    exit('Server error');
}
?>
