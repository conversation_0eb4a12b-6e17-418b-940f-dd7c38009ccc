First-class callables
-----
<?php
foo(...);
$this->foo(...);
A::foo(...);

// These are invalid, but accepted on the parser level.
new Foo(...);
$this?->foo(...);

#[Foo(...)]
function foo() {}
-----
array(
    0: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: foo
            )
            args: array(
                0: VariadicPlaceholder(
                )
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_MethodCall(
            var: Expr_Variable(
                name: this
            )
            name: Identifier(
                name: foo
            )
            args: array(
                0: VariadicPlaceholder(
                )
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_StaticCall(
            class: Name(
                name: A
            )
            name: Identifier(
                name: foo
            )
            args: array(
                0: VariadicPlaceholder(
                )
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_New(
            class: Name(
                name: Foo
            )
            args: array(
                0: VariadicPlaceholder(
                )
            )
        )
        comments: array(
            0: // These are invalid, but accepted on the parser level.
        )
    )
    4: Stmt_Expression(
        expr: Expr_NullsafeMethodCall(
            var: Expr_Variable(
                name: this
            )
            name: Identifier(
                name: foo
            )
            args: array(
                0: VariadicPlaceholder(
                )
            )
        )
    )
    5: Stmt_Function(
        attrGroups: array(
            0: AttributeGroup(
                attrs: array(
                    0: Attribute(
                        name: Name(
                            name: Foo
                        )
                        args: array(
                            0: VariadicPlaceholder(
                            )
                        )
                    )
                )
            )
        )
        byRef: false
        name: Identifier(
            name: foo
        )
        params: array(
        )
        returnType: null
        stmts: array(
        )
    )
)
