<?php
/**
 * Automatic Notification System
 * 
 * This file contains functions to automatically create notifications
 * when users perform specific actions in the system
 */

require_once __DIR__ . '/notification_functions.php';

/**
 * Send notification when a user posts a new skill
 * 
 * @param PDO $pdo Database connection
 * @param int $userId User who posted the skill
 * @param string $skillName Name of the skill
 * @param string $skillCategory Category of the skill
 * @param string $proficiencyLevel User's proficiency level
 * @return bool Success status
 */
function notifyNewSkillPosted($pdo, $userId, $skillName, $skillCategory, $proficiencyLevel) {
    try {
        // Get user information
        $stmt = $pdo->prepare("SELECT full_name, first_name, last_name FROM members WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) return false;
        
        $userName = $user['full_name'] ?: ($user['first_name'] . ' ' . $user['last_name']);
        
        // Create notification content
        $title = "New Skill Posted: $skillName";
        $message = "$userName has added a new skill to their profile: $skillName ($skillCategory). Their proficiency level is $proficiencyLevel. Connect with them if you need help with this skill or want to collaborate!";
        $actionUrl = "profile.php?id=$userId#skills";
        
        // Get all active members except the user who posted
        $stmt = $pdo->prepare("
            SELECT id FROM members 
            WHERE (status = 'active' OR is_active = 1) 
            AND id != ?
        ");
        $stmt->execute([$userId]);
        $recipients = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Filter recipients based on notification preferences
        $filteredRecipients = filterRecipientsByPreferences($pdo, $recipients, 'community');
        
        // Send bulk notification
        if (!empty($filteredRecipients)) {
            $result = sendBulkNotification(
                $pdo,
                $filteredRecipients,
                $title,
                $message,
                'community',
                $userId,
                'member',
                $actionUrl,
                'normal'
            );
            
            return $result['success_count'] > 0;
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error sending skill notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Send notification when a user creates a new request
 * 
 * @param PDO $pdo Database connection
 * @param int $userId User who created the request
 * @param int $requestId ID of the created request
 * @param string $title Request title
 * @param string $category Request category
 * @param string $privacyLevel Privacy level of the request
 * @param bool $isUrgent Whether the request is urgent
 * @return bool Success status
 */
function notifyNewRequestCreated($pdo, $userId, $requestId, $title, $category, $privacyLevel, $isUrgent = false) {
    try {
        // Only notify for public and members requests
        if ($privacyLevel === 'private') {
            return true; // Don't notify for private requests
        }
        
        // Get user information
        $stmt = $pdo->prepare("SELECT full_name, first_name, last_name, is_anonymous FROM members WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) return false;
        
        $userName = $user['full_name'] ?: ($user['first_name'] . ' ' . $user['last_name']);
        
        // Create notification content
        $urgentText = $isUrgent ? " (URGENT)" : "";
        $notificationTitle = "New Request Posted$urgentText";
        $message = "$userName has posted a new request in the $category category: \"$title\". Your prayers and support would be appreciated.";
        $actionUrl = "requests.php#request-$requestId";
        
        // Determine recipients based on privacy level
        $whereClause = "(status = 'active' OR is_active = 1) AND id != ?";
        if ($privacyLevel === 'members') {
            // Only church members
            $whereClause .= " AND (role = 'member' OR role IS NULL)";
        }
        
        $stmt = $pdo->prepare("SELECT id FROM members WHERE $whereClause");
        $stmt->execute([$userId]);
        $recipients = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Filter recipients based on notification preferences
        $filteredRecipients = filterRecipientsByPreferences($pdo, $recipients, 'community');
        
        // Send bulk notification
        if (!empty($filteredRecipients)) {
            $priority = $isUrgent ? 'high' : 'normal';
            $result = sendBulkNotification(
                $pdo,
                $filteredRecipients,
                $notificationTitle,
                $message,
                'community',
                $userId,
                'member',
                $actionUrl,
                $priority
            );
            
            return $result['success_count'] > 0;
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error sending request notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Send notification when a user comments on a request
 * 
 * @param PDO $pdo Database connection
 * @param int $commenterId User who posted the comment
 * @param int $requestId ID of the request
 * @param int $requestOwnerId Owner of the request
 * @param string $requestTitle Title of the request
 * @param string $comment Comment text
 * @return bool Success status
 */
function notifyNewRequestComment($pdo, $commenterId, $requestId, $requestOwnerId, $requestTitle, $comment) {
    try {
        // Don't notify if user is commenting on their own request
        if ($commenterId === $requestOwnerId) {
            return true;
        }
        
        // Get commenter information
        $stmt = $pdo->prepare("SELECT full_name, first_name, last_name FROM members WHERE id = ?");
        $stmt->execute([$commenterId]);
        $commenter = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$commenter) return false;
        
        $commenterName = $commenter['full_name'] ?: ($commenter['first_name'] . ' ' . $commenter['last_name']);
        
        // Create notification content
        $title = "New Comment on Your Request";
        $commentPreview = strlen($comment) > 100 ? substr($comment, 0, 100) . '...' : $comment;
        $message = "$commenterName commented on your request \"$requestTitle\": \"$commentPreview\"";
        $actionUrl = "requests.php#request-$requestId";
        
        // Send notification to request owner
        $success = createNotification(
            $pdo,
            $requestOwnerId,
            $title,
            $message,
            'message',
            $commenterId,
            'member',
            $actionUrl,
            'normal'
        );
        
        return $success;
        
    } catch (Exception $e) {
        error_log("Error sending comment notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Send notification when a user updates their profile significantly
 * 
 * @param PDO $pdo Database connection
 * @param int $userId User who updated their profile
 * @param array $changes Array of changes made
 * @return bool Success status
 */
function notifyProfileUpdate($pdo, $userId, $changes) {
    try {
        // Only notify for significant changes
        $significantFields = ['full_name', 'first_name', 'last_name', 'ministry', 'bio', 'profile_picture'];
        $significantChanges = array_intersect_key($changes, array_flip($significantFields));
        
        if (empty($significantChanges)) {
            return true; // No significant changes
        }
        
        // Get user information
        $stmt = $pdo->prepare("SELECT full_name, first_name, last_name FROM members WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) return false;
        
        $userName = $user['full_name'] ?: ($user['first_name'] . ' ' . $user['last_name']);
        
        // Create notification content
        $title = "Profile Updated: $userName";
        $changesList = [];
        foreach ($significantChanges as $field => $value) {
            $fieldName = ucwords(str_replace('_', ' ', $field));
            $changesList[] = $fieldName;
        }
        $changesText = implode(', ', $changesList);
        $message = "$userName has updated their profile. Changes include: $changesText. Check out their updated profile!";
        $actionUrl = "profile.php?id=$userId";
        
        // Get all active members except the user who updated
        $stmt = $pdo->prepare("
            SELECT id FROM members 
            WHERE (status = 'active' OR is_active = 1) 
            AND id != ?
        ");
        $stmt->execute([$userId]);
        $recipients = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Filter recipients based on notification preferences
        $filteredRecipients = filterRecipientsByPreferences($pdo, $recipients, 'community');
        
        // Send bulk notification (only to a subset to avoid spam)
        if (!empty($filteredRecipients)) {
            // Limit to 20 random recipients to avoid notification spam
            $limitedRecipients = array_slice($filteredRecipients, 0, 20);
            
            $result = sendBulkNotification(
                $pdo,
                $limitedRecipients,
                $title,
                $message,
                'community',
                $userId,
                'member',
                $actionUrl,
                'low'
            );
            
            return $result['success_count'] > 0;
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error sending profile update notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Filter recipients based on their notification preferences
 * 
 * @param PDO $pdo Database connection
 * @param array $recipients Array of user IDs
 * @param string $notificationType Type of notification
 * @return array Filtered array of user IDs
 */
function filterRecipientsByPreferences($pdo, $recipients, $notificationType) {
    try {
        if (empty($recipients)) return [];
        
        $placeholders = str_repeat('?,', count($recipients) - 1) . '?';
        $stmt = $pdo->prepare("
            SELECT user_id 
            FROM notification_preferences 
            WHERE user_id IN ($placeholders) 
            AND notification_type = ? 
            AND web_enabled = 1
        ");
        
        $params = array_merge($recipients, [$notificationType]);
        $stmt->execute($params);
        $enabledUsers = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // If no preferences set, assume all users want notifications
        if (empty($enabledUsers)) {
            return $recipients;
        }
        
        return $enabledUsers;
        
    } catch (Exception $e) {
        error_log("Error filtering recipients by preferences: " . $e->getMessage());
        return $recipients; // Return all if error
    }
}

/**
 * Add community notification type to existing users' preferences
 * 
 * @param PDO $pdo Database connection
 * @return bool Success status
 */
function addCommunityNotificationPreferences($pdo) {
    try {
        // Add community notification type to notification preferences for all users
        $pdo->exec("
            INSERT IGNORE INTO notification_preferences (user_id, notification_type, email_enabled, web_enabled, sms_enabled)
            SELECT m.id, 'community', 1, 1, 0 FROM members m
        ");
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error adding community notification preferences: " . $e->getMessage());
        return false;
    }
}
?>
