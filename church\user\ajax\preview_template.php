<?php
/**
 * Template Preview AJAX Endpoint
 * 
 * Returns HTML preview of a birthday template
 */

// Include configuration
require_once '../../config.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../../classes/SecurityManager.php';
require_once '../../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    http_response_code(401);
    echo 'Unauthorized';
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    http_response_code(401);
    echo 'Unauthorized';
    exit();
}

// Get template ID from request
$templateId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$templateId) {
    http_response_code(400);
    echo 'Invalid template ID';
    exit();
}

try {
    // Get template from admin-created email templates
    $stmt = $pdo->prepare("
        SELECT
            id,
            template_name as name,
            subject as description,
            content as template_content,
            template_category as category
        FROM email_templates
        WHERE id = ? AND is_birthday_template = 1
    ");
    $stmt->execute([$templateId]);
    $template = $stmt->fetch();
    
    if (!$template) {
        http_response_code(404);
        echo 'Template not found';
        exit();
    }
    
    // Get site settings for branding
    $sitename = get_organization_name();
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
    $stmt->execute();
    $siteNameResult = $stmt->fetch();
    if ($siteNameResult) {
        $sitename = $siteNameResult['setting_value'];
    }
    
    // Create preview content with sample data
    $previewContent = $template['template_content'];

    // Replace both old and new placeholder formats
    $replacements = [
        // New format (curly braces)
        '{full_name}' => 'John Doe',
        '{first_name}' => 'John',
        '{last_name}' => 'Doe',
        '{organization_name}' => $sitename,
        '{organization_type}' => 'church',
        '{sender_name}' => $userData['first_name'],
        '{sender_full_name}' => $userData['first_name'] . ' ' . ($userData['last_name'] ?? ''),

        // Legacy format (double curly braces)
        '{{recipient_name}}' => 'John Doe',
        '{{sender_name}}' => $userData['first_name'],
        '{{church_name}}' => $sitename,

        // Old format (square brackets)
        '[name]' => 'John Doe',
        '[sender]' => $userData['first_name'],
        '[church]' => $sitename
    ];

    foreach ($replacements as $placeholder => $value) {
        $previewContent = str_replace($placeholder, $value, $previewContent);
    }
    
    // Output the preview
    echo '<div class="template-preview-header mb-3">';
    echo '<h6>' . htmlspecialchars($template['name']) . '</h6>';
    if ($template['description']) {
        echo '<p class="text-muted small">' . htmlspecialchars($template['description']) . '</p>';
    }
    echo '<span class="badge bg-secondary">' . ucfirst($template['category']) . '</span>';
    echo '</div>';
    
    echo '<div class="template-preview-content">';
    echo $previewContent;
    echo '</div>';
    
} catch (Exception $e) {
    http_response_code(500);
    echo 'Error loading template preview';
    error_log('Template preview error: ' . $e->getMessage());
}
?>
