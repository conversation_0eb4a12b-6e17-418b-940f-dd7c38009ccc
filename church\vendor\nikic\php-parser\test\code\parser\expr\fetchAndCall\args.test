Arguments
-----
<?php

f();
f($a);
f($a, $b);
f(&$a);
f($a, ...$b);
-----
array(
    0: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: f
            )
            args: array(
            )
        )
    )
    1: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: f
            )
            args: array(
                0: Arg(
                    name: null
                    value: Expr_Variable(
                        name: a
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    2: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: f
            )
            args: array(
                0: Arg(
                    name: null
                    value: Expr_Variable(
                        name: a
                    )
                    byRef: false
                    unpack: false
                )
                1: Arg(
                    name: null
                    value: Expr_Variable(
                        name: b
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
    )
    3: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: f
            )
            args: array(
                0: Arg(
                    name: null
                    value: Expr_Variable(
                        name: a
                    )
                    byRef: true
                    unpack: false
                )
            )
        )
    )
    4: Stmt_Expression(
        expr: Expr_FuncCall(
            name: Name(
                name: f
            )
            args: array(
                0: Arg(
                    name: null
                    value: Expr_Variable(
                        name: a
                    )
                    byRef: false
                    unpack: false
                )
                1: Arg(
                    name: null
                    value: Expr_Variable(
                        name: b
                    )
                    byRef: false
                    unpack: true
                )
            )
        )
    )
)
